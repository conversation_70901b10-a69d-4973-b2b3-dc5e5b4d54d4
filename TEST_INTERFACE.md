# 🧪 界面测试指南

## 修复内容
已经修复了快速收藏窗口的显示问题：

### ✅ 修复的问题：
1. **窗口尺寸**：调整为 420x400px，确保所有内容可见
2. **布局优化**：减少间距，优化空间利用
3. **按钮区域**：确保保存按钮始终可见
4. **滚动支持**：内容区域支持滚动（如果需要）

## 🔍 测试步骤

### 1. 测试快速收藏窗口
```bash
# 应用已启动，现在按快捷键
Cmd + N  # (macOS) 或 Ctrl + N (Windows/Linux)
```

**预期结果：**
- ✅ 弹出一个居中的快速收藏窗口
- ✅ 窗口包含：标题栏、输入区域、按钮区域、底部信息
- ✅ 所有按钮都可见，包括"保存"按钮
- ✅ 窗口大小合适，不会被截断

### 2. 测试界面元素
在快速收藏窗口中：

1. **输入测试**：
   - 在"内容"文本框输入一些文字
   - 选择不同的"类型"选项
   - 添加"来源"信息

2. **按钮测试**：
   - 点击"粘贴"按钮
   - 点击"清除"按钮
   - 点击"💾 保存"按钮

3. **快捷键测试**：
   - `Cmd/Ctrl + V` 粘贴
   - `Enter` 保存
   - `Esc` 关闭窗口

### 3. 如果界面还有问题

#### 方法A：手动测试HTML
在浏览器中打开测试文件：
```bash
open test-quick-capture.html
```

#### 方法B：检查控制台
如果窗口仍然有问题：
1. 在应用中按 `Cmd + Shift + I` 打开开发者工具
2. 查看 Console 标签页的错误信息
3. 告诉我具体的错误信息

## 🎯 完整测试流程

### 步骤 1：基本功能测试
```
1. 复制这段文字："QNotes测试内容 - 这是一个智能收藏工具"
2. 按 Cmd + N
3. 窗口弹出后，点击"粘贴"
4. 点击"💾 保存"
5. 查看是否成功保存
```

### 步骤 2：切换模式测试
```
1. 按 Cmd + 2 切换到整理模式
2. 查看左侧是否显示刚才保存的内容
3. 按 Cmd + 1 切换回收集模式
```

### 步骤 3：托盘菜单测试
```
1. 右键点击系统托盘的 QNotes 图标
2. 查看菜单是否显示收藏计数
3. 尝试通过菜单切换模式
```

## ❓ 常见问题排查

### Q: 快速收藏窗口仍然看不到保存按钮
**解决方案：**
1. 检查屏幕分辨率是否太小
2. 尝试按 `Esc` 关闭窗口，重新按 `Cmd + N`
3. 重启应用：关闭后重新运行 `npm run dev`

### Q: 按 Cmd + N 没有反应
**可能原因：**
1. 应用没有获得必要的系统权限
2. 快捷键被其他应用占用
3. 应用没有正确启动

**解决方案：**
1. 尝试右键托盘图标 → "快速收藏"
2. 在 macOS 系统偏好设置中给应用辅助功能权限

### Q: 窗口显示但是空白
**解决方案：**
1. 检查 Console 错误信息
2. 确保 `src/ui/quick-capture.html` 文件存在
3. 重新安装依赖：`npm install`

## 📞 反馈问题

如果仍有问题，请告诉我：
1. **具体现象**：看到了什么/没看到什么
2. **错误信息**：Console 中的任何错误
3. **操作步骤**：你是怎么操作的
4. **系统信息**：macOS/Windows/Linux，屏幕分辨率

---

## 🎉 测试成功的话

如果界面显示正常，你就可以开始正常使用QNotes了！

**记住核心操作：**
```
复制内容 → Cmd + N → 粘贴 → 保存 ✅
```