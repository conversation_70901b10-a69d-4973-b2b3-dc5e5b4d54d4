const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  testDatabase: () => ipcRenderer.invoke('test-database'),
  testMaterialsQuery: () => ipcRenderer.invoke('test-materials-query'),
  createTestData: () => ipcRenderer.invoke('create-test-data'),
  testIPC: () => ipcRenderer.invoke('test-ipc'),
  testFullFlow: () => ipcRenderer.invoke('test-full-flow'),
  getCollections: (options) => ipcRenderer.invoke('get-collections', options)
}); 