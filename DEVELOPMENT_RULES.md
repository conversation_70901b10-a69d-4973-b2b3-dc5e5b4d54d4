# 开发规则与协作指南

## 🚀 开发效率原则

### 1. 测试策略
- **❌ 禁止 AI 自动测试**：不使用 npm run dev、启动应用等测试命令
- **✅ 人工测试优先**：用户进行实际功能测试，提供具体反馈
- **✅ 代码审查**：AI 专注于代码逻辑检查和静态分析
- **✅ 问题导向**：基于用户反馈的具体问题进行修复

### 2. 反馈机制
- **具体描述问题**：说明什么功能不工作、具体现象
- **操作步骤**：提供重现问题的具体步骤
- **预期 vs 实际**：说明期望结果和实际结果的差异
- **错误信息**：如有控制台错误，提供错误信息

### 3. 修复流程
```
用户发现问题 → 描述具体现象 → AI分析代码 → 定位问题 → 修复代码 → 用户验证
```

## 🔧 代码开发规范

### 1. 代码质量
- **模块化设计**：每个功能独立模块
- **错误处理**：完善的 try-catch 和错误提示
- **日志记录**：关键操作记录控制台日志
- **注释规范**：核心逻辑添加中文注释

### 2. 功能实现
- **渐进增强**：先实现核心功能，再添加高级特性
- **兼容性考虑**：支持主流操作系统和浏览器
- **性能优化**：避免不必要的计算和内存占用
- **用户体验**：界面响应快速，操作简单直观

### 3. 文件组织
```
src/
├── controllers/     # 核心控制器
├── modes/          # 模式实现（收集/整理）
├── services/       # 服务层（AI、设置等）
├── database/       # 数据库操作
├── ui/            # 界面文件
│   ├── *.html     # HTML 模板
│   ├── styles/    # CSS 样式
│   └── scripts/   # 前端脚本
└── utils/         # 工具类
```

## 🎯 协作方式

### 1. 问题报告格式
```
问题：[简短描述]
操作：[具体步骤]
现象：[实际结果]
期望：[预期结果]
错误：[控制台错误信息（如有）]
```

### 2. 功能需求格式
```
功能：[功能名称]
场景：[使用场景]
输入：[用户输入]
输出：[预期输出]
优先级：[高/中/低]
```

### 3. 修复确认
- 用户测试修复后的功能
- 确认问题是否解决
- 反馈任何新发现的问题
- 继续下一个问题的修复

## 📋 常见问题模式

### 1. 界面问题
- 元素不可见、位置错误
- 按钮不工作、样式异常
- 窗口大小、布局问题

### 2. 功能问题
- 保存失败、数据丢失
- 快捷键无效、事件不触发
- AI 服务连接、分析失败

### 3. 性能问题
- 启动缓慢、响应延迟
- 内存占用、CPU 使用率高
- 数据库查询、文件操作慢

## 🛠 开发工具与环境

### 1. 必需工具
- Node.js 16+
- Electron 27+
- SQLite 5+
- VS Code 或其他编辑器

### 2. 调试方法
- 控制台日志：`console.log()` 
- 开发者工具：`Cmd + Shift + I`
- 数据库检查：SQLite 浏览器
- 网络请求：开发者工具 Network 标签

### 3. 代码检查
```bash
# 语法检查（如果需要）
npm run lint

# 依赖检查
npm audit

# 构建检查
npm run build
```

## 🚨 禁止事项

### 1. 测试相关
- ❌ 不运行 `npm run dev` 或类似命令
- ❌ 不启动应用进行自动测试
- ❌ 不假设功能工作状态

### 2. 开发相关
- ❌ 不直接修改用户数据
- ❌ 不添加未经要求的功能
- ❌ 不删除现有工作功能

### 3. 文档相关
- ❌ 不创建过多文档文件
- ❌ 不重复已有信息
- ❌ 不写无用的示例代码

## 📈 效率提升

### 1. 快速定位问题
- 根据用户描述直接检查相关代码
- 使用 Grep 搜索关键字和函数
- 检查最近修改的文件

### 2. 批量修复
- 一次解决多个相关问题
- 统一修改类似的模式
- 避免反复修改同一文件

### 3. 优先级管理
- 先修复影响核心功能的问题
- 再处理界面和体验问题
- 最后优化性能和增强功能

---

## 🎯 目标：高效协作，快速迭代

遵循这些规则可以：
- ⚡ 减少不必要的测试时间
- 🎯 专注于实际问题解决
- 🔄 快速迭代和修复
- 📈 提高开发效率

记住：**用户测试 + AI修复 = 最高效的开发模式**