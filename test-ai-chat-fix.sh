#!/bin/bash

echo "🤖 AI对话功能修复测试"
echo "========================"

# 1. 测试API连接
echo "1. 测试API连接..."
response=$(curl -s -X POST https://api.tu-zi.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-o2VwufJTd4Un6aUgTfSwON547FA1Ztz3upNEmepySuPPRgI2" \
  -d '{
    "model": "claude-sonnet-4-20250514",
    "messages": [{"role": "user", "content": "Hello, this is a connection test."}],
    "max_tokens": 100
  }')

if echo "$response" | grep -q "choices"; then
    echo "✅ API连接成功"
    echo "响应: $(echo "$response" | jq -r '.choices[0].message.content' 2>/dev/null || echo "解析失败")"
else
    echo "❌ API连接失败"
    echo "响应: $response"
fi

echo ""

# 2. 检查配置文件
echo "2. 检查AI配置..."
if [ -f "src/services/Settings.js" ]; then
    if grep -q "enabled: true" src/services/Settings.js && grep -q "sk-o2VwufJTd4Un6aUgTfSwON547FA1Ztz3upNEmepySuPPRgI2" src/services/Settings.js; then
        echo "✅ AI配置正确"
    else
        echo "❌ AI配置有问题"
    fi
else
    echo "❌ 配置文件不存在"
fi

echo ""

# 3. 检查AI服务文件
echo "3. 检查AI服务实现..."
if [ -f "src/services/AIService.js" ]; then
    if grep -q "async chat" src/services/AIService.js; then
        echo "✅ AI服务chat方法存在"
    else
        echo "❌ AI服务chat方法不存在"
    fi
    
    if grep -q "stream.*true" src/services/AIService.js; then
        echo "✅ 流式处理支持存在"
    else
        echo "❌ 流式处理支持不存在"
    fi
else
    echo "❌ AI服务文件不存在"
fi

echo ""

# 4. 检查前端AI管理器
echo "4. 检查前端AI管理器..."
if [ -f "src/ui/scripts/ai-manager.js" ]; then
    if grep -q "ai-stream-data" src/ui/scripts/ai-manager.js; then
        echo "✅ 流式事件监听存在"
    else
        echo "❌ 流式事件监听不存在"
    fi
    
    if grep -q "sendAIMessage" src/ui/scripts/ai-manager.js; then
        echo "✅ 发送消息方法存在"
    else
        echo "❌ 发送消息方法不存在"
    fi
else
    echo "❌ AI管理器文件不存在"
fi

echo ""

# 5. 检查IPC处理
echo "5. 检查IPC处理..."
if [ -f "src/modes/OrganizationMode.js" ]; then
    if grep -q "ai-chat-stream" src/modes/OrganizationMode.js; then
        echo "✅ AI聊天流式IPC处理存在"
    else
        echo "❌ AI聊天流式IPC处理不存在"
    fi
else
    echo "❌ OrganizationMode文件不存在"
fi

echo ""

# 6. 检查应用是否运行
echo "6. 检查应用运行状态..."
if pgrep -f "electron.*qnotes" > /dev/null; then
    echo "✅ 应用正在运行"
    echo "进程信息:"
    ps aux | grep electron | grep qnotes | head -1
else
    echo "❌ 应用未运行"
fi

echo ""

# 7. 总结
echo "🔍 修复总结:"
echo "- 已配置API Key: sk-o2VwufJTd4Un6aUgTfSwON547FA1Ztz3upNEmepySuPPRgI2"
echo "- 已启用AI服务: enabled: true"
echo "- 已修复流式事件监听: ai-stream-data, ai-stream-end, ai-stream-error"
echo "- 已修复AI服务chat方法的错误处理"
echo "- 已修复前端AI管理器的事件绑定"

echo ""
echo "✨ 修复完成！现在可以在应用中测试AI对话功能了。"
echo "如果仍有问题，请检查控制台日志获取更多信息。" 