<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单素材测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .material-item { 
            border: 1px solid #ccc; 
            padding: 10px; 
            margin: 10px 0; 
            background: #f9f9f9;
        }
        .delete-btn { 
            background: #ff4444; 
            color: white; 
            border: none; 
            padding: 5px 10px;
            cursor: pointer;
            margin: 5px;
        }
        .folder-item { 
            border: 2px solid #007acc; 
            padding: 15px; 
            margin: 15px 0; 
            background: #e6f3ff;
        }
        .folder-header { font-weight: bold; margin-bottom: 10px; }
        .folder-content { margin-left: 20px; }
        .empty-state { 
            text-align: center; 
            padding: 50px; 
            color: #666; 
            border: 2px dashed #ccc;
        }
        #debug { 
            background: #f0f0f0; 
            padding: 10px; 
            margin: 20px 0; 
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>简单素材删除测试</h1>
    
    <button onclick="testManager.loadMaterials()">重新加载</button>
    <button onclick="testManager.clearDebug()">清除调试</button>
    
    <div id="debug">调试信息将显示在这里...</div>
    
    <div id="materialsList">初始化中...</div>
    
    <script>
        // 模拟核心工具
        const CoreUtils = {
            showElementLoading: function(elementId) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.innerHTML = '<div style="text-align:center;padding:20px;">加载中...</div>';
                }
            },
            hideElementLoading: function(elementId) {
                // 注意：这个方法会清空元素内容！
                const element = document.getElementById(elementId);
                if (element) {
                    element.innerHTML = '';
                }
            }
        };
        
        // 简化的MaterialsManager
        class SimpleMaterialsManager {
            constructor() {
                this.materials = [];
                this.searchQuery = '';
                this.selectedMaterials = new Set();
                this.openFolders = new Set(['测试文件夹']);
                
                this.updateDebug('初始化完成');
                this.loadMaterials();
            }
            
            updateDebug(message) {
                const debug = document.getElementById('debug');
                const timestamp = new Date().toLocaleTimeString();
                debug.textContent += `[${timestamp}] ${message}\\n`;
                debug.scrollTop = debug.scrollHeight;
                console.log(message);
            }
            
            clearDebug() {
                document.getElementById('debug').textContent = '';
            }
            
            getMockMaterials() {
                return [
                    {
                        id: 'mock-1',
                        content: '这是第一个测试素材的内容',
                        type: 'text',
                        category: '测试',
                        folder: 'root',
                        created_at: new Date().toISOString(),
                        keywords: 'test'
                    },
                    {
                        id: 'mock-2',
                        content: '这是第二个测试素材的内容',
                        type: 'text',
                        category: '测试',
                        folder: 'root',
                        created_at: new Date().toISOString(),
                        keywords: 'test'
                    },
                    {
                        id: 'mock-3',
                        content: '这是文件夹中的素材',
                        type: 'text',
                        category: '测试',
                        folder: '测试文件夹',
                        created_at: new Date().toISOString(),
                        keywords: 'test'
                    },
                    {
                        id: 'folder-config-1',
                        content: '# 测试文件夹\\n\\n这是一个文件夹配置文件，请勿删除。',
                        type: 'text',
                        category: '系统',
                        folder: '测试文件夹',
                        created_at: new Date().toISOString(),
                        keywords: '.folder_config'
                    }
                ];
            }
            
            async loadMaterials() {
                try {
                    this.updateDebug('开始加载素材...');
                    CoreUtils.showElementLoading('materialsList');
                    
                    // 模拟异步加载
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    this.materials = this.getMockMaterials();
                    this.updateDebug(`加载完成，素材数量: ${this.materials.length}`);
                    
                    // 先清除加载状态，再渲染
                    CoreUtils.hideElementLoading('materialsList');
                    this.renderMaterials();
                    
                } catch (error) {
                    this.updateDebug(`加载失败: ${error.message}`);
                    this.materials = [];
                    CoreUtils.hideElementLoading('materialsList');
                    this.renderMaterials();
                }
            }
            
            groupMaterialsByFolder() {
                const grouped = { root: [], folders: {} };
                
                this.materials.forEach(material => {
                    // 跳过文件夹配置文件
                    if (material.keywords && material.keywords.includes('.folder_config')) {
                        return;
                    }
                    
                    const folder = material.folder || 'root';
                    if (folder === 'root') {
                        grouped.root.push(material);
                    } else {
                        if (!grouped.folders[folder]) {
                            grouped.folders[folder] = [];
                        }
                        grouped.folders[folder].push(material);
                    }
                });
                
                this.updateDebug(`分组结果: root=${grouped.root.length}, folders=${Object.keys(grouped.folders).length}`);
                return grouped;
            }
            
            renderMaterials() {
                this.updateDebug(`开始渲染，总素材数: ${this.materials.length}`);
                
                const container = document.getElementById('materialsList');
                
                if (!this.materials || this.materials.length === 0) {
                    container.innerHTML = '<div class="empty-state"><h3>暂无素材</h3></div>';
                    this.updateDebug('显示空状态');
                    return;
                }
                
                const groupedMaterials = this.groupMaterialsByFolder();
                let html = '';
                
                // 渲染文件夹
                Object.keys(groupedMaterials.folders).forEach(folderName => {
                    const materials = groupedMaterials.folders[folderName];
                    html += `
                        <div class="folder-item">
                            <div class="folder-header">📁 ${folderName} (${materials.length})</div>
                            <div class="folder-content">
                                ${materials.map(m => this.renderMaterialItem(m)).join('')}
                            </div>
                        </div>
                    `;
                });
                
                // 渲染根目录素材
                if (groupedMaterials.root.length > 0) {
                    html += '<div class="folder-header">📄 根目录</div>';
                    html += groupedMaterials.root.map(m => this.renderMaterialItem(m)).join('');
                }
                
                container.innerHTML = html;
                this.updateDebug('渲染完成');
            }
            
            renderMaterialItem(material) {
                return `
                    <div class="material-item" data-id="${material.id}">
                        <div><strong>ID:</strong> ${material.id}</div>
                        <div><strong>内容:</strong> ${material.content.substring(0, 50)}...</div>
                        <div><strong>文件夹:</strong> ${material.folder || 'root'}</div>
                        <button class="delete-btn" onclick="testManager.deleteMaterial('${material.id}')">删除</button>
                    </div>
                `;
            }
            
            deleteMaterial(id) {
                this.updateDebug(`开始删除素材: ${id}`);
                
                const confirmed = confirm('确定要删除这个素材吗？');
                if (!confirmed) {
                    this.updateDebug('用户取消删除');
                    return;
                }
                
                const originalLength = this.materials.length;
                this.updateDebug(`删除前素材列表: ${this.materials.map(m => m.id).join(', ')}`);
                
                this.materials = this.materials.filter(material => material.id !== id);
                
                this.updateDebug(`删除后素材数量: ${originalLength} -> ${this.materials.length}`);
                this.updateDebug(`删除后素材列表: ${this.materials.map(m => m.id).join(', ')}`);
                
                this.renderMaterials();
                this.updateDebug('删除操作完成');
            }
        }
        
        // 初始化测试管理器
        const testManager = new SimpleMaterialsManager();
    </script>
</body>
</html>
