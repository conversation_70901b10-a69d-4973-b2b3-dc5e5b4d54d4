#!/usr/bin/env node

const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const Database = require('./src/database/Database');

// 防止多实例
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}

let mainWindow;
let database;

async function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'debug-preload.js')
    }
  });

  // 加载测试页面
  const testHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>素材加载调试</title>
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; }
      </style>
    </head>
    <body>
      <h1>QNotes 素材加载调试工具</h1>
      
      <div class="test-section">
        <h2>1. 数据库连接测试</h2>
        <button onclick="testDatabase()">测试数据库连接</button>
        <div id="database-result"></div>
      </div>
      
      <div class="test-section">
        <h2>2. 素材数据查询测试</h2>
        <button onclick="testMaterialsQuery()">查询素材数据</button>
        <div id="materials-result"></div>
      </div>
      
      <div class="test-section">
        <h2>3. 创建测试数据</h2>
        <button onclick="createTestData()">创建测试素材</button>
        <div id="create-result"></div>
      </div>
      
      <div class="test-section">
        <h2>4. IPC通信测试</h2>
        <button onclick="testIPC()">测试IPC通信</button>
        <div id="ipc-result"></div>
      </div>
      
      <div class="test-section">
        <h2>5. 完整加载流程测试</h2>
        <button onclick="testFullFlow()">测试完整流程</button>
        <div id="flow-result"></div>
      </div>
      
      <script>
        async function testDatabase() {
          const result = document.getElementById('database-result');
          result.innerHTML = '<div class="info">测试中...</div>';
          
          try {
            const response = await window.electronAPI.testDatabase();
            result.innerHTML = '<div class="success">数据库连接成功</div><pre>' + JSON.stringify(response, null, 2) + '</pre>';
          } catch (error) {
            result.innerHTML = '<div class="error">数据库连接失败: ' + error.message + '</div>';
          }
        }
        
        async function testMaterialsQuery() {
          const result = document.getElementById('materials-result');
          result.innerHTML = '<div class="info">查询中...</div>';
          
          try {
            const response = await window.electronAPI.testMaterialsQuery();
            result.innerHTML = '<div class="success">查询成功，找到 ' + response.length + ' 个素材</div><pre>' + JSON.stringify(response, null, 2) + '</pre>';
          } catch (error) {
            result.innerHTML = '<div class="error">查询失败: ' + error.message + '</div>';
          }
        }
        
        async function createTestData() {
          const result = document.getElementById('create-result');
          result.innerHTML = '<div class="info">创建中...</div>';
          
          try {
            const response = await window.electronAPI.createTestData();
            result.innerHTML = '<div class="success">测试数据创建成功</div><pre>' + JSON.stringify(response, null, 2) + '</pre>';
          } catch (error) {
            result.innerHTML = '<div class="error">创建失败: ' + error.message + '</div>';
          }
        }
        
        async function testIPC() {
          const result = document.getElementById('ipc-result');
          result.innerHTML = '<div class="info">测试中...</div>';
          
          try {
            const response = await window.electronAPI.testIPC();
            result.innerHTML = '<div class="success">IPC通信正常</div><pre>' + JSON.stringify(response, null, 2) + '</pre>';
          } catch (error) {
            result.innerHTML = '<div class="error">IPC通信失败: ' + error.message + '</div>';
          }
        }
        
        async function testFullFlow() {
          const result = document.getElementById('flow-result');
          result.innerHTML = '<div class="info">测试完整流程...</div>';
          
          try {
            const response = await window.electronAPI.testFullFlow();
            result.innerHTML = '<div class="success">完整流程测试成功</div><pre>' + JSON.stringify(response, null, 2) + '</pre>';
          } catch (error) {
            result.innerHTML = '<div class="error">完整流程测试失败: ' + error.message + '</div>';
          }
        }
      </script>
    </body>
    </html>
  `;
  
  mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(testHtml));
  mainWindow.webContents.openDevTools();
}

// 初始化数据库
async function initDatabase() {
  try {
    console.log('初始化数据库...');
    database = new Database();
    await database.init();
    console.log('数据库初始化完成');
    return true;
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return false;
  }
}

// 设置IPC处理器
function setupIPC() {
  // 测试数据库连接
  ipcMain.handle('test-database', async () => {
    try {
      const stats = await database.getStatistics();
      return {
        success: true,
        stats,
        message: '数据库连接正常'
      };
    } catch (error) {
      throw new Error('数据库连接失败: ' + error.message);
    }
  });
  
  // 测试素材查询
  ipcMain.handle('test-materials-query', async () => {
    try {
      const materials = await database.getCollections();
      return materials;
    } catch (error) {
      throw new Error('素材查询失败: ' + error.message);
    }
  });
  
  // 创建测试数据
  ipcMain.handle('create-test-data', async () => {
    try {
      const testMaterials = [
        {
          content: '这是第一个测试素材',
          type: 'text',
          category: '测试',
          keywords: '测试,示例',
          source: 'debug-tool'
        },
        {
          content: 'https://github.com/electron/electron',
          type: 'link',
          category: '开发',
          keywords: 'electron,github',
          source: 'debug-tool'
        },
        {
          content: 'console.log("Hello, World!");',
          type: 'code',
          category: '代码',
          keywords: 'javascript,console',
          source: 'debug-tool'
        }
      ];
      
      const results = [];
      for (const material of testMaterials) {
        const result = await database.addCollection(material);
        results.push(result);
      }
      
      return {
        success: true,
        created: results.length,
        materials: results
      };
    } catch (error) {
      throw new Error('创建测试数据失败: ' + error.message);
    }
  });
  
  // 测试IPC通信
  ipcMain.handle('test-ipc', async () => {
    return {
      success: true,
      message: 'IPC通信正常',
      timestamp: new Date().toISOString()
    };
  });
  
  // 测试完整流程
  ipcMain.handle('test-full-flow', async () => {
    try {
      // 1. 数据库连接测试
      const stats = await database.getStatistics();
      
      // 2. 查询现有素材
      const existingMaterials = await database.getCollections();
      
      // 3. 如果没有素材，创建测试数据
      if (existingMaterials.length === 0) {
        await database.addCollection({
          content: '测试素材 - 完整流程',
          type: 'text',
          category: '测试',
          source: 'full-flow-test'
        });
      }
      
      // 4. 重新查询验证
      const finalMaterials = await database.getCollections();
      
      return {
        success: true,
        steps: {
          database: { success: true, stats },
          existing: { count: existingMaterials.length },
          final: { count: finalMaterials.length, materials: finalMaterials }
        }
      };
    } catch (error) {
      throw new Error('完整流程测试失败: ' + error.message);
    }
  });
  
  // 标准的get-collections处理器
  ipcMain.handle('get-collections', async (event, options = {}) => {
    try {
      console.log('get-collections called with options:', options);
      const result = await database.getCollections(options);
      console.log('get-collections result:', result.length, 'items');
      return result;
    } catch (error) {
      console.error('get-collections error:', error);
      throw error;
    }
  });
}

// 应用启动
app.whenReady().then(async () => {
  console.log('应用启动...');
  
  // 初始化数据库
  const dbInitialized = await initDatabase();
  if (!dbInitialized) {
    console.error('数据库初始化失败，退出应用');
    app.quit();
    return;
  }
  
  // 设置IPC处理器
  setupIPC();
  
  // 创建窗口
  await createWindow();
  
  console.log('调试工具启动完成');
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
}); 