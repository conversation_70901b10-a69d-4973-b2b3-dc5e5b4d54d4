#!/bin/bash

# 拖拽功能修复测试脚本

echo "🧪 开始拖拽功能测试..."

# 检查必要的文件是否存在
echo "📋 检查必要文件..."

required_files=(
    "src/ui/scripts/core.js"
    "src/ui/scripts/markdown-editor.js"
    "src/ui/scripts/materials-manager.js"
    "src/ui/scripts/ui-manager.js"
    "src/ui/scripts/app.js"
    "src/ui/styles/organization.css"
    "test-drag-drop-fix.html"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    echo "❌ 缺少必要文件:"
    printf '   %s\n' "${missing_files[@]}"
    exit 1
fi

echo "✅ 所有必要文件都存在"

# 检查JavaScript语法
echo "🔍 检查JavaScript语法..."

js_files=(
    "src/ui/scripts/markdown-editor.js"
    "src/ui/scripts/materials-manager.js"
    "src/ui/scripts/ui-manager.js"
    "src/ui/scripts/app.js"
)

for file in "${js_files[@]}"; do
    if command -v node >/dev/null 2>&1; then
        if ! node -c "$file" 2>/dev/null; then
            echo "❌ JavaScript语法错误: $file"
            exit 1
        fi
    fi
done

echo "✅ JavaScript语法检查通过"

# 检查CSS语法
echo "🎨 检查CSS语法..."
if [ -f "src/ui/styles/organization.css" ]; then
    # 简单的CSS语法检查
    if grep -q "}" "src/ui/styles/organization.css"; then
        echo "✅ CSS文件看起来正常"
    else
        echo "❌ CSS文件可能有语法错误"
        exit 1
    fi
fi

# 启动测试页面
echo "🚀 启动测试页面..."

# 检查是否有可用的浏览器
if command -v open >/dev/null 2>&1; then
    # macOS
    open "test-drag-drop-fix.html"
elif command -v xdg-open >/dev/null 2>&1; then
    # Linux
    xdg-open "test-drag-drop-fix.html"
elif command -v start >/dev/null 2>&1; then
    # Windows
    start "test-drag-drop-fix.html"
else
    echo "⚠️  无法自动打开浏览器，请手动打开 test-drag-drop-fix.html"
fi

echo ""
echo "🔧 测试说明:"
echo "   1. 在浏览器中打开测试页面"
echo "   2. 打开开发者工具查看控制台日志"
echo "   3. 尝试拖拽左侧素材到右侧编辑器"
echo "   4. 观察是否有蓝色插入指示器"
echo "   5. 检查素材是否正确插入并格式化"
echo ""
echo "📊 预期结果:"
echo "   - 文本: 直接插入"
echo "   - 链接: 格式化为 [标题](链接)"
echo "   - 图片: 格式化为 ![标题](图片链接)"
echo "   - 代码: 格式化为代码块"
echo ""
echo "🐛 如果发现问题，请检查:"
echo "   - 浏览器控制台是否有错误"
echo "   - 拖拽数据是否正确传递"
echo "   - MarkdownEditor是否正确初始化"
echo "   - 主应用实例是否可访问"
echo ""
echo "✅ 测试准备完成！" 