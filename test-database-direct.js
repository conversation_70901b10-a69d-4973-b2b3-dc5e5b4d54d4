// 直接测试真实数据库的素材加载
const sqlite3 = require('sqlite3').verbose();

const realDbPath = '/Users/<USER>/Library/Application Support/qnotes/qnotes.db';

async function testRealDatabase() {
    console.log('=== 测试真实数据库素材加载 ===');
    
    return new Promise((resolve, reject) => {
        const db = new sqlite3.Database(realDbPath, (err) => {
            if (err) {
                console.error('❌ 数据库连接失败:', err);
                reject(err);
                return;
            }
            
            console.log('✓ 数据库连接成功');
            
            // 模拟 getCollections 查询
            const sql = 'SELECT * FROM collections ORDER BY created_at DESC';
            
            db.all(sql, [], (err, rows) => {
                if (err) {
                    console.error('❌ 查询失败:', err);
                    db.close();
                    reject(err);
                    return;
                }
                
                console.log(`✓ 查询成功，找到 ${rows.length} 个素材`);
                
                // 检查数据格式
                console.log('检查数据格式...');
                let validCount = 0;
                let invalidCount = 0;
                
                rows.forEach((row, index) => {
                    if (row && row.id && row.content !== undefined) {
                        validCount++;
                        if (index < 3) {
                            console.log(`  示例 ${index + 1}:`, {
                                id: row.id,
                                type: row.type,
                                content: row.content ? row.content.substring(0, 50) + '...' : 'null',
                                category: row.category,
                                created_at: row.created_at
                            });
                        }
                    } else {
                        invalidCount++;
                        console.log(`  ⚠️  无效项目 ${index}:`, row);
                    }
                });
                
                console.log(`✓ 有效素材: ${validCount}`);
                console.log(`⚠️  无效素材: ${invalidCount}`);
                
                // 模拟前端渲染测试
                console.log('\n模拟前端渲染测试...');
                const validRows = rows.filter(row => row && row.id && row.content !== undefined);
                
                validRows.slice(0, 3).forEach((material, index) => {
                    try {
                        // 模拟 renderMaterialItem 的关键逻辑
                        const type = material.type || 'text';
                        const content = material.content || '';
                        const category = material.category || '未分类';
                        const title = content.substring(0, 80) + (content.length > 80 ? '...' : '');
                        
                        console.log(`  渲染项目 ${index + 1}: ✓`);
                        console.log(`    类型: ${type}`);
                        console.log(`    标题: ${title}`);
                        console.log(`    分类: ${category}`);
                        
                    } catch (error) {
                        console.log(`  渲染项目 ${index + 1}: ❌`, error.message);
                    }
                });
                
                db.close();
                console.log('\n✓ 测试完成');
                resolve(rows);
            });
        });
    });
}

testRealDatabase().catch(console.error);