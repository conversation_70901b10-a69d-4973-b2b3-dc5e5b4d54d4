#!/bin/bash

echo "=== QNotes 素材加载修复测试 ==="
echo ""

echo "1. 检查Node.js和npm..."
node --version
npm --version
echo ""

echo "2. 检查项目依赖..."
if [ ! -d "node_modules" ]; then
    echo "安装依赖..."
    npm install
fi
echo "✓ 依赖检查完成"
echo ""

echo "3. 检查数据库文件..."
if [ -f "/Users/<USER>/Library/Application Support/qnotes/qnotes.db" ]; then
    echo "✓ 找到主数据库文件"
    echo "   路径: /Users/<USER>/Library/Application Support/qnotes/qnotes.db"
else
    echo "⚠️  主数据库文件不存在"
fi

if [ -f "/Users/<USER>/Library/Application Support/Electron/qnotes.db" ]; then
    echo "✓ 找到Electron数据库文件"
    echo "   路径: /Users/<USER>/Library/Application Support/Electron/qnotes.db"
fi

if [ -f "data/qnotes.db" ]; then
    echo "✓ 找到本地数据库文件"
    echo "   路径: data/qnotes.db"
fi
echo ""

echo "4. 运行数据库直接测试..."
node test-database-direct.js
echo ""

echo "5. 启动修复工具..."
echo "   这将打开一个Electron窗口进行实时调试"
echo "   请查看控制台输出和开发者工具中的信息"
echo ""

# 启动修复工具
electron fix-materials-loading.js