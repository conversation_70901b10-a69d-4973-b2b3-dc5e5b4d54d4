# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/
out/

# 数据文件
data/
*.db
*.sqlite
*.sqlite3

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/
.cache/

# 系统文件
.DS_Store
Thumbs.db
*.swp
*.swo

# IDE
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# 环境变量
.env
.env.local
.env.development
.env.test
.env.production

# 用户配置
config/user/
settings/user/

# 测试覆盖率
coverage/
.nyc_output/

# 打包文件
*.tgz
*.tar.gz

# macOS
.AppleDouble
.LSOverride

# Windows
ehthumbs.db
Desktop.ini

# Linux
*~

# Electron 特定
# 构建时的原生模块
app/node_modules/

# 发布包
release/

# 调试
.vscode/launch.json