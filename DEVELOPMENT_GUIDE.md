# QNotes 开发指南

## 🎯 快速定位代码修改指南

当需要修改特定功能时，请参考以下文件映射表快速定位代码位置。

## 🗂️ 功能与文件映射表

### 🔍 收集模式相关
| 功能 | 文件位置 | 主要方法/组件 |
|------|----------|--------------|
| 拖拽收集弹窗 | `src/ui/drag-popup.html` | UI结构 |
| | `src/ui/scripts/drag-popup.js` | 拖拽逻辑、保存处理 |
| | `src/ui/styles/drag-popup.css` | 弹窗样式 |
| 快速收集界面 | `src/ui/quick-capture.html` | 主界面结构 |
| | `src/ui/scripts/quick-capture.js` | `saveCollection()`, `showCategories()` |
| | `src/ui/styles/quick-capture.css` | 界面样式 |
| 收集模式控制器 | `src/modes/CollectionMode.js` | `show()`, `hide()`, `setupTray()` |

### 📝 整理模式相关
| 功能 | 文件位置 | 主要方法/组件 |
|------|----------|--------------|
| 主界面布局 | `src/ui/organization.html` | 三栏布局、AI面板 |
| 素材管理 | `src/ui/scripts/organization.js` | `loadMaterials()`, `renderMaterialItem()` |
| 文件管理 | `src/ui/scripts/organization.js` | `createNewFile()`, `createNewFolder()` |
| Markdown编辑器 | `src/ui/scripts/markdown-editor.js` | `initializeMarkdownEditor()` |
| 主界面样式 | `src/ui/styles/organization.css` | `.material-item`, `.input-modal-form` |
| 整理模式控制器 | `src/modes/OrganizationMode.js` | `show()`, `setupIPC()` |

### 🤖 AI功能相关
| 功能 | 文件位置 | 主要方法/组件 |
|------|----------|--------------|
| AI服务核心 | `src/services/AIService.js` | `chat()`, `processText()` |
| 流式对话UI | `src/ui/scripts/organization.js` | `sendAIMessage()`, `setupAIStreamListeners()` |
| AI面板样式 | `src/ui/styles/organization.css` | `.ai-chat-history`, `.ai-message` |
| IPC处理 | `src/modes/OrganizationMode.js` | `ai-chat-stream`, `ai-process-text` |

### 🗄️ 数据库相关
| 功能 | 文件位置 | 主要方法/组件 |
|------|----------|--------------|
| 数据库服务 | `src/database/Database.js` | `addCollection()`, `getDocuments()` |
| 数据迁移 | `src/database/Database.js` | `runMigrations()` |
| 设置管理 | `src/services/Settings.js` | `getAISettings()`, `setAISettings()` |

### 🎨 UI/UX相关
| 功能 | 文件位置 | 主要方法/组件 |
|------|----------|--------------|
| 拖拽精准插入 | `src/ui/scripts/organization.js` | `getTextPositionFromPoint()` |
| 拖拽视觉指示器 | `src/ui/scripts/organization.js` | `showDragIndicator()` |
| 现代化素材卡片 | `src/ui/styles/organization.css` | `.material-item` |
| 输入模态框 | `src/ui/scripts/organization.js` | `showInputModal()` |
| 输入模态框样式 | `src/ui/styles/organization.css` | `.input-modal-form` |
| 图标库 | `src/ui/scripts/organization.js` | `this.icons` 对象 |

## 🔧 常见修改场景

### 1. 修改AI功能
```javascript
// 位置：src/services/AIService.js
// 修改AI模型配置
this.config = {
  baseURL: 'https://api.tu-zi.com/v1',
  model: 'claude-sonnet-4-20250514', // 修改这里
  // ...
};

// 添加新的文本处理功能
async processText(text, action, context = '') {
  const prompts = {
    newAction: `请对以下文本进行新的处理：${text}`, // 添加新功能
    // ...
  };
}
```

### 2. 修改素材卡片样式
```css
/* 位置：src/ui/styles/organization.css */
/* 修改卡片基础样式 */
.material-item {
  background: /* 修改背景 */;
  border-radius: /* 修改圆角 */;
  /* ... */
}

/* 修改悬停效果 */
.material-item:hover {
  transform: /* 修改变换效果 */;
  box-shadow: /* 修改阴影 */;
}
```

### 3. 添加新的拖拽类型
```javascript
// 位置：src/ui/scripts/organization.js
renderMaterialItem(material) {
  const typeIcon = this.getTypeIcon(material.type || 'text');
  // 在 getTypeIcon 方法中添加新类型
}

getTypeIcon(type) {
  return this.icons[type] || this.icons.text; // 确保有默认图标
}

// 在图标库中添加新图标
this.icons = {
  // 现有图标...
  newType: '<svg>...</svg>', // 添加新类型图标
};
```

### 4. 修改拖拽插入精度
```javascript
// 位置：src/ui/scripts/organization.js
getTextPositionFromPoint(clientX, clientY) {
  // 修改字符宽度计算
  getCharWidth(char, metrics) {
    if (/[\u4e00-\u9fff]/.test(char)) {
      return metrics.average * 1.8; // 调整中文字符宽度倍数
    }
    // ...
  }
}
```

### 5. 添加新的快捷键
```javascript
// 位置：src/modes/OrganizationMode.js
setupGlobalShortcuts() {
  globalShortcut.register('CommandOrControl+新快捷键', () => {
    // 新功能逻辑
  });
}
```

### 6. 修改数据库结构
```javascript
// 位置：src/database/Database.js
async runMigrations() {
  // 添加新的迁移
  if (currentVersion < 新版本号) {
    await this.db.exec(`
      ALTER TABLE collections ADD COLUMN new_field TEXT;
    `);
    await this.updateSetting('db_version', 新版本号);
  }
}
```

## 🎨 样式修改指南

### 1. 颜色主题
```css
/* 位置：src/ui/styles/organization.css */
:root {
  --primary-color: #3b82f6;    /* 主色调 */
  --secondary-color: #6b7280;  /* 次要色调 */
  --success-color: #059669;    /* 成功色 */
  --warning-color: #d97706;    /* 警告色 */
  --danger-color: #dc2626;     /* 危险色 */
}
```

### 2. 动画效果
```css
/* 修改过渡动画 */
.material-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); /* 贝塞尔曲线 */
}

/* 修改变换效果 */
.material-item:hover {
  transform: translateY(-4px) scale(1.01); /* Y轴移动 + 缩放 */
}
```

### 3. 响应式断点
```css
/* 平板设备 */
@media (max-width: 768px) {
  .material-item {
    /* 平板样式 */
  }
}

/* 手机设备 */
@media (max-width: 480px) {
  .material-item {
    /* 手机样式 */
  }
}
```

## 🔍 调试技巧

### 1. 开启开发者工具
```javascript
// 位置：src/main.js 或相关模式文件
mainWindow.webContents.openDevTools(); // 添加这行开启调试
```

### 2. 添加调试日志
```javascript
// 在需要调试的方法中添加
console.log('调试信息:', variable);
console.error('错误信息:', error);
console.table(arrayData); // 表格形式显示数组
```

### 3. 检查IPC通信
```javascript
// 主进程监听
ipcRenderer.invoke('method-name', data)
  .then(result => console.log('IPC结果:', result))
  .catch(error => console.error('IPC错误:', error));
```

### 4. 数据库查询调试
```javascript
// 位置：src/database/Database.js
async someMethod() {
  console.log('执行SQL:', query);
  const result = await this.db.all(query, params);
  console.log('查询结果:', result);
  return result;
}
```

## 🧪 测试策略

### 1. 功能测试
- 收集模式：测试拖拽、快捷键、保存
- 整理模式：测试素材加载、AI对话、文件管理
- 跨模式：测试数据同步、设置保存

### 2. 性能测试
- 大量数据加载测试
- AI流式输出性能测试
- 内存泄漏检查

### 3. UI测试
- 不同屏幕尺寸适配
- 深色/浅色主题切换
- 动画效果流畅性

## 🚀 发布流程

### 1. 版本更新
```bash
# 更新版本号
npm version patch|minor|major

# 更新changelog
echo "## v$(node -p "require('./package.json').version")" >> CHANGELOG.md
```

### 2. 构建打包
```bash
# 开发环境构建
npm run build:dev

# 生产环境构建
npm run build:prod

# 打包分发
npm run dist
```

### 3. 质量检查
- [ ] 功能测试完成
- [ ] 性能测试通过
- [ ] UI/UX测试通过
- [ ] 代码审查完成
- [ ] 文档更新完成

## 📋 代码规范

### 1. JavaScript规范
```javascript
// 类名：PascalCase
class OrganizationApp {}

// 方法名：camelCase
async loadMaterials() {}

// 常量：UPPER_SNAKE_CASE
const DEFAULT_CONFIG = {};

// 私有方法：下划线前缀
_privateMethod() {}
```

### 2. CSS规范
```css
/* BEM命名规范 */
.material-item {}           /* 块 */
.material-item__title {}    /* 元素 */
.material-item--selected {} /* 修饰符 */
```

### 3. 文件命名
- HTML文件：kebab-case (`organization.html`)
- JS文件：camelCase (`organizationApp.js`)
- CSS文件：kebab-case (`organization.css`)

## 🔒 安全注意事项

### 1. API密钥安全
```javascript
// 不要在前端代码中硬编码API密钥
// 应该通过设置界面配置
const apiKey = this.settings.getAISettings().apiKey;
```

### 2. 输入验证
```javascript
// 验证用户输入
function validateInput(input) {
  if (!input || typeof input !== 'string') {
    throw new Error('无效输入');
  }
  return input.trim();
}
```

### 3. SQL注入防护
```javascript
// 使用参数化查询
const result = await this.db.all(
  'SELECT * FROM collections WHERE type = ?',
  [type] // 参数数组
);
```

## 📞 获取帮助

### 1. 查看文档
- `PROJECT_DOCUMENTATION.md` - 完整项目文档
- `README.md` - 快速开始指南
- 本文件 - 开发指南

### 2. 调试工具
- Chrome DevTools（内置）
- Electron DevTools
- Console日志输出

### 3. 常见问题
- 检查网络连接（AI功能）
- 验证API配置
- 清除应用缓存
- 重启Electron应用

---

*记住：修改代码前先阅读相关文档，修改后及时更新文档和注释！*