// 拖拽功能修复脚本
// 在浏览器控制台中运行此脚本来修复拖拽功能

console.log('=== 开始修复拖拽功能 ===');

// 修复函数
function fixDragDrop() {
    // 1. 确保主应用实例存在
    if (!window.organizationApp) {
        console.error('主应用未初始化，无法修复拖拽功能');
        return false;
    }
    
    const app = window.organizationApp;
    
    // 2. 确保MarkdownEditor实例存在
    if (!app.markdownEditor) {
        console.error('MarkdownEditor未初始化，无法修复拖拽功能');
        return false;
    }
    
    // 3. 重新绑定素材项的拖拽事件
    console.log('重新绑定素材项拖拽事件...');
    const materialItems = document.querySelectorAll('.material-item');
    
    materialItems.forEach((item, index) => {
        // 移除现有事件监听器
        item.ondragstart = null;
        item.ondragend = null;
        
        // 确保draggable属性设置正确
        item.draggable = true;
        
        // 重新绑定dragstart事件
        item.addEventListener('dragstart', (e) => {
            console.log('拖拽开始:', item.dataset.id);
            app.handleDragStart(e, item.dataset.id);
        });
        
        // 重新绑定dragend事件
        item.addEventListener('dragend', (e) => {
            console.log('拖拽结束:', item.dataset.id);
            e.target.closest('.material-item')?.classList.remove('dragging');
        });
        
        console.log(`素材项 ${index} 事件绑定完成`);
    });
    
    // 4. 确保MarkdownEditor的拖拽事件正确绑定
    console.log('重新绑定MarkdownEditor拖拽事件...');
    if (app.markdownEditor.bindDragEvents) {
        app.markdownEditor.bindDragEvents();
    }
    
    // 5. 添加调试日志
    const originalHandleDrop = app.markdownEditor.handleDrop;
    app.markdownEditor.handleDrop = function(e) {
        console.log('MarkdownEditor接收到drop事件');
        console.log('拖拽数据:', {
            materialData: e.dataTransfer.getData('application/x-material'),
            materialId: e.dataTransfer.getData('text/plain')
        });
        
        return originalHandleDrop.call(this, e);
    };
    
    console.log('拖拽功能修复完成');
    return true;
}

// 添加样式修复
function fixDragStyles() {
    console.log('修复拖拽样式...');
    
    const style = document.createElement('style');
    style.id = 'drag-drop-fix-styles';
    style.textContent = `
        .material-item {
            cursor: grab;
            transition: all 0.2s ease;
        }
        
        .material-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .material-item.dragging {
            opacity: 0.5;
            transform: scale(0.95);
            cursor: grabbing;
        }
        
        .material-item[draggable="true"] {
            -webkit-user-drag: element;
        }
        
        #editorContent {
            position: relative;
        }
        
        #editorContent.drag-over {
            background-color: rgba(59, 130, 246, 0.05);
            border: 2px dashed #3b82f6;
        }
        
        .markdown-insertion-indicator {
            position: absolute;
            width: 2px;
            height: 20px;
            background-color: #3b82f6;
            z-index: 1000;
            display: none;
            pointer-events: none;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    `;
    
    // 移除旧样式
    const oldStyle = document.getElementById('drag-drop-fix-styles');
    if (oldStyle) {
        oldStyle.remove();
    }
    
    document.head.appendChild(style);
    console.log('拖拽样式修复完成');
}

// 测试拖拽功能
function testDragFunction() {
    console.log('=== 测试拖拽功能 ===');
    
    if (!window.organizationApp) {
        console.error('主应用未初始化');
        return;
    }
    
    const app = window.organizationApp;
    const materials = app.materials;
    
    if (materials.length === 0) {
        console.error('没有素材可以测试');
        return;
    }
    
    const testMaterial = materials[0];
    console.log('测试素材:', testMaterial);
    
    // 创建模拟事件
    const mockDataTransfer = {
        data: {},
        setData: function(type, data) {
            this.data[type] = data;
        },
        getData: function(type) {
            return this.data[type];
        },
        effectAllowed: 'copy'
    };
    
    const mockDragStartEvent = {
        dataTransfer: mockDataTransfer,
        target: document.querySelector('.material-item'),
        preventDefault: () => {}
    };
    
    // 测试dragstart
    console.log('测试dragstart事件...');
    app.handleDragStart(mockDragStartEvent, testMaterial.id);
    
    console.log('拖拽数据设置:', mockDataTransfer.data);
    
    // 测试drop
    const mockDropEvent = {
        dataTransfer: mockDataTransfer,
        preventDefault: () => {},
        clientX: 100,
        clientY: 100
    };
    
    console.log('测试drop事件...');
    app.markdownEditor.dropPosition = 0; // 设置一个测试位置
    app.markdownEditor.handleDrop(mockDropEvent);
    
    console.log('测试完成');
}

// 执行修复
fixDragStyles();
const success = fixDragDrop();

if (success) {
    console.log('✅ 拖拽功能修复成功！');
    console.log('现在可以尝试拖拽素材到编辑器中');
    
    // 提供测试函数
    window.testDragFunction = testDragFunction;
    console.log('运行 testDragFunction() 来测试拖拽功能');
} else {
    console.log('❌ 拖拽功能修复失败');
}

console.log('=== 修复完成 ==='); 