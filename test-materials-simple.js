// 简化的素材加载测试脚本
const Database = require('./src/database/Database');

async function testMaterialsLoading() {
    console.log('=== 开始测试素材加载 ===');
    
    try {
        // 初始化数据库
        console.log('1. 初始化数据库...');
        const database = new Database();
        await database.init();
        console.log('✓ 数据库初始化成功');
        
        // 查询素材
        console.log('2. 查询素材数据...');
        const collections = await database.getCollections();
        console.log(`✓ 查询成功，找到 ${collections.length} 个素材`);
        
        if (collections.length > 0) {
            console.log('3. 素材数据示例:');
            collections.slice(0, 3).forEach((item, index) => {
                console.log(`   ${index + 1}. ID: ${item.id}`);
                console.log(`      类型: ${item.type || 'null'}`);
                console.log(`      内容: ${item.content ? item.content.substring(0, 50) + '...' : 'null'}`);
                console.log(`      分类: ${item.category || 'null'}`);
                console.log(`      创建时间: ${item.created_at || 'null'}`);
                console.log('');
            });
        }
        
        // 检查数据完整性
        console.log('4. 检查数据完整性...');
        let validCount = 0;
        let invalidCount = 0;
        
        collections.forEach((item, index) => {
            if (item && item.id && item.content !== undefined) {
                validCount++;
            } else {
                invalidCount++;
                console.log(`   ⚠️  无效项目 ${index}:`, item);
            }
        });
        
        console.log(`✓ 有效素材: ${validCount}`);
        console.log(`⚠️  无效素材: ${invalidCount}`);
        
        // 关闭数据库
        await database.close();
        console.log('✓ 数据库连接已关闭');
        
        console.log('=== 测试完成 ===');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        process.exit(1);
    }
}

// 运行测试
testMaterialsLoading();