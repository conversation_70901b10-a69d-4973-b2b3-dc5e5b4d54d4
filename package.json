{"name": "qnotes", "version": "1.0.0", "description": "双模式智能内容收集整理工具", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "test": "jest"}, "keywords": ["electron", "ai", "notes", "collection", "markdown"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.0.0", "jest": "^29.0.0"}, "dependencies": {"sqlite3": "^5.1.0", "axios": "^1.6.0", "uuid": "^9.0.0", "lodash": "^4.17.0", "electron-store": "^8.1.0", "marked": "^9.0.0", "highlight.js": "^11.9.0"}, "build": {"appId": "com.yourcompany.qnotes", "productName": "QNotes", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}