<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试版本 - QNotes</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-panel { 
            background: #f0f0f0; 
            padding: 15px; 
            margin: 10px 0; 
            border: 1px solid #ccc;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .materials-list { 
            border: 2px solid #007acc; 
            padding: 15px; 
            margin: 15px 0; 
            min-height: 200px;
        }
        .material-item { 
            border: 1px solid #ccc; 
            padding: 10px; 
            margin: 10px 0; 
            background: #f9f9f9;
        }
        .loading { text-align: center; padding: 20px; color: #666; }
        .error { color: red; font-weight: bold; }
        .success { color: green; font-weight: bold; }
    </style>
</head>
<body>
    <h1>QNotes 调试版本</h1>
    
    <button onclick="debugApp.testMaterialsManager()">测试素材管理器</button>
    <button onclick="debugApp.clearDebug()">清除调试</button>
    
    <div id="debugPanel" class="debug-panel">调试信息将显示在这里...</div>
    
    <h2>素材列表</h2>
    <div id="materialsList" class="materials-list">
        <div class="loading">初始化中...</div>
    </div>
    
    <script>
        // 全局错误捕获
        window.addEventListener('error', function(e) {
            debugApp.log('ERROR: ' + e.message + ' at ' + e.filename + ':' + e.lineno, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            debugApp.log('UNHANDLED PROMISE REJECTION: ' + e.reason, 'error');
        });
        
        // 调试应用
        class DebugApp {
            constructor() {
                this.materials = [];
                this.log('调试应用初始化完成');
                this.testMaterialsManager();
            }
            
            log(message, type = 'info') {
                const panel = document.getElementById('debugPanel');
                const timestamp = new Date().toLocaleTimeString();
                const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : '');
                panel.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\\n`;
                panel.scrollTop = panel.scrollHeight;
                console.log(message);
            }
            
            clearDebug() {
                document.getElementById('debugPanel').innerHTML = '';
            }
            
            async testMaterialsManager() {
                try {
                    this.log('开始测试素材管理器...');
                    
                    // 模拟CoreUtils
                    const CoreUtils = {
                        showElementLoading: (elementId) => {
                            this.log(`showElementLoading: ${elementId}`);
                            const element = document.getElementById(elementId);
                            if (element) {
                                element.innerHTML = '<div class="loading">加载中...</div>';
                            }
                        },
                        hideElementLoading: (elementId) => {
                            this.log(`hideElementLoading: ${elementId}`);
                            const element = document.getElementById(elementId);
                            if (element) {
                                element.innerHTML = '';
                            }
                        }
                    };
                    
                    // 模拟MaterialsManager的核心逻辑
                    this.log('调用 showElementLoading...');
                    CoreUtils.showElementLoading('materialsList');
                    
                    this.log('检查 ipcRenderer...');
                    if (!window.ipcRenderer) {
                        this.log('ipcRenderer 不存在，使用模拟数据');
                        
                        this.log('生成模拟数据...');
                        this.materials = [
                            {
                                id: 'test-1',
                                content: '测试素材1',
                                type: 'text',
                                folder: 'root'
                            },
                            {
                                id: 'test-2',
                                content: '测试素材2',
                                type: 'text',
                                folder: 'root'
                            }
                        ];
                        
                        this.log(`生成了 ${this.materials.length} 个模拟素材`);
                        
                        this.log('调用 hideElementLoading...');
                        CoreUtils.hideElementLoading('materialsList');
                        
                        this.log('开始渲染素材...');
                        this.renderMaterials();
                        
                        this.log('素材管理器测试完成', 'success');
                    } else {
                        this.log('在Electron环境中，跳过测试');
                    }
                    
                } catch (error) {
                    this.log(`测试失败: ${error.message}`, 'error');
                    this.log(`错误堆栈: ${error.stack}`, 'error');
                }
            }
            
            renderMaterials() {
                try {
                    this.log('renderMaterials: 开始渲染');
                    
                    const container = document.getElementById('materialsList');
                    if (!container) {
                        this.log('materialsList 容器不存在', 'error');
                        return;
                    }
                    
                    this.log(`renderMaterials: 素材数量 = ${this.materials.length}`);
                    
                    if (!this.materials || this.materials.length === 0) {
                        container.innerHTML = '<div class="loading">暂无素材</div>';
                        this.log('renderMaterials: 显示空状态');
                        return;
                    }
                    
                    let html = '';
                    this.materials.forEach(material => {
                        html += `
                            <div class="material-item">
                                <strong>ID:</strong> ${material.id}<br>
                                <strong>内容:</strong> ${material.content}<br>
                                <strong>文件夹:</strong> ${material.folder}
                            </div>
                        `;
                    });
                    
                    this.log('renderMaterials: 设置HTML内容');
                    container.innerHTML = html;
                    
                    this.log('renderMaterials: 渲染完成', 'success');
                    
                } catch (error) {
                    this.log(`renderMaterials 失败: ${error.message}`, 'error');
                }
            }
        }
        
        // 初始化调试应用
        const debugApp = new DebugApp();
    </script>
</body>
</html>
