const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    show: false // 先不显示，等加载完成后再显示
  });

  // 加载 organization.html
  mainWindow.loadFile('src/ui/organization.html');

  // 当页面加载完成后显示窗口
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    console.log('Organization window is ready and shown');
  });

  // 打开开发者工具
  mainWindow.webContents.openDevTools();

  // 监听页面加载事件
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('Page finished loading');
  });

  // 监听控制台消息
  mainWindow.webContents.on('console-message', (event, level, message) => {
    console.log(`Console [${level}]: ${message}`);
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.whenReady().then(() => {
  console.log('Electron app is ready');
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 基本的 IPC 处理器
ipcMain.handle('load-materials', async () => {
  console.log('Loading materials...');
  return {
    success: true,
    data: [
      {
        id: '1',
        title: '测试素材 1',
        content: '这是第一个测试素材的内容',
        type: 'text',
        createdAt: new Date().toISOString(),
        tags: ['测试', '示例']
      },
      {
        id: '2',
        title: '测试素材 2',
        content: '这是第二个测试素材的内容',
        type: 'text',
        createdAt: new Date().toISOString(),
        tags: ['测试']
      }
    ]
  };
});

ipcMain.handle('load-files', async () => {
  console.log('Loading files...');
  return {
    success: true,
    data: [
      {
        id: 'file1',
        name: '测试文档.md',
        path: '/demo/测试文档.md',
        type: 'file',
        size: 1024,
        modifiedAt: new Date().toISOString()
      },
      {
        id: 'folder1',
        name: '文档文件夹',
        path: '/demo/文档文件夹',
        type: 'folder',
        children: [
          {
            id: 'file2',
            name: '子文档.md',
            path: '/demo/文档文件夹/子文档.md',
            type: 'file',
            size: 512,
            modifiedAt: new Date().toISOString()
          }
        ]
      }
    ]
  };
});

ipcMain.handle('save-document', async (event, data) => {
  console.log('Saving document:', data.title);
  return { success: true };
});

ipcMain.handle('ai-chat', async (event, message) => {
  console.log('AI chat:', message);
  return {
    success: true,
    data: {
      response: `AI回复：我收到了您的消息"${message}"。这是一个测试回复。`
    }
  };
});

ipcMain.handle('get-settings', async () => {
  return {
    success: true,
    data: {
      theme: 'light',
      language: 'zh-CN',
      aiProvider: 'openai'
    }
  };
});

console.log('Test Organization app starting...'); 