<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试快速收藏窗口</title>
    <link rel="stylesheet" href="src/ui/styles/quick-capture.css">
    <style>
        body {
            background: #f0f0f0;
            padding: 50px;
        }
        .test-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 80vh;
        }
        .test-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="test-info">
        📝 快速捕获界面测试<br>
        🔧 修复内容：<br>
        • 窗口尺寸：450x420px<br>
        • 保存按钮：文本修复<br>
        • 布局优化：更好的空间利用<br>
        <br>
        🎯 测试要点：<br>
        • 确保所有按钮可见<br>
        • 保存功能正常工作<br>
        • 窗口大小合适
    </div>
    <div class="test-container">
        <div class="capture-container">
            <div class="capture-header">
                <h3>📝 快速收藏</h3>
                <button class="btn-icon">✕</button>
            </div>
            
            <div class="capture-content">
                <div class="input-group">
                    <label for="contentType">类型</label>
                    <select id="contentType">
                        <option value="text">文本</option>
                        <option value="link">链接</option>
                        <option value="image">图片</option>
                    </select>
                </div>
                
                <div class="input-group">
                    <label for="contentInput">内容</label>
                    <textarea id="contentInput" placeholder="输入要收藏的内容..." rows="3">这是一个测试内容，用来检查界面是否正常显示。</textarea>
                </div>
                
                <div class="input-group">
                    <label for="sourceInput">来源 (可选)</label>
                    <input type="text" id="sourceInput" placeholder="内容来源..." value="测试来源">
                </div>
                
                <div class="capture-actions">
                    <button class="btn-secondary">粘贴</button>
                    <button class="btn-secondary">清除</button>
                    <button class="btn-primary">💾 保存</button>
                </div>
            </div>
            
            <div class="capture-footer">
                <div class="shortcuts">
                    <span>快捷键: Ctrl+V 粘贴 | Enter 保存 | Esc 取消</span>
                </div>
                <button class="btn-link">切换到整理模式</button>
            </div>
        </div>
    </div>
</body>
</html>