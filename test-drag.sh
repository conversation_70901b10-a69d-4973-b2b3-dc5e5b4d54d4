#!/bin/bash

# QNotes 拖拽功能测试脚本

echo "🚀 启动 QNotes 拖拽功能测试"
echo "================================"

# 检查文件是否存在
if [ ! -f "test-drag-drop.html" ]; then
    echo "❌ 测试文件不存在"
    exit 1
fi

if [ ! -f "src/ui/scripts/markdown-editor.js" ]; then
    echo "❌ markdown-editor.js 文件不存在"
    exit 1
fi

# 检查语法
echo "📝 检查JavaScript语法..."
node -c src/ui/scripts/markdown-editor.js
if [ $? -eq 0 ]; then
    echo "✅ markdown-editor.js 语法正确"
else
    echo "❌ markdown-editor.js 语法错误"
    exit 1
fi

node -c src/ui/scripts/organization.js
if [ $? -eq 0 ]; then
    echo "✅ organization.js 语法正确"
else
    echo "❌ organization.js 语法错误"
    exit 1
fi

# 启动测试页面
echo "🌐 启动测试页面..."
open test-drag-drop.html

echo "✨ 测试页面已启动！"
echo ""
echo "📋 测试指南:"
echo "1. 从左侧素材库拖拽内容到右侧编辑器"
echo "2. 观察蓝色闪烁的插入指示器"
echo "3. 松开鼠标完成插入"
echo "4. 查看内容是否正确格式化"
echo ""
echo "🔧 如果遇到问题，请检查浏览器控制台" 