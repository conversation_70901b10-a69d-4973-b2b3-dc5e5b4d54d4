#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

class BasicFunctionalityTester {
    constructor() {
        this.projectRoot = __dirname;
        this.testResults = [];
    }

    async runTests() {
        console.log('🧪 开始基本功能测试...\n');

        // 测试1: 检查依赖
        await this.testDependencies();
        
        // 测试2: 检查主要文件
        await this.testMainFiles();
        
        // 测试3: 检查数据库初始化
        await this.testDatabaseInit();
        
        // 测试4: 检查设置加载
        await this.testSettingsLoad();
        
        // 显示测试结果
        this.showResults();
    }

    async testDependencies() {
        console.log('📦 检查依赖...');
        
        try {
            const fs = require('fs');
            const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'));
            
            // 检查关键依赖
            const requiredDeps = ['electron', 'sqlite3', 'axios'];
            const missingDeps = requiredDeps.filter(dep => 
                !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
            );
            
            if (missingDeps.length > 0) {
                this.addResult('依赖检查', false, `缺少依赖: ${missingDeps.join(', ')}`);
            } else {
                this.addResult('依赖检查', true, '所有必需依赖都已安装');
            }
            
        } catch (error) {
            this.addResult('依赖检查', false, `错误: ${error.message}`);
        }
    }

    async testMainFiles() {
        console.log('📄 检查主要文件...');
        
        const fs = require('fs');
        const requiredFiles = [
            'src/main.js',
            'src/controllers/AppController.js',
            'src/modes/CollectionMode.js',
            'src/modes/OrganizationMode.js',
            'src/services/Settings.js',
            'src/database/Database.js',
            'src/ui/organization.html',
            'src/ui/quick-capture.html'
        ];
        
        const missingFiles = requiredFiles.filter(file => 
            !fs.existsSync(path.join(this.projectRoot, file))
        );
        
        if (missingFiles.length > 0) {
            this.addResult('文件检查', false, `缺少文件: ${missingFiles.join(', ')}`);
        } else {
            this.addResult('文件检查', true, '所有必需文件都存在');
        }
    }

    async testDatabaseInit() {
        console.log('🗄️  检查数据库初始化...');
        
        try {
            const Database = require('./src/database/Database');
            const db = new Database();
            
            // 测试数据库初始化
            await db.init();
            
            // 测试基本操作
            const testData = {
                content: 'Test content',
                type: 'text',
                source: 'test',
                timestamp: new Date().toISOString()
            };
            
            const result = await db.addCollection(testData);
            
            if (result && result.id) {
                // 清理测试数据
                await db.deleteCollection(result.id);
                this.addResult('数据库初始化', true, '数据库初始化和基本操作正常');
            } else {
                this.addResult('数据库初始化', false, '数据库操作失败');
            }
            
            await db.close();
            
        } catch (error) {
            this.addResult('数据库初始化', false, `错误: ${error.message}`);
        }
    }

    async testSettingsLoad() {
        console.log('⚙️  检查设置加载...');
        
        try {
            const Settings = require('./src/services/Settings');
            const settings = new Settings();
            
            await settings.load();
            
            // 测试基本设置操作
            const testKey = 'test.value';
            const testValue = 'test-value';
            
            settings.set(testKey, testValue);
            const retrievedValue = settings.get(testKey);
            
            if (retrievedValue === testValue) {
                settings.delete(testKey);
                this.addResult('设置加载', true, '设置加载和操作正常');
            } else {
                this.addResult('设置加载', false, '设置操作失败');
            }
            
        } catch (error) {
            this.addResult('设置加载', false, `错误: ${error.message}`);
        }
    }

    addResult(test, passed, message) {
        this.testResults.push({ test, passed, message });
        const status = passed ? '✅' : '❌';
        console.log(`  ${status} ${test}: ${message}`);
    }

    showResults() {
        console.log('\n📊 测试结果汇总:');
        console.log('='.repeat(50));
        
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        
        this.testResults.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.test}`);
            if (!result.passed) {
                console.log(`   ${result.message}`);
            }
        });
        
        console.log('='.repeat(50));
        console.log(`总计: ${passed}/${total} 测试通过`);
        
        if (passed === total) {
            console.log('🎉 所有基本功能测试通过！');
            console.log('\n💡 现在可以运行应用:');
            console.log('   npm start');
        } else {
            console.log('⚠️  部分测试失败，请检查上述错误');
        }
    }
}

// 运行测试
if (require.main === module) {
    const tester = new BasicFunctionalityTester();
    tester.runTests().catch(error => {
        console.error('❌ 测试过程中发生错误:', error);
        process.exit(1);
    });
}

module.exports = BasicFunctionalityTester; 