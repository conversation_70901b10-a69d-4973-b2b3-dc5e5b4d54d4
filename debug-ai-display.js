// AI对话显示问题调试脚本
console.log('开始调试AI对话显示问题...');

// 检查DOM元素
function checkDOMElements() {
    console.log('=== DOM元素检查 ===');
    
    const aiChatHistory = document.getElementById('aiChatHistory');
    console.log('aiChatHistory元素:', aiChatHistory);
    
    if (aiChatHistory) {
        console.log('aiChatHistory内容:', aiChatHistory.innerHTML);
        console.log('aiChatHistory子元素数量:', aiChatHistory.children.length);
    }
    
    const aiInput = document.getElementById('aiInput');
    console.log('aiInput元素:', aiInput);
    
    const sendBtn = document.getElementById('sendBtn');
    console.log('sendBtn元素:', sendBtn);
}

// 检查AI管理器
function checkAIManager() {
    console.log('=== AI管理器检查 ===');
    
    if (window.organizationApp && window.organizationApp.aiManager) {
        const aiManager = window.organizationApp.aiManager;
        console.log('AI管理器存在:', aiManager);
        console.log('AI历史记录:', aiManager.aiHistory);
        console.log('当前AI消息:', aiManager.currentAIMessage);
        console.log('是否正在流式处理:', aiManager.isAIStreaming);
    } else {
        console.log('AI管理器不存在');
    }
}

// 检查事件绑定
function checkEventBindings() {
    console.log('=== 事件绑定检查 ===');
    
    const sendBtn = document.getElementById('sendBtn');
    if (sendBtn) {
        console.log('发送按钮事件监听器:', sendBtn.onclick);
        console.log('发送按钮事件属性:', sendBtn.getAttribute('onclick'));
    }
}

// 模拟AI消息
function simulateAIMessage() {
    console.log('=== 模拟AI消息 ===');
    
    if (window.organizationApp && window.organizationApp.aiManager) {
        const aiManager = window.organizationApp.aiManager;
        
        // 添加测试消息
        const testMessage = {
            role: 'assistant',
            content: '这是一条测试AI消息，用于检查显示功能。',
            timestamp: Date.now()
        };
        
        aiManager.aiHistory.push(testMessage);
        aiManager.renderAIHistory();
        
        console.log('已添加测试消息并渲染');
    }
}

// 检查CSS样式
function checkCSSStyles() {
    console.log('=== CSS样式检查 ===');
    
    const aiChatHistory = document.getElementById('aiChatHistory');
    if (aiChatHistory) {
        const styles = window.getComputedStyle(aiChatHistory);
        console.log('aiChatHistory样式:');
        console.log('- display:', styles.display);
        console.log('- visibility:', styles.visibility);
        console.log('- height:', styles.height);
        console.log('- overflow:', styles.overflow);
    }
}

// 检查按钮点击功能
function testButtonClick() {
    console.log('=== 按钮点击测试 ===');
    
    if (window.organizationApp && window.organizationApp.aiManager) {
        const aiManager = window.organizationApp.aiManager;
        
        // 测试复制功能
        try {
            aiManager.copyToClipboard('测试复制内容');
            console.log('复制功能测试成功');
        } catch (error) {
            console.error('复制功能测试失败:', error);
        }
        
        // 测试插入功能
        try {
            aiManager.insertToEditor('测试插入内容');
            console.log('插入功能测试成功');
        } catch (error) {
            console.error('插入功能测试失败:', error);
        }
    }
}

// 运行所有检查
function runAllChecks() {
    checkDOMElements();
    checkAIManager();
    checkEventBindings();
    checkCSSStyles();
    simulateAIMessage();
    testButtonClick();
}

// 等待DOM加载完成后运行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllChecks);
} else {
    runAllChecks();
}

// 导出函数以便手动调用
window.debugAI = {
    checkDOMElements,
    checkAIManager,
    checkEventBindings,
    checkCSSStyles,
    simulateAIMessage,
    testButtonClick,
    runAllChecks
};

console.log('调试脚本已加载，可以在控制台调用 debugAI.runAllChecks() 进行检查'); 