<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试删除功能</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .material-item { 
            border: 1px solid #ccc; 
            padding: 10px; 
            margin: 10px 0; 
            background: #f9f9f9;
        }
        .material-actions button {
            margin: 0 5px;
            padding: 5px 10px;
            cursor: pointer;
        }
        .delete-btn { background: #ff4444; color: white; border: none; }
        .folder-item { 
            border: 2px solid #007acc; 
            padding: 15px; 
            margin: 15px 0; 
            background: #e6f3ff;
        }
        .folder-header { font-weight: bold; margin-bottom: 10px; }
        .folder-content { margin-left: 20px; }
        .empty-state { 
            text-align: center; 
            padding: 50px; 
            color: #666; 
            border: 2px dashed #ccc;
        }
        #debug { 
            background: #f0f0f0; 
            padding: 10px; 
            margin: 20px 0; 
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>素材删除功能测试</h1>
    
    <div id="debug">调试信息将显示在这里...</div>
    
    <div id="materialsList">加载中...</div>
    
    <script>
        // 模拟MaterialsManager的核心功能
        class TestMaterialsManager {
            constructor() {
                this.materials = [
                    {
                        id: 'test-1',
                        content: '这是第一个测试素材',
                        type: 'text',
                        category: '测试',
                        folder: 'root',
                        created_at: new Date().toISOString()
                    },
                    {
                        id: 'test-2',
                        content: '这是第二个测试素材',
                        type: 'text',
                        category: '测试',
                        folder: 'root',
                        created_at: new Date().toISOString()
                    },
                    {
                        id: 'test-3',
                        content: '这是文件夹中的素材',
                        type: 'text',
                        category: '测试',
                        folder: '测试文件夹',
                        created_at: new Date().toISOString()
                    },
                    {
                        id: 'folder-config-1',
                        content: '# 测试文件夹\\n\\n这是一个文件夹配置文件，请勿删除。',
                        type: 'text',
                        category: '系统',
                        folder: '测试文件夹',
                        created_at: new Date().toISOString(),
                        keywords: '.folder_config'
                    }
                ];
                this.searchQuery = '';
                this.selectedMaterials = new Set();
                this.openFolders = new Set(['测试文件夹']);
                
                this.updateDebug('初始化完成');
                this.renderMaterials();
            }
            
            updateDebug(message) {
                const debug = document.getElementById('debug');
                const timestamp = new Date().toLocaleTimeString();
                debug.textContent += `[${timestamp}] ${message}\\n`;
                console.log(message);
            }
            
            groupMaterialsByFolder() {
                const grouped = { root: [], folders: {} };
                
                this.materials.forEach(material => {
                    // 跳过文件夹配置文件
                    if (material.keywords && material.keywords.includes('.folder_config')) {
                        return;
                    }
                    
                    const folder = material.folder || 'root';
                    if (folder === 'root') {
                        grouped.root.push(material);
                    } else {
                        if (!grouped.folders[folder]) {
                            grouped.folders[folder] = [];
                        }
                        grouped.folders[folder].push(material);
                    }
                });
                
                this.updateDebug(`分组结果: root=${grouped.root.length}, folders=${Object.keys(grouped.folders).length}`);
                return grouped;
            }
            
            renderMaterials() {
                this.updateDebug(`开始渲染，总素材数: ${this.materials.length}`);
                
                const container = document.getElementById('materialsList');
                
                if (!this.materials || this.materials.length === 0) {
                    container.innerHTML = '<div class="empty-state"><h3>暂无素材</h3></div>';
                    this.updateDebug('显示空状态');
                    return;
                }
                
                const groupedMaterials = this.groupMaterialsByFolder();
                let html = '';
                
                // 渲染文件夹
                Object.keys(groupedMaterials.folders).forEach(folderName => {
                    const materials = groupedMaterials.folders[folderName];
                    html += `
                        <div class="folder-item">
                            <div class="folder-header">📁 ${folderName} (${materials.length})</div>
                            <div class="folder-content">
                                ${materials.map(m => this.renderMaterialItem(m)).join('')}
                            </div>
                        </div>
                    `;
                });
                
                // 渲染根目录素材
                if (groupedMaterials.root.length > 0) {
                    html += '<div class="folder-header">📄 根目录</div>';
                    html += groupedMaterials.root.map(m => this.renderMaterialItem(m)).join('');
                }
                
                container.innerHTML = html;
                this.updateDebug('渲染完成');
            }
            
            renderMaterialItem(material) {
                return `
                    <div class="material-item" data-id="${material.id}">
                        <div><strong>ID:</strong> ${material.id}</div>
                        <div><strong>内容:</strong> ${material.content}</div>
                        <div><strong>文件夹:</strong> ${material.folder || 'root'}</div>
                        <div class="material-actions">
                            <button class="delete-btn" onclick="testManager.deleteMaterial('${material.id}')">删除</button>
                        </div>
                    </div>
                `;
            }
            
            deleteMaterial(id) {
                this.updateDebug(`开始删除素材: ${id}`);
                
                const confirmed = confirm('确定要删除这个素材吗？');
                if (!confirmed) {
                    this.updateDebug('用户取消删除');
                    return;
                }
                
                const originalLength = this.materials.length;
                this.updateDebug(`删除前素材列表: ${this.materials.map(m => m.id).join(', ')}`);
                
                this.materials = this.materials.filter(material => material.id !== id);
                
                this.updateDebug(`删除后素材数量: ${originalLength} -> ${this.materials.length}`);
                this.updateDebug(`删除后素材列表: ${this.materials.map(m => m.id).join(', ')}`);
                
                this.renderMaterials();
                this.updateDebug('删除操作完成');
            }
        }
        
        // 初始化测试管理器
        const testManager = new TestMaterialsManager();
    </script>
</body>
</html>
