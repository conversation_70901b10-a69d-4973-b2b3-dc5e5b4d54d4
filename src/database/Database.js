const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { app } = require('electron');
const fs = require('fs');

class Database {
  constructor() {
    this.db = null;
    this.dbPath = this.getDbPath();
  }

  getDbPath() {
    try {
      // 检查 app 是否已经准备好
      if (app && app.isReady()) {
        // 直接使用已知的正确路径
        const correctDbPath = '/Users/<USER>/Library/Application Support/qnotes/qnotes.db';
        
        // 检查正确的数据库是否存在
        if (fs.existsSync(correctDbPath)) {
          console.log('使用现有的正确数据库路径:', correctDbPath);
          return correctDbPath;
        }
        
        // 如果不存在，使用标准的用户数据路径
        const userDataPath = app.getPath('userData');
        console.log('Electron用户数据路径:', userDataPath);
        
        // 确保使用 qnotes 作为应用名称
        const appDataDir = path.dirname(userDataPath);
        const qnotesDataDir = path.join(appDataDir, 'qnotes');
        
        // 确保目录存在
        if (!fs.existsSync(qnotesDataDir)) {
          fs.mkdirSync(qnotesDataDir, { recursive: true });
        }
        
        const dbPath = path.join(qnotesDataDir, 'qnotes.db');
        console.log('新数据库路径:', dbPath);
        return dbPath;
      } else {
        throw new Error('App not ready');
      }
    } catch (error) {
      // 如果在测试环境或 Electron 未初始化，使用本地路径
      const fallbackPath = path.join(process.cwd(), 'data');
      console.log('使用本地数据路径:', fallbackPath);
      
      // 确保目录存在
      if (!fs.existsSync(fallbackPath)) {
        fs.mkdirSync(fallbackPath, { recursive: true });
      }
      
      return path.join(fallbackPath, 'qnotes.db');
    }
  }

  async init() {
    return new Promise((resolve, reject) => {
      console.log('初始化数据库:', this.dbPath);
      
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('数据库连接失败:', err);
          reject(err);
        } else {
          console.log('数据库连接成功');
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  async createTables() {
    const tables = [
      // 收藏表
      `CREATE TABLE IF NOT EXISTS collections (
        id TEXT PRIMARY KEY,
        content TEXT NOT NULL,
        type TEXT NOT NULL,
        source TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        ai_processed BOOLEAN DEFAULT FALSE,
        category TEXT,
        keywords TEXT,
        summary TEXT,
        priority INTEGER DEFAULT 3,
        image_data TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 生成的文档表
      `CREATE TABLE IF NOT EXISTS documents (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        folder TEXT,
        size INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        collection_ids TEXT,
        sort_order INTEGER DEFAULT 0
      )`,
      
      // 设置表
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 标签表
      `CREATE TABLE IF NOT EXISTS tags (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        color TEXT DEFAULT '#007acc',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 收藏-标签关联表
      `CREATE TABLE IF NOT EXISTS collection_tags (
        collection_id TEXT,
        tag_id TEXT,
        PRIMARY KEY (collection_id, tag_id),
        FOREIGN KEY (collection_id) REFERENCES collections(id) ON DELETE CASCADE,
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
      )`
    ];

    for (const sql of tables) {
      await this.run(sql);
    }
    
    // 数据库迁移：添加新字段
    await this.migrate();
    
    console.log('数据库表创建完成');
  }
  
  // 数据库迁移
  async migrate() {
    try {
      // 检查documents表字段
      const documentsTableInfo = await this.all("PRAGMA table_info(documents)");
      const hasFolder = documentsTableInfo.some(col => col.name === 'folder');
      const hasSize = documentsTableInfo.some(col => col.name === 'size');
      const hasSortOrder = documentsTableInfo.some(col => col.name === 'sort_order');

      if (!hasFolder) {
        await this.run("ALTER TABLE documents ADD COLUMN folder TEXT");
        console.log('已添加documents.folder字段');
      }

      if (!hasSize) {
        await this.run("ALTER TABLE documents ADD COLUMN size INTEGER DEFAULT 0");
        console.log('已添加documents.size字段');
      }

      if (!hasSortOrder) {
        await this.run("ALTER TABLE documents ADD COLUMN sort_order INTEGER DEFAULT 0");
        console.log('已添加documents.sort_order字段');
      }

      // 检查collections表字段
      const collectionsTableInfo = await this.all("PRAGMA table_info(collections)");
      const hasImageData = collectionsTableInfo.some(col => col.name === 'image_data');
      const collectionsHasFolder = collectionsTableInfo.some(col => col.name === 'folder');
      const collectionsHasSortOrder = collectionsTableInfo.some(col => col.name === 'sort_order');

      if (!hasImageData) {
        await this.run("ALTER TABLE collections ADD COLUMN image_data TEXT");
        console.log('已添加collections.image_data字段');
      }

      if (!collectionsHasFolder) {
        await this.run("ALTER TABLE collections ADD COLUMN folder TEXT");
        console.log('已添加collections.folder字段');
      }

      if (!collectionsHasSortOrder) {
        await this.run("ALTER TABLE collections ADD COLUMN sort_order INTEGER DEFAULT 0");
        console.log('已添加collections.sort_order字段');
      }
    } catch (error) {
      console.error('数据库迁移失败:', error);
    }
  }

  // 通用查询方法
  async run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('SQL执行失败:', err, sql);
          reject(err);
        } else {
          resolve({ lastID: this.lastID, changes: this.changes });
        }
      });
    });
  }

  async get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          console.error('SQL查询失败:', err, sql);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  async all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('SQL查询失败:', err, sql);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 收藏相关方法
  async addCollection(data) {
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const sql = `
      INSERT INTO collections (id, content, type, source, timestamp, category, keywords, summary, priority, image_data, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      id,
      data.content,
      data.type,
      data.source || null,
      data.timestamp || now,
      data.category || null,
      data.keywords || null,
      data.summary || null,
      data.priority || 3,
      data.imageData || null,
      now,
      now
    ];
    
    await this.run(sql, params);
    return await this.getCollection(id);
  }

  async getCollection(id) {
    const sql = 'SELECT * FROM collections WHERE id = ?';
    return await this.get(sql, [id]);
  }

  async getCollections(options = {}) {
    let sql = 'SELECT * FROM collections';
    const params = [];
    const conditions = [];

    // 添加过滤条件
    if (options.type) {
      conditions.push('type = ?');
      params.push(options.type);
    }

    if (options.category) {
      conditions.push('category = ?');
      params.push(options.category);
    }

    if (options.ai_processed !== undefined) {
      conditions.push('ai_processed = ?');
      params.push(options.ai_processed);
    }

    if (options.search) {
      conditions.push('(content LIKE ? OR keywords LIKE ? OR summary LIKE ?)');
      const searchParam = `%${options.search}%`;
      params.push(searchParam, searchParam, searchParam);
    }

    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }

    // 排序
    const orderBy = options.orderBy || 'created_at';
    const order = options.order || 'DESC';
    sql += ` ORDER BY ${orderBy} ${order}`;

    // 限制数量
    if (options.limit) {
      sql += ' LIMIT ?';
      params.push(options.limit);
      
      if (options.offset) {
        sql += ' OFFSET ?';
        params.push(options.offset);
      }
    }

    return await this.all(sql, params);
  }

  async updateCollection(id, data) {
    const now = new Date().toISOString();
    const fields = [];
    const params = [];

    // 动态构建更新字段
    const updateFields = ['content', 'type', 'source', 'category', 'keywords', 'summary', 'priority', 'ai_processed', 'imageData', 'folder', 'sort_order'];

    updateFields.forEach(field => {
      if (data[field] !== undefined) {
        // 将 imageData 映射到数据库字段 image_data
        const dbField = field === 'imageData' ? 'image_data' : field;
        fields.push(`${dbField} = ?`);
        params.push(data[field]);
      }
    });

    if (fields.length === 0) {
      throw new Error('没有提供要更新的字段');
    }

    fields.push('updated_at = ?');
    params.push(now);
    params.push(id);

    const sql = `UPDATE collections SET ${fields.join(', ')} WHERE id = ?`;
    await this.run(sql, params);
    
    return await this.getCollection(id);
  }

  async deleteCollection(id) {
    const sql = 'DELETE FROM collections WHERE id = ?';
    const result = await this.run(sql, [id]);
    return result.changes > 0;
  }

  async batchUpdateCollections(updates) {
    const results = [];
    
    for (const update of updates) {
      const result = await this.updateCollection(update.id, update.data);
      results.push(result);
    }
    
    return results;
  }

  // 文档相关方法
  async addDocument(data) {
    const id = uuidv4();
    const now = new Date().toISOString();
    const size = data.content ? data.content.length : 0;
    
    const sql = `
      INSERT INTO documents (id, title, content, folder, size, collection_ids, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      id,
      data.title,
      data.content,
      data.folder || null,
      size,
      JSON.stringify(data.collectionIds || []),
      now,
      now
    ];
    
    await this.run(sql, params);
    return await this.getDocument(id);
  }

  async getDocument(id) {
    const sql = 'SELECT * FROM documents WHERE id = ?';
    const doc = await this.get(sql, [id]);
    if (doc && doc.collection_ids) {
      doc.collection_ids = JSON.parse(doc.collection_ids);
    }
    return doc;
  }

  async getDocuments() {
    const sql = 'SELECT * FROM documents ORDER BY updated_at DESC';
    const docs = await this.all(sql);
    return docs.map(doc => {
      if (doc.collection_ids) {
        doc.collection_ids = JSON.parse(doc.collection_ids);
      }
      return doc;
    });
  }

  async updateDocument(id, data) {
    const now = new Date().toISOString();
    const fields = [];
    const params = [];

    if (data.title !== undefined) {
      fields.push('title = ?');
      params.push(data.title);
    }

    if (data.content !== undefined) {
      fields.push('content = ?');
      params.push(data.content);
      // 更新文件大小
      fields.push('size = ?');
      params.push(data.content.length);
    }

    if (data.folder !== undefined) {
      fields.push('folder = ?');
      params.push(data.folder);
    }

    if (data.collectionIds !== undefined) {
      fields.push('collection_ids = ?');
      params.push(JSON.stringify(data.collectionIds));
    }

    // 支持排序相关字段
    if (data.created_at !== undefined) {
      fields.push('created_at = ?');
      params.push(data.created_at);
    }

    if (data.sort_order !== undefined) {
      fields.push('sort_order = ?');
      params.push(data.sort_order);
    }

    if (fields.length === 0) {
      throw new Error('没有提供要更新的字段');
    }

    fields.push('updated_at = ?');
    params.push(now);
    params.push(id);

    const sql = `UPDATE documents SET ${fields.join(', ')} WHERE id = ?`;
    await this.run(sql, params);
    
    return await this.getDocument(id);
  }

  async deleteDocument(id) {
    const sql = 'DELETE FROM documents WHERE id = ?';
    const result = await this.run(sql, [id]);
    return result.changes > 0;
  }

  // 统计方法
  async getStatistics() {
    const stats = {};
    
    // 收藏总数
    const totalCollections = await this.get('SELECT COUNT(*) as count FROM collections');
    stats.totalCollections = totalCollections.count;
    
    // 按类型统计
    const typeStats = await this.all(`
      SELECT type, COUNT(*) as count 
      FROM collections 
      GROUP BY type
    `);
    stats.byType = typeStats.reduce((acc, row) => {
      acc[row.type] = row.count;
      return acc;
    }, {});
    
    // 按分类统计
    const categoryStats = await this.all(`
      SELECT category, COUNT(*) as count 
      FROM collections 
      WHERE category IS NOT NULL 
      GROUP BY category
    `);
    stats.byCategory = categoryStats.reduce((acc, row) => {
      acc[row.category] = row.count;
      return acc;
    }, {});
    
    // AI处理统计
    const aiStats = await this.get(`
      SELECT 
        SUM(CASE WHEN ai_processed = 1 THEN 1 ELSE 0 END) as processed,
        SUM(CASE WHEN ai_processed = 0 THEN 1 ELSE 0 END) as unprocessed
      FROM collections
    `);
    stats.aiProcessed = aiStats.processed;
    stats.aiUnprocessed = aiStats.unprocessed;
    
    // 文档总数
    const totalDocuments = await this.get('SELECT COUNT(*) as count FROM documents');
    stats.totalDocuments = totalDocuments.count;
    
    // 最近30天的收藏统计
    const recentCollections = await this.get(`
      SELECT COUNT(*) as count 
      FROM collections 
      WHERE created_at >= datetime('now', '-30 days')
    `);
    stats.recentCollections = recentCollections.count;
    
    return stats;
  }

  // 设置相关方法
  async getSetting(key) {
    const sql = 'SELECT value FROM settings WHERE key = ?';
    const result = await this.get(sql, [key]);
    return result ? result.value : null;
  }

  async setSetting(key, value) {
    const now = new Date().toISOString();
    const sql = `
      INSERT OR REPLACE INTO settings (key, value, created_at, updated_at)
      VALUES (?, ?, COALESCE((SELECT created_at FROM settings WHERE key = ?), ?), ?)
    `;
    await this.run(sql, [key, value, key, now, now]);
  }

  async getSettings() {
    const sql = 'SELECT key, value FROM settings';
    const rows = await this.all(sql);
    return rows.reduce((acc, row) => {
      acc[row.key] = row.value;
      return acc;
    }, {});
  }

  // 搜索方法
  async search(query, options = {}) {
    const sql = `
      SELECT * FROM collections 
      WHERE content LIKE ? OR keywords LIKE ? OR summary LIKE ?
      ORDER BY 
        CASE 
          WHEN content LIKE ? THEN 1
          WHEN keywords LIKE ? THEN 2
          WHEN summary LIKE ? THEN 3
          ELSE 4
        END,
        created_at DESC
      LIMIT ?
    `;
    
    const searchParam = `%${query}%`;
    const limit = options.limit || 50;
    
    const params = [
      searchParam, searchParam, searchParam,
      searchParam, searchParam, searchParam,
      limit
    ];
    
    return await this.all(sql, params);
  }

  // 标签相关方法
  async getTags() {
    const sql = `
      SELECT 
        t.id, 
        t.name, 
        t.color, 
        t.created_at,
        COUNT(ct.collection_id) as count
      FROM tags t
      LEFT JOIN collection_tags ct ON t.id = ct.tag_id
      GROUP BY t.id, t.name, t.color, t.created_at
      ORDER BY count DESC, t.created_at DESC
    `;
    return await this.all(sql);
  }

  async addTag(name, color = '#007acc') {
    const id = uuidv4();
    const now = new Date().toISOString();
    const sql = `
      INSERT INTO tags (id, name, color, created_at)
      VALUES (?, ?, ?, ?)
    `;
    
    try {
      await this.run(sql, [id, name, color, now]);
      return { id, name, color, created_at: now };
    } catch (error) {
      if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        // 如果标签已存在，返回现有标签
        const existingTag = await this.get('SELECT * FROM tags WHERE name = ?', [name]);
        return existingTag;
      }
      throw error;
    }
  }

  async addTagToCollection(collectionId, tagName) {
    // 首先确保标签存在
    let tag = await this.get('SELECT * FROM tags WHERE name = ?', [tagName]);
    if (!tag) {
      tag = await this.addTag(tagName);
    }
    
    // 添加关联关系
    const sql = `
      INSERT OR IGNORE INTO collection_tags (collection_id, tag_id)
      VALUES (?, ?)
    `;
    
    await this.run(sql, [collectionId, tag.id]);
    return tag;
  }

  async getCollectionTags(collectionId) {
    const sql = `
      SELECT t.* FROM tags t
      JOIN collection_tags ct ON t.id = ct.tag_id
      WHERE ct.collection_id = ?
      ORDER BY t.name
    `;
    return await this.all(sql, [collectionId]);
  }

  async removeTagFromCollection(collectionId, tagId) {
    const sql = 'DELETE FROM collection_tags WHERE collection_id = ? AND tag_id = ?';
    await this.run(sql, [collectionId, tagId]);
  }

  async clearCollectionTags(collectionId) {
    const sql = 'DELETE FROM collection_tags WHERE collection_id = ?';
    await this.run(sql, [collectionId]);
  }

  // 清理方法
  async cleanup() {
    // 删除30天前的未分类收藏
    const sql = `
      DELETE FROM collections 
      WHERE category IS NULL 
      AND ai_processed = 0 
      AND created_at < datetime('now', '-30 days')
    `;
    
    const result = await this.run(sql);
    console.log(`清理了 ${result.changes} 条过期收藏`);
    
    return result.changes;
  }

  // 关闭数据库连接
  async close() {
    if (this.db) {
      return new Promise((resolve) => {
        this.db.close((err) => {
          if (err) {
            console.error('关闭数据库失败:', err);
          } else {
            console.log('数据库连接已关闭');
          }
          resolve();
        });
      });
    }
  }
}

module.exports = Database;