const { EventEmitter } = require('events');
const { <PERSON><PERSON><PERSON><PERSON>indow, Tray, Menu, nativeImage, screen, clipboard } = require('electron');
const path = require('path');
const CollectionMode = require('../modes/CollectionMode');
const OrganizationMode = require('../modes/OrganizationMode');
const Database = require('../database/Database');
const Settings = require('../services/Settings');
const AIService = require('../services/AIService');
const IPCManager = require('../utils/IPCManager');

class AppController extends EventEmitter {
  constructor() {
    super();
    this.currentMode = 'collection';
    this.collectionMode = null;
    this.organizationMode = null;
    this.database = null;
    this.settings = null;
    this.aiService = null;
    this.ipcManager = null;
    this.isInitialized = false;
    // 全局组件
    this.tray = null;
    this.quickCaptureWindow = null;
    this.collectionCount = 0;
  }

  async initialize() {
    try {
      console.log('初始化应用控制器...');
      
      // 初始化数据库
      this.database = new Database();
      await this.database.init();
      
      // 初始化设置
      this.settings = new Settings();
      await this.settings.load();
      
      // 初始化AI服务（在设置加载后）
      this.aiService = new AIService(this);
      
      // 初始化IPC管理器
      this.ipcManager = new IPCManager();
      
      // 设置全局IPC处理器
      this.ensureGlobalIPCHandlers();
      
      // 初始化模式
      this.collectionMode = new CollectionMode(this);
      this.organizationMode = new OrganizationMode(this);
      
      // 设置事件监听
      this.setupEventListeners();
      
      // 创建全局托盘
      this.createTray();
      
      // 更新收藏计数
      await this.updateCollectionCount();

      // 从设置中读取上次的模式，默认为整理模式
      const savedMode = this.settings.get('currentMode') || 'organization';
      await this.switchMode(savedMode);
      
      this.isInitialized = true;
      console.log('应用控制器初始化完成');
      
    } catch (error) {
      console.error('应用控制器初始化失败:', error);
      throw error;
    }
  }

  setupEventListeners() {
    // 监听模式切换事件
    this.on('modeChanged', (mode) => {
      console.log(`模式已切换到: ${mode}`);
      this.settings.set('currentMode', mode);
    });

    // 监听数据变化事件
    this.on('dataChanged', () => {
      // 更新全局收藏计数
      this.updateCollectionCount();
      
      // 通知所有模式数据已更新
      if (this.collectionMode) {
        this.collectionMode.onDataChanged();
      }
      if (this.organizationMode) {
        this.organizationMode.onDataChanged();
      }
    });

    // 监听设置变化事件
    this.on('settingsChanged', (key, value) => {
      console.log(`设置已更新: ${key} = ${value}`);
      this.settings.set(key, value);
    });
  }

  async switchMode(mode) {
    if (this.currentMode === mode) {
      console.log(`已在 ${mode} 模式中`);
      // 如果是整理模式，确保窗口显示
      if (mode === 'organization' && this.organizationMode && this.organizationMode.mainWindow) {
        this.organizationMode.showMainWindow();
      }
      return;
    }

    try {
      console.log(`切换模式: ${this.currentMode} -> ${mode}`);
      
      // 验证模式有效性
      if (!['collection', 'organization'].includes(mode)) {
        throw new Error(`无效的模式: ${mode}`);
      }
      
      // 显示切换动画
      this.showTransitionOverlay();
      
      // 停用当前模式
      if (this.currentMode && this[this.currentMode + 'Mode']) {
        await this[this.currentMode + 'Mode'].deactivate();
      }
      
      // 清除当前模式的IPC处理器
      if (this.ipcManager) {
        // 只清除模式特定的处理器，保留全局的
        this.clearModeSpecificHandlers();
      }
      
      // 确保全局IPC处理器存在
      this.ensureGlobalIPCHandlers();
      
      // 切换到新模式
      const previousMode = this.currentMode;
      this.currentMode = mode;
      
      try {
        // 先激活新模式，确保IPC处理器被重新注册
        await this[mode + 'Mode'].activate();
        
        // 确保全局IPC处理器存在
        this.ensureGlobalIPCHandlers();
        
      } catch (activationError) {
        // 如果新模式激活失败，尝试恢复到之前的模式
        console.error(`激活${mode}模式失败:`, activationError);
        this.currentMode = previousMode;
        if (this[previousMode + 'Mode']) {
          await this[previousMode + 'Mode'].activate();
        }
        throw activationError;
      }
      
      // 更新托盘状态
      this.updateTrayTooltip();
      this.createTrayMenu();
      
      // 隐藏切换动画
      this.hideTransitionOverlay();
      
      // 发送模式切换事件
      this.emit('modeChanged', mode);
      
      // 更新设置
      this.settings.set('currentMode', mode);
      
      console.log(`模式切换完成: ${mode}`);
      
    } catch (error) {
      console.error('模式切换失败:', error);
      this.hideTransitionOverlay();
      
      // 如果不是收集模式且切换失败，尝试恢复到收集模式
      if (mode !== 'collection' && this.currentMode !== 'collection') {
        try {
          await this.switchMode('collection');
        } catch (fallbackError) {
          console.error('恢复到收集模式也失败:', fallbackError);
        }
      }
      
      throw error;
    }
  }

  clearModeSpecificHandlers() {
    // 定义需要在模式切换时清除的处理器
    // 移除 quick-capture 和 switch-to-organization，它们应该在所有模式下都可用
    const modeSpecificHandlers = [
      'save-collection',
      // 'get-collections', // FIX: Promoted to a global handler
      'get-collection',
      'update-collection',
      'delete-collection',
      'analyze-collections',
      'generate-markdown'
    ];
    
    modeSpecificHandlers.forEach(handler => {
      if (this.ipcManager.hasHandler(handler)) {
        this.ipcManager.removeHandler(handler);
      }
    });
  }

  ensureGlobalIPCHandlers() {
    const { clipboard } = require('electron');
    
    // 快速捕获处理器
    if (!this.ipcManager.hasHandler('quick-capture')) {
      this.ipcManager.handle('quick-capture', async (event, data) => {
        try {
          const collectionData = {
            content: data.content,
            type: data.type || 'text',
            source: data.source || 'quick-capture',
            timestamp: new Date().toISOString()
          };
          
          // 如果有图片数据，添加到收藏数据中
          if (data.imageData) {
            collectionData.imageData = data.imageData;
          }
          
          const result = await this.addCollection(collectionData);
          
          // 如果有标签，添加标签关联
          if (data.tags && data.tags.length > 0) {
            for (const tagName of data.tags) {
              await this.database.addTagToCollection(result.id, tagName);
            }
          }
          
          return result;
        } catch (error) {
          console.error('快速捕获失败:', error);
          throw error;
        }
      });
    }
    
    // 模式切换处理器
    if (!this.ipcManager.hasHandler('switch-to-organization')) {
      this.ipcManager.handle('switch-to-organization', async (event) => {
        try {
          await this.switchMode('organization');
          return true;
        } catch (error) {
          console.error('切换到整理模式失败:', error);
          throw error;
        }
      });
    }
    
    // 剪贴板处理器
    if (!this.ipcManager.hasHandler('get-clipboard-content')) {
      this.ipcManager.handle('get-clipboard-content', () => {
        const text = clipboard.readText();
        const image = clipboard.readImage();
        
        return {
          text,
          hasImage: !image.isEmpty(),
          image: image.isEmpty() ? null : image.toDataURL()
        };
      });
    }
    
    if (!this.ipcManager.hasHandler('set-clipboard-content')) {
      this.ipcManager.handle('set-clipboard-content', (event, content) => {
        clipboard.writeText(content);
      });
    }
    
    // 获取可用标签
    if (!this.ipcManager.hasHandler('get-available-tags')) {
      this.ipcManager.handle('get-available-tags', async (event) => {
        try {
          // 从数据库获取真实的标签数据
          const tags = await this.database.getTags();
          return tags || [];
        } catch (error) {
          console.error('获取标签失败:', error);
          return [];
        }
      });
    }

    // 添加标签到收藏
    if (!this.ipcManager.hasHandler('add-tag-to-collection')) {
      this.ipcManager.handle('add-tag-to-collection', async (event, collectionId, tagName) => {
        try {
          return await this.database.addTagToCollection(collectionId, tagName);
        } catch (error) {
          console.error('添加标签到收藏失败:', error);
          throw error;
        }
      });
    }

    // 清除收藏的所有标签
    if (!this.ipcManager.hasHandler('clear-collection-tags')) {
      this.ipcManager.handle('clear-collection-tags', async (event, collectionId) => {
        try {
          return await this.database.clearCollectionTags(collectionId);
        } catch (error) {
          console.error('清除收藏标签失败:', error);
          throw error;
        }
      });
    }

    if (!this.ipcManager.hasHandler('get-collections')) {
      this.ipcManager.handle('get-collections', async (event, options) => {
        return await this.database.getCollections(options);
      });
    }
  }

  showTransitionOverlay() {
    // 显示模式切换动画
    // 这里可以添加视觉效果
    console.log('显示模式切换动画');
  }

  hideTransitionOverlay() {
    // 隐藏模式切换动画
    console.log('隐藏模式切换动画');
  }

  // 获取收藏数据
  async getCollections(options = {}) {
    return await this.database.getCollections(options);
  }

  // 添加收藏
  async addCollection(data) {
    try {
      const result = await this.database.addCollection(data);
      this.emit('dataChanged');
      return result;
    } catch (error) {
      console.error('添加收藏失败:', error);
      throw error;
    }
  }

  // 更新收藏
  async updateCollection(id, data) {
    try {
      const result = await this.database.updateCollection(id, data);
      this.emit('dataChanged');
      return result;
    } catch (error) {
      console.error('更新收藏失败:', error);
      throw error;
    }
  }

  // 删除收藏
  async deleteCollection(id) {
    try {
      const result = await this.database.deleteCollection(id);
      this.emit('dataChanged');
      return result;
    } catch (error) {
      console.error('删除收藏失败:', error);
      throw error;
    }
  }

  // 批量AI分析
  async analyzeCollections(items) {
    return await this.aiService.analyzeCollections(items);
  }

  // 生成Markdown
  async generateMarkdown(categories, items) {
    return await this.aiService.generateMarkdown(categories, items);
  }

  // 获取统计信息
  async getStatistics() {
    return await this.database.getStatistics();
  }

  // 导出数据
  async exportData(format = 'json') {
    const collections = await this.getCollections();
    const documents = await this.database.getDocuments();
    
    const data = {
      collections,
      documents,
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    };

    switch (format) {
      case 'json':
        return JSON.stringify(data, null, 2);
      case 'csv':
        return this.convertToCSV(data);
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
  }

  convertToCSV(data) {
    // 简单的CSV转换实现
    const header = ['ID', 'Content', 'Type', 'Source', 'Timestamp', 'Category'];
    const rows = data.collections.map(item => [
      item.id,
      item.content.replace(/,/g, ';'),
      item.type,
      item.source || '',
      item.timestamp,
      item.category || ''
    ]);
    
    return [header, ...rows].map(row => row.join(',')).join('\n');
  }

  // 创建全局托盘
  createTray() {
    try {
      let trayIcon;
      
      try {
        // 尝试使用项目图标
        const iconPath = path.join(__dirname, '../assets/tray-icon.png');
        trayIcon = nativeImage.createFromPath(iconPath);
        
        if (trayIcon.isEmpty()) {
          throw new Error('图标文件为空');
        }
      } catch (error) {
        // 创建一个简单的SVG图标
        const size = process.platform === 'win32' ? 16 : 22;
        trayIcon = nativeImage.createFromDataURL(
          `data:image/svg+xml;base64,${Buffer.from(`
            <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
              <rect width="${size}" height="${size}" fill="#3182ce"/>
              <text x="50%" y="70%" text-anchor="middle" fill="white" font-size="12" font-family="Arial">Q</text>
            </svg>
          `).toString('base64')}`
        );
        
        if (process.platform === 'darwin') {
          trayIcon.setTemplateImage(true);
        }
      }
      
      this.tray = new Tray(trayIcon);
      
      // 设置托盘提示
      this.updateTrayTooltip();
      
      // 创建托盘菜单
      this.createTrayMenu();
      
      // 托盘点击事件
      this.tray.on('click', () => {
        this.showQuickCapture();
      });
      
      // 托盘右键菜单
      this.tray.on('right-click', () => {
        this.tray.popUpContextMenu();
      });
      
      console.log('全局托盘创建完成');
      
    } catch (error) {
      console.error('托盘创建失败:', error);
    }
  }

  createTrayMenu() {
    if (!this.tray) {
      return;
    }
    
    const template = [
      {
        label: 'QNotes - 内容收集器',
        enabled: false
      },
      { type: 'separator' },
      {
        label: '收集模式',
        checked: this.currentMode === 'collection',
        type: 'radio',
        click: () => {
          this.switchMode('collection');
        }
      },
      {
        label: '整理模式',
        checked: this.currentMode === 'organization',
        type: 'radio',
        click: () => {
          this.switchMode('organization');
        }
      },
      { type: 'separator' },
      {
        label: '快速收藏',
        accelerator: 'Control+Option+Command+N',
        click: () => {
          this.showQuickCapture();
        }
      },
      { type: 'separator' },
      {
        label: `统计 (已收藏 ${this.collectionCount} 项)`,
        click: () => {
          this.showStatistics();
        }
      },
      { type: 'separator' },
      {
        label: '设置',
        click: () => {
          this.showSettings();
        }
      },
      {
        label: '退出',
        click: () => {
          process.exit(0);
        }
      }
    ];
    
    const contextMenu = Menu.buildFromTemplate(template);
    this.tray.setContextMenu(contextMenu);
  }

  updateTrayTooltip() {
    if (!this.tray) {
      return;
    }
    
    const modeText = this.currentMode === 'collection' ? '收集模式' : '整理模式';
    const tooltip = `QNotes - ${modeText}\n已收藏 ${this.collectionCount} 项`;
    this.tray.setToolTip(tooltip);
  }

  async updateCollectionCount() {
    try {
      const stats = await this.getStatistics();
      this.collectionCount = stats.totalCollections || 0;
      this.updateTrayTooltip();
      this.createTrayMenu();
    } catch (error) {
      console.error('更新收藏计数失败:', error);
    }
  }

  showQuickCapture() {
    if (this.quickCaptureWindow) {
      this.quickCaptureWindow.focus();
      return;
    }
    
    const display = screen.getPrimaryDisplay();
    const { width, height } = display.workAreaSize;
    
    this.quickCaptureWindow = new BrowserWindow({
      width: 520,
      height: 520,
      x: Math.round((width - 520) / 2),
      y: Math.round((height - 520) / 2),
      frame: false,
      transparent: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });
    
    this.quickCaptureWindow.loadFile(path.join(__dirname, '../ui/quick-capture.html'));
    
    this.quickCaptureWindow.on('closed', () => {
      this.quickCaptureWindow = null;
    });
    
    // 失去焦点时关闭
    this.quickCaptureWindow.on('blur', () => {
      setTimeout(() => {
        if (this.quickCaptureWindow) {
          this.quickCaptureWindow.close();
        }
      }, 100);
    });
  }

  showStatistics() {
    // 根据当前模式显示统计
    if (this.currentMode === 'collection' && this.collectionMode) {
      this.collectionMode.showStatistics();
    } else if (this.currentMode === 'organization' && this.organizationMode) {
      this.organizationMode.showStatistics();
    }
  }

  showSettings() {
    // 切换到整理模式并显示设置
    this.switchMode('organization').then(() => {
      if (this.organizationMode) {
        this.organizationMode.showSettings();
      }
    });
  }

  // 清理资源
  async cleanup() {
    try {
      console.log('清理应用资源...');
      
      // 关闭快速收藏窗口
      if (this.quickCaptureWindow) {
        this.quickCaptureWindow.close();
        this.quickCaptureWindow = null;
      }
      
      // 销毁托盘
      if (this.tray) {
        this.tray.destroy();
        this.tray = null;
      }
      
      // 停用当前模式
      if (this.currentMode && this[this.currentMode + 'Mode']) {
        await this[this.currentMode + 'Mode'].deactivate();
      }
      
      // 保存设置
      if (this.settings) {
        await this.settings.save();
      }
      
      // 关闭数据库
      if (this.database) {
        await this.database.close();
      }
      
      console.log('应用资源清理完成');
      
    } catch (error) {
      console.error('清理资源失败:', error);
    }
  }
}

module.exports = AppController;