const { ipcMain } = require('electron');

class IPCManager {
    constructor() {
        this.handlers = new Map();
    }

    // 安全地注册IPC处理器
    handle(channel, handler) {
        // 如果已经存在处理器，先移除
        if (this.handlers.has(channel)) {
            ipcMain.removeHandler(channel);
        }

        // 注册新的处理器
        ipcMain.handle(channel, handler);
        this.handlers.set(channel, handler);

        console.log(`IPC处理器已注册: ${channel}`);
    }

    // 移除IPC处理器
    removeHandler(channel) {
        if (this.handlers.has(channel)) {
            ipcMain.removeHandler(channel);
            this.handlers.delete(channel);
            console.log(`IPC处理器已移除: ${channel}`);
        }
    }

    // 清除所有处理器
    clearAll() {
        for (const channel of this.handlers.keys()) {
            ipcMain.removeHandler(channel);
        }
        this.handlers.clear();
        console.log('所有IPC处理器已清除');
    }

    // 获取已注册的处理器列表
    getHandlers() {
        return Array.from(this.handlers.keys());
    }

    // 检查处理器是否存在
    hasHandler(channel) {
        return this.handlers.has(channel);
    }
}

module.exports = IPCManager;