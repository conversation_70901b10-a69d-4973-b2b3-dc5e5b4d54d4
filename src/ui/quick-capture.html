<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速收藏</title>
    <link rel="stylesheet" href="styles/quick-capture.css">
</head>
<body>
    <div class="capture-window">
        <div class="window-header">
            <div class="window-title">快速收藏</div>
            <button id="closeBtn" class="close-button">
                <svg width="12" height="12" viewBox="0 0 12 12">
                    <path d="M1 1L11 11M11 1L1 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                </svg>
            </button>
        </div>
        
        <div class="content-area">
            <!-- 内容预览区域 -->
            <div id="contentPreview" class="content-preview" style="display: none;">
                <div class="preview-label">内容预览</div>
                <div id="previewContent" class="preview-content"></div>
            </div>
            
            <!-- 内容输入区域 -->
            <div class="input-section">
                <textarea 
                    id="contentInput" 
                    placeholder="输入要收藏的内容，或直接按 Enter 保存剪贴板内容..."
                    class="content-input"
                    rows="4"
                ></textarea>
                
                <!-- 标签输入 -->
                <div class="tag-section">
                    <div class="tag-label">标签</div>
                    <div class="tag-input-container">
                        <div class="tag-list" id="tagList"></div>
                        <input 
                            type="text" 
                            id="sourceInput" 
                            placeholder="输入新标签或点击下方标签"
                            class="tag-input"
                            autocomplete="off"
                        >
                        <div class="tag-suggestions" id="tagSuggestions" style="display: none;"></div>
                    </div>
                    <div class="recent-tags" id="recentTags"></div>
                </div>
            </div>
        </div>
        
        <div class="action-bar">
            <div class="shortcuts-hint">
                <span>⌘ + Enter 保存 • Esc 取消</span>
            </div>
            <div class="action-buttons">
                <button id="clearBtn" class="btn-secondary">清除</button>
                <button id="saveBtn" class="btn-primary">保存</button>
            </div>
        </div>
        
        <!-- 成功状态覆盖层 -->
        <div id="successOverlay" class="success-overlay" style="display: none;">
            <div class="success-content">
                <button class="success-close" onclick="quickCapture.hideSuccess(); quickCapture.closeWindow();">×</button>
                <div class="success-checkmark">
                    <svg width="48" height="48" viewBox="0 0 48 48">
                        <circle cx="24" cy="24" r="22" fill="#34D399" opacity="0.1"/>
                        <circle cx="24" cy="24" r="18" fill="#34D399"/>
                        <path d="M16 24L22 30L32 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="success-text">已保存到收藏</div>
                <div class="success-actions">
                    <button id="continueBtn" class="btn-secondary">关闭</button>
                    <button id="organizeBtn" class="btn-primary">整理模式</button>
                </div>
                <div class="success-hint">1.5秒后自动关闭 • Esc 关闭</div>
            </div>
        </div>
    </div>
    
    <script src="scripts/quick-capture.js"></script>
</body>
</html>