class FilesManager {
    constructor(app) {
        this.app = app;
        this.files = [];
        this.selectedFile = null;
        this.openFolders = new Set();

        // 延迟绑定事件，确保DOM已加载
        setTimeout(() => this.bindEvents(), 0);
    }

    bindEvents() {
        // 文件管理按钮
        document.getElementById('newFolderBtn')?.addEventListener('click', () => this.createNewFolder());
        document.getElementById('newFileBtn')?.addEventListener('click', () => this.createNewFile());

        // 持久搜索功能
        const searchInput = document.getElementById('filesSearchInput');
        const clearBtn = document.getElementById('clearSearchBtn');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleFilesSearch(e.target.value));
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.clearFilesSearch();
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearFilesSearch());
        }
    }

    async loadFiles() {
        const filesTree = document.getElementById('filesTree');
        if (filesTree) {
            filesTree.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>加载文件中...</p>
                </div>
            `;
        }

        try {
            console.log('开始加载文件...');
            const files = await ipcRenderer.invoke('get-documents');
            console.log('获取到文件数据:', files);

            // 确保文件数据是有效的
            this.files = Array.isArray(files) ? files.filter(file => file && file.id) : [];
            this.app.files = this.files;

            console.log('过滤后的文件数量:', this.files.length);

            // 如果没有文件，创建一个默认的欢迎文件
            if (this.files.length === 0) {
                console.log('没有文件，创建欢迎文件...');
                await this.createWelcomeFile();
            }

            this.renderFiles();
        } catch (error) {
            console.error('加载文件失败:', error);
            this.files = [];
            this.app.files = this.files;
            this.renderFiles();
            CoreUtils.showNotification('加载文件失败: ' + error.message, 'error');
        }
    }

    async createWelcomeFile() {
        try {
            const welcomeContent = `# 欢迎使用 QNotes

这是您的第一个文档！

## 功能介绍

- 📝 **Markdown 编辑**：支持实时预览和语法高亮
- 📁 **文件夹管理**：创建文件夹来组织您的文档
- 🔍 **智能搜索**：快速找到您需要的内容
- 🤖 **AI 助手**：帮助您改进和处理文本
- 💾 **自动保存**：每10秒自动保存，再也不用担心丢失内容
- ↩️ **撤销重做**：支持 Cmd+Z 撤销操作

## 开始使用

1. 点击左侧的"新建文件"按钮创建新文档
2. 点击"新建文件夹"按钮来组织您的文档
3. 使用右侧的 AI 面板来获得写作帮助

祝您使用愉快！`;

            const result = await ipcRenderer.invoke('create-document', {
                title: '欢迎使用 QNotes',
                content: welcomeContent,
                folder: null
            });

            if (result) {
                // 重新加载文件列表
                const files = await ipcRenderer.invoke('get-documents');
                this.files = Array.isArray(files) ? files : [];
                this.app.files = this.files;

                // 自动打开欢迎文件
                if (this.app.editorManager) {
                    setTimeout(() => {
                        this.app.editorManager.loadDocument(result.id);
                    }, 100);
                }
            }
        } catch (error) {
            console.error('创建欢迎文件失败:', error);
        }
    }

    renderFiles() {
        // 防止重复渲染
        if (this.isRendering) {
            return;
        }

        this.isRendering = true;

        try {
            console.log('开始渲染文件列表...');
            const container = document.getElementById('filesTree');
            if (!container) {
                console.error('找不到filesTree容器');
                return;
            }

            if (!this.files || this.files.length === 0) {
                console.log('没有文件，显示空状态');
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>暂无文件</h3>
                        <p>点击"新建文件"按钮创建第一个文档</p>
                    </div>
                `;
                return;
            }

            console.log('开始分组文件...');
            const filesByFolder = this.groupFilesByFolder(this.files);
            console.log('文件分组完成:', filesByFolder);

            console.log('开始渲染文件树...');
            const html = this.renderFileTree(filesByFolder);
            console.log('文件树HTML生成完成，长度:', html.length);

            container.innerHTML = html;
            console.log('HTML已插入到容器');

            this.bindFileEvents();
            console.log('文件事件绑定完成');
        } catch (error) {
            console.error('渲染文件列表失败:', error);
            const container = document.getElementById('filesTree');
            if (container) {
                container.innerHTML = `
                    <div class="error-state">
                        <h3>加载失败</h3>
                        <p>文件列表渲染出错: ${error.message}</p>
                        <button onclick="organizationApp.filesManager.loadFiles()">重试</button>
                    </div>
                `;
            }
        } finally {
            // 延迟重置渲染标志，防止快速连续调用
            setTimeout(() => {
                this.isRendering = false;
            }, 100);
        }
    }

    groupFilesByFolder(files) {
        const grouped = {};
        const allFolders = new Set();

        // 第一遍：收集所有文件夹名称
        files.forEach(file => {
            if (file.title && file.title.startsWith('.folder_config') && file.folder) {
                allFolders.add(file.folder);
            } else if (file.folder) {
                allFolders.add(file.folder);
            }
        });

        // 第二遍：分组文件，跳过配置文件
        files.forEach(file => {
            // 跳过隐藏的配置文件
            if (file.title && file.title.startsWith('.folder_config')) {
                return;
            }

            const folder = file.folder || 'root';
            if (!grouped[folder]) {
                grouped[folder] = [];
            }
            grouped[folder].push(file);
        });

        // 确保所有文件夹都在grouped中，即使是空文件夹
        allFolders.forEach(folderName => {
            if (!grouped[folderName]) {
                grouped[folderName] = [];
            }
        });

        return grouped;
    }

    renderFileTree(filesByFolder) {
        let html = '';

        // 渲染根文件夹的文件
        if (filesByFolder.root && filesByFolder.root.length > 0) {
            // 按创建时间倒序排列
            const sortedRootFiles = this.sortFilesByDate(filesByFolder.root);
            html += sortedRootFiles.map(file => this.renderFileItem(file)).join('');
        }

        // 渲染所有文件夹（包括空文件夹）
        let folderNames = Object.keys(filesByFolder)
            .filter(folder => folder !== 'root');

        // 尝试从本地存储获取文件夹顺序
        try {
            const savedOrder = localStorage.getItem('qnotes-folder-order');
            if (savedOrder) {
                const orderArray = JSON.parse(savedOrder);
                // 按保存的顺序排列，新文件夹添加到末尾
                const orderedFolders = [];
                orderArray.forEach(folder => {
                    if (folderNames.includes(folder)) {
                        orderedFolders.push(folder);
                    }
                });
                // 添加不在保存顺序中的新文件夹
                folderNames.forEach(folder => {
                    if (!orderedFolders.includes(folder)) {
                        orderedFolders.push(folder);
                    }
                });
                folderNames = orderedFolders;
            } else {
                folderNames.sort(); // 默认按字母顺序排列
            }
        } catch (error) {
            console.warn('读取文件夹顺序失败:', error);
            folderNames.sort(); // 默认按字母顺序排列
        }

        folderNames.forEach(folder => {
            const folderFiles = this.sortFilesByDate(filesByFolder[folder]);
            html += this.renderFolderItem(folder, folderFiles);
        });

        return html;
    }

    sortFilesByDate(files) {
        return files.sort((a, b) => {
            const dateA = new Date(a.created_at || a.createdAt || 0);
            const dateB = new Date(b.created_at || b.createdAt || 0);
            return dateB - dateA; // 倒序排列，新文件在前
        });
    }

    renderFileItem(file, isInFolder = false) {
        if (!file || !file.id) {
            console.warn('无效的文件数据:', file);
            return '';
        }

        const selected = this.selectedFile === file.id;
        const indentClass = isInFolder ? 'file-item-nested' : '';

        // 安全地处理日期
        let dateText = '未知时间';
        try {
            const timestamp = file.created_at || file.createdAt || file.updated_at || Date.now();
            const fileDate = new Date(timestamp);
            const isToday = this.isToday(fileDate);
            dateText = isToday ? CoreUtils.formatTime(timestamp) : CoreUtils.formatDate(timestamp);
        } catch (error) {
            console.warn('日期格式化失败:', error);
        }

        return `
            <div class="file-item ${selected ? 'selected' : ''} ${indentClass}" data-id="${file.id}" draggable="true">
                <div class="file-content">
                    <div class="file-icon-wrapper">
                        <svg class="file-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                    </div>
                    <div class="file-info">
                        <div class="file-name" title="${file.title || '未命名文档'}">${file.title || '未命名文档'}</div>
                        <div class="file-meta">
                            <span class="file-date">${dateText}</span>
                            <span class="file-size">${CoreUtils.formatFileSize(file.size || file.content?.length || 0)}</span>
                        </div>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="file-action-btn edit-btn" onclick="organizationApp.filesManager.renameFile('${file.id}')" title="重命名文件">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                            <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                        </svg>
                    </button>
                    <button class="file-action-btn duplicate-btn" onclick="organizationApp.filesManager.duplicateFile('${file.id}')" title="复制文件">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                        </svg>
                    </button>
                    <button class="file-action-btn delete-btn" onclick="organizationApp.filesManager.deleteFile('${file.id}')" title="删除文件">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="3,6 5,6 21,6"/>
                            <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1 2-2h4a2,2 0 0,1 2,2v2"/>
                            <line x1="10" y1="11" x2="10" y2="17"/>
                            <line x1="14" y1="11" x2="14" y2="17"/>
                        </svg>
                    </button>
                </div>
            </div>
        `;
    }

    isToday(date) {
        const today = new Date();
        return date.toDateString() === today.toDateString();
    }

    renderFolderItem(folderName, files) {
        const isOpen = this.openFolders.has(folderName);
        const chevronIcon = isOpen ?
            `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
            </svg>` :
            `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
            </svg>`;

        const folderIcon = isOpen ?
            `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,20H4C2.89,20 2,19.1 2,18V6C2,4.89 2.89,4 4,4H10L12,6H19A2,2 0 0,1 21,8H21L4,8V18L6.14,10H23.21L20.93,18.5C20.7,19.37 19.92,20 19,20Z"/>
            </svg>` :
            `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z"/>
            </svg>`;

        let html = `
            <div class="folder-item ${isOpen ? 'open' : ''}" data-folder="${folderName}" draggable="true">
                <div class="folder-header">
                    <div class="folder-toggle">
                        ${chevronIcon}
                    </div>
                    <div class="folder-icon-wrapper">
                        ${folderIcon}
                    </div>
                    <div class="folder-info">
                        <span class="folder-name">${folderName}</span>
                        <span class="folder-count">${files.length} 个文件</span>
                    </div>
                    <div class="folder-actions">
                        <button class="folder-action-btn add-file-btn" data-action="add-file" data-folder="${folderName}" title="新建文件">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="12" y1="18" x2="12" y2="12"/>
                                <line x1="9" y1="15" x2="15" y2="15"/>
                            </svg>
                        </button>
                        <button class="folder-action-btn rename-btn" data-action="rename-folder" data-folder="${folderName}" title="重命名文件夹">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                            </svg>
                        </button>
                        <button class="folder-action-btn delete-btn" data-action="delete-folder" data-folder="${folderName}" title="删除文件夹">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"/>
                                <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1 2-2h4a2,2 0 0,1 2,2v2"/>
                                <line x1="10" y1="11" x2="10" y2="17"/>
                                <line x1="14" y1="11" x2="14" y2="17"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="folder-content ${isOpen ? 'expanded' : 'collapsed'}">
                    ${isOpen ? files.map(file => this.renderFileItem(file, true)).join('') : ''}
                </div>
            </div>
        `;

        return html;
    }

    bindFileEvents() {
        // 移除之前的事件监听器，避免重复绑定
        const container = document.getElementById('filesTree');
        if (container) {
            // 移除旧的事件监听器
            const oldHandler = container._clickHandler;
            const oldContextHandler = container._contextHandler;

            if (oldHandler) {
                container.removeEventListener('click', oldHandler);
            }
            if (oldContextHandler) {
                container.removeEventListener('contextmenu', oldContextHandler);
            }

            // 创建新的事件处理器
            const clickHandler = (e) => this.handleFileClick(e);
            const contextHandler = (e) => this.handleFileContextMenu(e);

            // 保存引用以便后续移除
            container._clickHandler = clickHandler;
            container._contextHandler = contextHandler;

            container.addEventListener('click', clickHandler);
            container.addEventListener('contextmenu', contextHandler);
        }

        // 绑定拖拽事件（这些需要直接绑定到元素上）
        const fileItems = document.querySelectorAll('.file-item');
        fileItems.forEach(item => {
            this.bindDragEvents(item);
        });

        // 绑定文件夹事件
        const folderItems = document.querySelectorAll('.folder-item');
        folderItems.forEach(item => {
            // 拖拽目标事件
            this.bindDropEvents(item);
            // 文件夹拖拽事件
            this.bindFolderDragEvents(item);
        });

        // 添加全局拖拽调试
        if (!this.globalDragListenerAdded) {
            document.addEventListener('dragstart', (e) => {
                console.log('Global dragstart:', e.target);
            });
            document.addEventListener('dragover', (e) => {
                if (e.dataTransfer.types.includes('text/plain')) {
                    // console.log('Global dragover:', e.target);
                }
            });
            document.addEventListener('drop', (e) => {
                console.log('Global drop:', e.target);
            });
            this.globalDragListenerAdded = true;
        }
    }

    handleFileClick(e) {
        const fileItem = e.target.closest('.file-item');
        const folderHeader = e.target.closest('.folder-header');
        const folderActionBtn = e.target.closest('.folder-action-btn');

        // 处理文件夹操作按钮点击
        if (folderActionBtn) {
            e.stopPropagation();
            e.preventDefault();

            const action = folderActionBtn.dataset.action;
            const folderName = folderActionBtn.dataset.folder;

            switch (action) {
                case 'add-file':
                    this.createNewFileInFolder(folderName);
                    break;
                case 'rename-folder':
                    this.renameFolder(folderName);
                    break;
                case 'delete-folder':
                    this.deleteFolder(folderName);
                    break;
            }
            return;
        }

        if (fileItem && !e.target.closest('.file-actions')) {
            e.stopPropagation();
            const fileId = fileItem.dataset.id;
            if (fileId) {
                this.selectFile(fileId);
                this.openFile(fileId);
            }
        } else if (folderHeader) {
            e.stopPropagation();
            e.preventDefault(); // 防止默认行为
            const folderItem = folderHeader.closest('.folder-item');
            if (folderItem && folderItem.dataset.folder) {
                this.toggleFolder(folderItem.dataset.folder);
            }
        }
    }

    handleFileContextMenu(e) {
        const fileItem = e.target.closest('.file-item');
        const folderHeader = e.target.closest('.folder-header');

        if (fileItem) {
            e.preventDefault();
            const fileId = fileItem.dataset.id;
            if (fileId) {
                this.showFileContextMenu(e, fileId);
            }
        } else if (folderHeader) {
            e.preventDefault();
            const folderItem = folderHeader.closest('.folder-item');
            if (folderItem) {
                this.showFolderContextMenu(e, folderItem.dataset.folder);
            }
        }
    }

    bindDragEvents(fileItem) {
        // 确保元素是可拖拽的
        fileItem.draggable = true;

        fileItem.addEventListener('dragstart', (e) => {
            const fileId = fileItem.dataset.id;
            console.log('Drag start for file:', fileId);

            // 设置拖拽数据
            const dragData = {
                type: 'file',
                id: fileId
            };

            e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
            e.dataTransfer.effectAllowed = 'move';

            fileItem.classList.add('dragging');

            // 简化拖拽预览
            e.dataTransfer.setDragImage(fileItem, 0, 0);
        });

        fileItem.addEventListener('dragend', (e) => {
            console.log('File drag end for:', fileItem.dataset.id);
            fileItem.classList.remove('dragging');
            // 清除所有拖拽目标高亮
            document.querySelectorAll('.drop-target, .drop-before, .drop-after').forEach(el => {
                el.classList.remove('drop-target', 'drop-before', 'drop-after');
            });
        });

        // 文件之间的排序
        fileItem.addEventListener('dragover', (e) => {
            if (e.dataTransfer.types.includes('text/plain')) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';

                // 显示插入位置指示器
                const rect = fileItem.getBoundingClientRect();
                const midY = rect.top + rect.height / 2;

                if (e.clientY < midY) {
                    fileItem.classList.add('drop-before');
                    fileItem.classList.remove('drop-after');
                } else {
                    fileItem.classList.add('drop-after');
                    fileItem.classList.remove('drop-before');
                }
            }
        });

        fileItem.addEventListener('dragleave', (e) => {
            // 只有当鼠标真正离开元素时才移除样式
            if (!fileItem.contains(e.relatedTarget)) {
                fileItem.classList.remove('drop-before', 'drop-after');
            }
        });

        fileItem.addEventListener('drop', (e) => {
            console.log('File drop event triggered on:', fileItem.dataset.id);
            e.preventDefault();
            e.stopPropagation();
            fileItem.classList.remove('drop-before', 'drop-after');

            try {
                const dragDataStr = e.dataTransfer.getData('text/plain');
                console.log('Drop event - raw data:', dragDataStr);

                if (!dragDataStr) {
                    console.warn('No drag data found');
                    return;
                }

                const dragData = JSON.parse(dragDataStr);
                console.log('Drop event - parsed data:', dragData);

                if (dragData.type === 'file') {
                    console.log('Calling reorderFile:', dragData.id, '->', fileItem.dataset.id);
                    // 文件排序
                    this.reorderFile(dragData.id, fileItem.dataset.id, e);
                } else {
                    console.log('Not a file drag operation, ignoring');
                }
            } catch (error) {
                console.error('拖拽数据解析失败:', error);
                console.log('Raw drag data:', e.dataTransfer.getData('text/plain'));
            }
        });
    }

    bindDropEvents(folderItem) {
        folderItem.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            folderItem.classList.add('drop-target');
        });

        folderItem.addEventListener('dragleave', (e) => {
            // 只有当离开整个文件夹区域时才移除高亮
            if (!folderItem.contains(e.relatedTarget)) {
                folderItem.classList.remove('drop-target');
            }
        });

        folderItem.addEventListener('drop', (e) => {
            e.preventDefault();
            const dragDataStr = e.dataTransfer.getData('text/plain');
            const targetFolder = folderItem.dataset.folder;

            folderItem.classList.remove('drop-target');

            try {
                const dragData = JSON.parse(dragDataStr);
                if (dragData.type === 'file' && targetFolder) {
                    this.moveFileToFolder(dragData.id, targetFolder);
                }
            } catch (error) {
                // 兼容旧格式（直接是文件ID）
                if (dragDataStr && targetFolder) {
                    this.moveFileToFolder(dragDataStr, targetFolder);
                }
            }
        });

        // 支持拖拽到根目录
        const filesTree = document.getElementById('filesTree');
        if (filesTree && !filesTree.hasAttribute('data-drop-bound')) {
            filesTree.setAttribute('data-drop-bound', 'true');

            filesTree.addEventListener('dragover', (e) => {
                // 只有当拖拽到空白区域时才允许放置到根目录
                if (e.target === filesTree || e.target.closest('.file-item, .folder-item') === null) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                }
            });

            filesTree.addEventListener('drop', (e) => {
                if (e.target === filesTree || e.target.closest('.file-item, .folder-item') === null) {
                    e.preventDefault();
                    const dragDataStr = e.dataTransfer.getData('text/plain');

                    try {
                        const dragData = JSON.parse(dragDataStr);
                        if (dragData.type === 'file') {
                            this.moveFileToFolder(dragData.id, null); // null 表示根目录
                        }
                    } catch (error) {
                        // 兼容旧格式（直接是文件ID）
                        if (dragDataStr) {
                            this.moveFileToFolder(dragDataStr, null);
                        }
                    }
                }
            });
        }
    }

    selectFile(fileId) {
        // 移除之前的选中状态
        const previousSelected = document.querySelector('.file-item.selected');
        if (previousSelected) {
            previousSelected.classList.remove('selected');
        }

        // 添加新的选中状态
        this.selectedFile = fileId;
        const newSelected = document.querySelector(`.file-item[data-id="${fileId}"]`);
        if (newSelected) {
            newSelected.classList.add('selected');
        }
    }

    async openFile(fileId) {
        try {
            const file = this.files.find(f => f.id === fileId);
            if (!file) return;
            
            // 加载文件内容到编辑器
            const content = await ipcRenderer.invoke('get-document-content', fileId);
            const editor = document.getElementById('markdownEditor');
            if (editor) {
                editor.value = content || '';
                editor.dispatchEvent(new Event('input'));
            }
            
            this.app.currentDocument = file;
            this.app.editorContent = content || '';
            
            // 更新文档标题
            const titleInput = document.getElementById('documentTitle');
            if (titleInput) {
                titleInput.value = file.title || '';
            }
            
            CoreUtils.showNotification('文件已打开', 'success');
            
        } catch (error) {
            console.error('打开文件失败:', error);
            CoreUtils.showNotification('打开文件失败: ' + error.message, 'error');
        }
    }

    async renameFile(fileId) {
        const file = this.files.find(f => f.id === fileId);
        if (!file) return;

        this.showInputModal('重命名文件', '请输入新的文件名:', file.title || '', async (newName) => {
            if (!newName || newName === file.title) return;

            try {
                await ipcRenderer.invoke('update-document', fileId, { title: newName });
                await this.loadFiles();
                CoreUtils.showNotification('文件重命名成功', 'success');
            } catch (error) {
                console.error('重命名失败:', error);
                CoreUtils.showNotification('重命名失败: ' + error.message, 'error');
            }
        });
    }

    async deleteFile(fileId) {
        const file = this.files.find(f => f.id === fileId);
        if (!file) return;

        this.showConfirmModal(
            '删除文件',
            `确定要删除文件"${file.title}"吗？此操作不可撤销。`,
            async () => {
                try {
                    await ipcRenderer.invoke('delete-document', fileId);

                    // 立即从本地列表中移除文件，避免重新加载
                    this.files = this.files.filter(f => f.id !== fileId);
                    this.app.files = this.files;

                    // 如果删除的是当前选中的文件，清除选择
                    if (this.selectedFile === fileId) {
                        this.selectedFile = null;
                    }

                    // 如果删除的是当前编辑的文件，清空编辑器
                    if (this.app.editorManager && this.app.editorManager.currentDocument &&
                        this.app.editorManager.currentDocument.id === fileId) {
                        this.app.editorManager.clearEditor();
                    }

                    this.renderFiles();
                    CoreUtils.showNotification('文件已删除', 'success');
                } catch (error) {
                    console.error('删除文件失败:', error);
                    CoreUtils.showNotification('删除失败: ' + error.message, 'error');
                }
            }
        );
    }

    async duplicateFile(fileId) {
        const file = this.files.find(f => f.id === fileId);
        if (!file) return;
        
        try {
            const content = await ipcRenderer.invoke('get-document-content', fileId);
            const newTitle = file.title + ' - 副本';
            
            await ipcRenderer.invoke('create-document', {
                title: newTitle,
                content: content,
                folder: file.folder
            });
            
            await this.loadFiles();
            CoreUtils.showNotification('文件已复制', 'success');
        } catch (error) {
            console.error('复制文件失败:', error);
            CoreUtils.showNotification('复制失败: ' + error.message, 'error');
        }
    }

    async moveFile(fileId) {
        CoreUtils.showNotification('移动功能开发中...', 'info');
    }

    async createNewFile() {
        this.showInputModal('新建文件', '请输入文件名:', '', async (title) => {
            if (!title) return;

            const trimmedTitle = title.trim();
            if (!trimmedTitle) {
                CoreUtils.showNotification('文件名不能为空', 'warning');
                return;
            }

            try {
                // 检查文件名是否已存在（在根目录）
                const existingFiles = this.files.filter(f => !f.folder).map(f => f.title);
                if (existingFiles.includes(trimmedTitle)) {
                    CoreUtils.showNotification('文件名已存在', 'warning');
                    return;
                }

                // 创建新文件，默认添加Markdown标题
                const result = await ipcRenderer.invoke('create-document', {
                    title: trimmedTitle,
                    content: `# ${trimmedTitle}\n\n开始编写内容...`,
                    folder: null
                });

                if (result) {
                    // 立即添加到本地文件列表，避免重新加载
                    this.files.push(result);
                    this.app.files = this.files;
                    this.renderFiles();

                    CoreUtils.showNotification(`已创建文件: ${trimmedTitle}`, 'success');

                    // 自动打开新创建的文件
                    if (this.app.editorManager) {
                        setTimeout(() => {
                            this.app.editorManager.loadDocument(result.id);
                        }, 100);
                    }
                }

            } catch (error) {
                console.error('创建文件失败:', error);
                CoreUtils.showNotification('创建失败: ' + error.message, 'error');
            }
        });
    }

    async createNewFolder() {
        this.showInputModal('新建文件夹', '请输入文件夹名:', '', async (name) => {
            if (!name) return;

            const trimmedName = name.trim();
            if (!trimmedName) {
                CoreUtils.showNotification('文件夹名称不能为空', 'warning');
                return;
            }

            try {
                // 检查文件夹是否已存在
                const existingFolders = [...new Set(this.files.filter(f => f.folder).map(f => f.folder))];
                if (existingFolders.includes(trimmedName)) {
                    CoreUtils.showNotification('文件夹已存在', 'warning');
                    return;
                }

                // 创建文件夹（通过创建一个隐藏的配置文件）
                const configResult = await ipcRenderer.invoke('create-document', {
                    title: '.folder_config',
                    content: JSON.stringify({
                        folderName: trimmedName,
                        created: new Date().toISOString(),
                        type: 'folder_config'
                    }),
                    folder: trimmedName
                });

                if (configResult) {
                    // 立即添加到本地文件列表（但不显示配置文件）
                    this.files.push(configResult);
                    this.app.files = this.files;
                    this.renderFiles();

                    CoreUtils.showNotification(`已创建文件夹: ${trimmedName}`, 'success');
                }

            } catch (error) {
                console.error('创建文件夹失败:', error);
                CoreUtils.showNotification('创建失败: ' + error.message, 'error');
            }
        });
    }

    toggleFolder(folderName) {
        if (!this.openFolders) {
            this.openFolders = new Set();
        }

        if (this.openFolders.has(folderName)) {
            this.openFolders.delete(folderName);
        } else {
            this.openFolders.add(folderName);
        }

        // 直接渲染，但确保不会重复
        if (!this.isRendering) {
            this.renderFiles();
        }
    }

    showFileContextMenu(e, fileId) {
        const items = [
            {
                icon: CoreUtils.getIcon('book'),
                label: '打开',
                action: `organizationApp.filesManager.openFile('${fileId}')`
            },
            {
                icon: CoreUtils.getIcon('edit'),
                label: '重命名',
                action: `organizationApp.filesManager.renameFile('${fileId}')`
            },
            {
                icon: CoreUtils.getIcon('copy'),
                label: '复制',
                action: `organizationApp.filesManager.duplicateFile('${fileId}')`
            },
            {
                icon: CoreUtils.getIcon('move'),
                label: '移动',
                action: `organizationApp.filesManager.moveFile('${fileId}')`
            },
            { separator: true },
            {
                icon: CoreUtils.getIcon('delete'),
                label: '删除',
                className: 'danger',
                action: `organizationApp.filesManager.deleteFile('${fileId}')`
            }
        ];
        
        this.app.uiManager.showContextMenu(e, items);
    }

    showFolderContextMenu(e, folderName) {
        const items = [
            {
                icon: CoreUtils.getIcon('add'),
                label: '新建文件',
                action: `organizationApp.filesManager.createNewFileInFolder('${folderName}')`
            },
            {
                icon: CoreUtils.getIcon('folder'),
                label: '新建文件夹',
                action: `organizationApp.filesManager.createNewFolderIn('${folderName}')`
            },
            { separator: true },
            {
                icon: CoreUtils.getIcon('edit'),
                label: '重命名',
                action: `organizationApp.filesManager.renameFolder('${folderName}')`
            },
            {
                icon: CoreUtils.getIcon('delete'),
                label: '删除',
                className: 'danger',
                action: `organizationApp.filesManager.deleteFolder('${folderName}')`
            }
        ];
        
        this.app.uiManager.showContextMenu(e, items);
    }

    async createNewFileInFolder(folderName) {
        this.showInputModal('新建文件', `在文件夹"${folderName}"中创建文件:`, '', async (title) => {
            if (!title) return;

            const trimmedTitle = title.trim();
            if (!trimmedTitle) {
                CoreUtils.showNotification('文件名不能为空', 'warning');
                return;
            }

            try {
                // 检查文件名是否已存在（在指定文件夹中）
                const existingFiles = this.files.filter(f => f.folder === folderName).map(f => f.title);
                if (existingFiles.includes(trimmedTitle)) {
                    CoreUtils.showNotification('文件名已存在', 'warning');
                    return;
                }

                // 创建新文件，默认添加Markdown标题
                const result = await ipcRenderer.invoke('create-document', {
                    title: trimmedTitle,
                    content: `# ${trimmedTitle}\n\n开始编写内容...`,
                    folder: folderName
                });

                if (result) {
                    // 立即添加到本地文件列表，避免重新加载
                    this.files.push(result);
                    this.app.files = this.files;
                    this.renderFiles();

                    CoreUtils.showNotification(`已在文件夹"${folderName}"中创建文件: ${trimmedTitle}`, 'success');

                    // 自动打开新创建的文件
                    if (this.app.editorManager) {
                        setTimeout(() => {
                            this.app.editorManager.loadDocument(result.id);
                        }, 100);
                    }
                }

            } catch (error) {
                console.error('创建文件失败:', error);
                CoreUtils.showNotification('创建失败: ' + error.message, 'error');
            }
        });
    }

    async createNewFolderIn(parentFolder) {
        // 暂时不支持嵌套文件夹，显示友好提示
        CoreUtils.showNotification('当前版本暂不支持嵌套文件夹，请在根目录创建新文件夹', 'info');
    }

    async renameFolder(folderName) {
        // 获取当前所有文件夹名称用于重复检查
        const existingFolders = [...new Set(this.files
            .filter(f => f.folder && !f.title.startsWith('.folder_config'))
            .map(f => f.folder)
        )];

        // 从配置文件中获取文件夹名称
        const folderConfigs = this.files.filter(f => f.title && f.title.startsWith('.folder_config'));
        folderConfigs.forEach(config => {
            if (config.folder) {
                existingFolders.push(config.folder);
            }
        });

        this.showInputModal('重命名文件夹', '请输入新的文件夹名称:', folderName, async (newName) => {
            if (!newName || newName === folderName) return;

            const trimmedName = newName.trim();
            if (!trimmedName) {
                CoreUtils.showNotification('文件夹名称不能为空', 'warning');
                return;
            }

            // 检查是否与现有文件夹重名
            if (existingFolders.includes(trimmedName)) {
                CoreUtils.showNotification('文件夹名称已存在', 'warning');
                return;
            }

            try {
                // 更新所有在该文件夹中的文件
                const filesToUpdate = this.files.filter(f => f.folder === folderName);
                const updatePromises = filesToUpdate.map(file =>
                    ipcRenderer.invoke('update-document', file.id, {
                        folder: trimmedName
                    })
                );

                await Promise.all(updatePromises);

                // 立即更新本地文件列表
                this.files.forEach(file => {
                    if (file.folder === folderName) {
                        file.folder = trimmedName;
                    }
                });

                // 更新打开的文件夹状态
                if (this.openFolders.has(folderName)) {
                    this.openFolders.delete(folderName);
                    this.openFolders.add(trimmedName);
                }

                this.app.files = this.files;
                this.renderFiles();

                CoreUtils.showNotification(`文件夹已重命名为"${trimmedName}"`, 'success');

            } catch (error) {
                console.error('重命名文件夹失败:', error);
                CoreUtils.showNotification('重命名失败: ' + error.message, 'error');
            }
        });
    }

    async deleteFolder(folderName) {
        const filesInFolder = this.files.filter(f => f.folder === folderName && !f.title.startsWith('.folder_config'));
        const fileCount = filesInFolder.length;

        let confirmMessage;
        if (fileCount === 0) {
            confirmMessage = `确定要删除空文件夹"${folderName}"吗？`;
        } else {
            confirmMessage = `文件夹"${folderName}"中包含 ${fileCount} 个文件。\n\n删除选项：\n• 删除文件夹并将文件移至根目录\n• 删除文件夹及其所有文件\n\n请选择操作：`;
        }

        if (fileCount === 0) {
            // 空文件夹，直接删除
            this.showConfirmModal(
                '删除文件夹',
                confirmMessage,
                async () => {
                    await this.performFolderDeletion(folderName, 'delete-empty');
                }
            );
        } else {
            // 非空文件夹，显示选择对话框
            this.showFolderDeletionOptions(folderName, filesInFolder);
        }
    }

    showFolderDeletionOptions(folderName, filesInFolder) {
        const modalHtml = `
            <div class="modal-overlay" id="folderDeletionModalOverlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>删除文件夹</h3>
                        <button class="modal-close" id="folderDeletionModalClose">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>文件夹"${folderName}"中包含 ${filesInFolder.length} 个文件：</p>
                        <div class="file-list-preview">
                            ${filesInFolder.slice(0, 5).map(f => `<div class="file-preview-item">📄 ${f.title}</div>`).join('')}
                            ${filesInFolder.length > 5 ? `<div class="file-preview-more">...还有 ${filesInFolder.length - 5} 个文件</div>` : ''}
                        </div>
                        <p>请选择删除方式：</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" id="folderDeletionCancel">取消</button>
                        <button class="btn btn-warning" id="folderDeletionMoveFiles">删除文件夹，移动文件到根目录</button>
                        <button class="btn btn-danger" id="folderDeletionDeleteAll">删除文件夹及所有文件</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const overlay = document.getElementById('folderDeletionModalOverlay');
        const cancelBtn = document.getElementById('folderDeletionCancel');
        const moveBtn = document.getElementById('folderDeletionMoveFiles');
        const deleteAllBtn = document.getElementById('folderDeletionDeleteAll');
        const closeBtn = document.getElementById('folderDeletionModalClose');

        const handleCancel = () => overlay.remove();

        const handleMoveFiles = async () => {
            overlay.remove();
            await this.performFolderDeletion(folderName, 'move-files');
        };

        const handleDeleteAll = async () => {
            overlay.remove();
            await this.performFolderDeletion(folderName, 'delete-all');
        };

        cancelBtn.addEventListener('click', handleCancel);
        moveBtn.addEventListener('click', handleMoveFiles);
        deleteAllBtn.addEventListener('click', handleDeleteAll);
        closeBtn.addEventListener('click', handleCancel);

        overlay.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                handleCancel();
            }
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                handleCancel();
            }
        });
    }

    async performFolderDeletion(folderName, action) {
        try {
            const filesInFolder = this.files.filter(f => f.folder === folderName);

            if (action === 'move-files') {
                // 将文件移动到根目录
                const movePromises = filesInFolder
                    .filter(f => !f.title.startsWith('.folder_config'))
                    .map(file =>
                        ipcRenderer.invoke('update-document', file.id, { folder: null })
                    );

                await Promise.all(movePromises);

                // 删除配置文件
                const configFiles = filesInFolder.filter(f => f.title.startsWith('.folder_config'));
                const deleteConfigPromises = configFiles.map(file =>
                    ipcRenderer.invoke('delete-document', file.id)
                );

                await Promise.all(deleteConfigPromises);

                // 更新本地文件列表
                this.files.forEach(file => {
                    if (file.folder === folderName && !file.title.startsWith('.folder_config')) {
                        file.folder = null;
                    }
                });

                // 移除配置文件
                this.files = this.files.filter(f =>
                    !(f.folder === folderName && f.title.startsWith('.folder_config'))
                );

                CoreUtils.showNotification(`文件夹"${folderName}"已删除，文件已移至根目录`, 'success');

            } else if (action === 'delete-all') {
                // 删除文件夹及所有文件
                const deletePromises = filesInFolder.map(file =>
                    ipcRenderer.invoke('delete-document', file.id)
                );

                await Promise.all(deletePromises);

                // 从本地文件列表中移除所有文件
                this.files = this.files.filter(f => f.folder !== folderName);

                CoreUtils.showNotification(`文件夹"${folderName}"及其所有文件已删除`, 'success');

            } else if (action === 'delete-empty') {
                // 删除空文件夹（删除配置文件）
                const configFiles = filesInFolder.filter(f => f.title.startsWith('.folder_config'));
                const deletePromises = configFiles.map(file =>
                    ipcRenderer.invoke('delete-document', file.id)
                );

                await Promise.all(deletePromises);

                // 从本地文件列表中移除配置文件
                this.files = this.files.filter(f =>
                    !(f.folder === folderName && f.title.startsWith('.folder_config'))
                );

                CoreUtils.showNotification(`空文件夹"${folderName}"已删除`, 'success');
            }

            // 从打开的文件夹列表中移除
            this.openFolders.delete(folderName);

            // 如果当前编辑的文件在被删除的文件夹中，清空编辑器
            if (this.app.editorManager && this.app.editorManager.currentDocument &&
                this.app.editorManager.currentDocument.folder === folderName) {
                this.app.editorManager.clearEditor();
            }

            this.app.files = this.files;
            this.renderFiles();

        } catch (error) {
            console.error('删除文件夹失败:', error);
            CoreUtils.showNotification('删除失败: ' + error.message, 'error');
        }
    }

    handleFilesSearch(query) {
        const trimmedQuery = query.trim().toLowerCase();
        const clearBtn = document.getElementById('clearSearchBtn');

        // 显示/隐藏清除按钮
        if (clearBtn) {
            clearBtn.style.display = trimmedQuery ? 'flex' : 'none';
        }

        if (!trimmedQuery) {
            // 如果搜索为空，显示所有文件
            this.renderFiles();
            return;
        }

        // 过滤文件
        const filteredFiles = this.files.filter(file => {
            // 过滤掉隐藏的配置文件
            if (file.title && file.title.startsWith('.')) {
                return false;
            }

            // 按文件名搜索
            const titleMatch = file.title && file.title.toLowerCase().includes(trimmedQuery);

            // 按文件夹名搜索
            const folderMatch = file.folder && file.folder.toLowerCase().includes(trimmedQuery);

            // 按内容搜索（如果有内容）
            const contentMatch = file.content && file.content.toLowerCase().includes(trimmedQuery);

            return titleMatch || folderMatch || contentMatch;
        });

        // 渲染搜索结果
        this.renderSearchResults(filteredFiles, trimmedQuery);
    }

    renderSearchResults(files, query) {
        const container = document.getElementById('filesTree');
        if (!container) return;

        if (files.length === 0) {
            container.innerHTML = `
                <div class="search-no-results">
                    <div class="no-results-icon">🔍</div>
                    <div class="no-results-text">未找到匹配"${query}"的文件</div>
                    <div class="no-results-hint">尝试使用不同的关键词</div>
                </div>
            `;
            return;
        }

        // 按文件夹分组
        const filesByFolder = this.groupFilesByFolder(files);

        let html = `<div class="search-results-header">
            <span class="search-results-count">找到 ${files.length} 个结果</span>
        </div>`;

        // 渲染搜索结果
        html += this.renderFileTree(filesByFolder);

        container.innerHTML = html;
        this.bindFileEvents();

        // 高亮搜索关键词
        this.highlightSearchTerms(query);
    }

    highlightSearchTerms(query) {
        const fileItems = document.querySelectorAll('.file-item .file-name');
        const folderItems = document.querySelectorAll('.folder-item .folder-name');

        [...fileItems, ...folderItems].forEach(element => {
            const text = element.textContent;
            const regex = new RegExp(`(${query})`, 'gi');
            const highlightedText = text.replace(regex, '<mark>$1</mark>');
            element.innerHTML = highlightedText;
        });
    }

    closeFilesSearch() {
        const container = document.getElementById('filesSearchContainer');
        const input = document.getElementById('filesSearchInput');

        if (container && input) {
            container.style.display = 'none';
            input.value = '';
            // 恢复显示所有文件
            this.renderFiles();
        }
    }

    clearFilesSearch() {
        const searchInput = document.getElementById('filesSearchInput');
        const clearBtn = document.getElementById('clearSearchBtn');

        if (searchInput) {
            searchInput.value = '';
        }

        if (clearBtn) {
            clearBtn.style.display = 'none';
        }

        // 显示所有文件
        this.renderFiles();
    }

    showInputModal(title, message, defaultValue = '', callback) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal-overlay" id="inputModalOverlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close" id="inputModalClose">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                        <input type="text" id="inputModalInput" value="${defaultValue}" placeholder="请输入...">
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" id="inputModalCancel">取消</button>
                        <button class="btn btn-primary" id="inputModalConfirm">确定</button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const overlay = document.getElementById('inputModalOverlay');
        const input = document.getElementById('inputModalInput');
        const confirmBtn = document.getElementById('inputModalConfirm');
        const cancelBtn = document.getElementById('inputModalCancel');
        const closeBtn = document.getElementById('inputModalClose');

        // 聚焦输入框并选中文本
        input.focus();
        input.select();

        // 确定按钮事件
        const handleConfirm = () => {
            const value = input.value.trim();
            overlay.remove();
            if (value && callback) {
                callback(value);
            }
        };

        // 取消按钮事件
        const handleCancel = () => {
            overlay.remove();
        };

        // 绑定事件
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        closeBtn.addEventListener('click', handleCancel);

        // 回车确认，ESC取消
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleConfirm();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                handleCancel();
            }
        });

        // 点击遮罩层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                handleCancel();
            }
        });
    }

    showConfirmModal(title, message, onConfirm) {
        // 创建确认对话框HTML
        const modalHtml = `
            <div class="modal-overlay" id="confirmModalOverlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close" id="confirmModalClose">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" id="confirmModalCancel">取消</button>
                        <button class="btn btn-danger" id="confirmModalConfirm">确定</button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const overlay = document.getElementById('confirmModalOverlay');
        const confirmBtn = document.getElementById('confirmModalConfirm');
        const cancelBtn = document.getElementById('confirmModalCancel');
        const closeBtn = document.getElementById('confirmModalClose');

        // 聚焦确定按钮
        confirmBtn.focus();

        // 确定按钮事件
        const handleConfirm = () => {
            overlay.remove();
            if (onConfirm) {
                onConfirm();
            }
        };

        // 取消按钮事件
        const handleCancel = () => {
            overlay.remove();
        };

        // 绑定事件
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        closeBtn.addEventListener('click', handleCancel);

        // 键盘事件
        overlay.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleConfirm();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                handleCancel();
            }
        });

        // 点击遮罩层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                handleCancel();
            }
        });
    }

    async moveFileToFolder(fileId, targetFolder) {
        const file = this.files.find(f => f.id === fileId);
        if (!file) {
            CoreUtils.showNotification('文件不存在', 'error');
            return;
        }

        // 如果目标文件夹和当前文件夹相同，不需要移动
        if (file.folder === targetFolder) {
            return;
        }

        const targetFolderName = targetFolder || '根目录';
        const sourceFolderName = file.folder || '根目录';

        try {
            // 更新文件的文件夹信息
            await ipcRenderer.invoke('update-document', fileId, {
                folder: targetFolder
            });

            // 立即更新本地文件列表
            const fileIndex = this.files.findIndex(f => f.id === fileId);
            if (fileIndex !== -1) {
                this.files[fileIndex].folder = targetFolder;
                this.app.files = this.files;
            }

            // 如果目标文件夹是折叠的，展开它
            if (targetFolder && !this.openFolders.has(targetFolder)) {
                this.openFolders.add(targetFolder);
            }

            this.renderFiles();
            CoreUtils.showNotification(`文件已从"${sourceFolderName}"移动到"${targetFolderName}"`, 'success');

        } catch (error) {
            console.error('移动文件失败:', error);
            CoreUtils.showNotification('移动文件失败: ' + error.message, 'error');
        }
    }

    bindFolderDragEvents(folderItem) {
        const folderName = folderItem.dataset.folder;

        // 文件夹默认不可拖拽，只有从头部拖拽时才启用
        folderItem.draggable = false;

        // 为文件夹头部添加特殊的拖拽处理
        const folderHeader = folderItem.querySelector('.folder-header');
        if (folderHeader) {
            folderHeader.draggable = true;
        }

        // 绑定拖拽事件到文件夹头部
        if (folderHeader) {
            folderHeader.addEventListener('dragstart', (e) => {
                // 检查是否从操作按钮拖拽
                const isFromActions = e.target.closest('.folder-actions');

                if (isFromActions) {
                    e.preventDefault();
                    return;
                }

                console.log('Drag start for folder:', folderName);

                const dragData = {
                    type: 'folder',
                    name: folderName
                };

                try {
                    e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
                    e.dataTransfer.effectAllowed = 'move';
                    console.log('Folder drag data set:', JSON.stringify(dragData));
                } catch (error) {
                    console.error('Failed to set folder drag data:', error);
                    e.preventDefault();
                    return;
                }

                folderItem.classList.add('dragging');

                // 简化拖拽预览
                e.dataTransfer.setDragImage(folderItem, 0, 0);
            });
        }

        folderItem.addEventListener('dragend', (e) => {
            folderItem.classList.remove('dragging');
            // 清除所有拖拽目标高亮
            document.querySelectorAll('.drop-target, .drop-before, .drop-after').forEach(el => {
                el.classList.remove('drop-target', 'drop-before', 'drop-after');
            });
        });

        // 文件夹之间的排序
        folderItem.addEventListener('dragover', (e) => {
            if (e.dataTransfer.types.includes('text/plain')) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';

                // 显示插入位置指示器
                const rect = folderItem.getBoundingClientRect();
                const midY = rect.top + rect.height / 2;

                if (e.clientY < midY) {
                    folderItem.classList.add('drop-before');
                    folderItem.classList.remove('drop-after');
                } else {
                    folderItem.classList.add('drop-after');
                    folderItem.classList.remove('drop-before');
                }
            }
        });

        folderItem.addEventListener('dragleave', (e) => {
            if (!folderItem.contains(e.relatedTarget)) {
                folderItem.classList.remove('drop-before', 'drop-after');
            }
        });

        folderItem.addEventListener('drop', (e) => {
            console.log('Folder drop event triggered on:', folderName);
            e.preventDefault();
            e.stopPropagation();
            folderItem.classList.remove('drop-before', 'drop-after');

            try {
                const dragDataStr = e.dataTransfer.getData('text/plain');
                console.log('Folder drop - raw data:', dragDataStr);

                if (!dragDataStr) {
                    console.warn('No drag data found for folder drop');
                    return;
                }

                const dragData = JSON.parse(dragDataStr);
                console.log('Folder drop - parsed data:', dragData);

                if (dragData.type === 'folder') {
                    console.log('Calling reorderFolder:', dragData.name, '->', folderName);
                    // 文件夹排序
                    this.reorderFolder(dragData.name, folderName, e);
                } else if (dragData.type === 'file') {
                    console.log('Moving file to folder:', dragData.id, '->', folderName);
                    // 文件移动到文件夹
                    this.moveFileToFolder(dragData.id, folderName);
                } else {
                    console.log('Unknown drag type:', dragData.type);
                }
            } catch (error) {
                console.error('Folder drop data parsing failed:', error);
                // 如果不是JSON，可能是文件ID（兼容旧格式）
                const fileId = e.dataTransfer.getData('text/plain');
                if (fileId) {
                    console.log('Fallback: moving file to folder:', fileId, '->', folderName);
                    this.moveFileToFolder(fileId, folderName);
                }
            }
        });
    }

    reorderFolder(draggedFolder, targetFolder, event) {
        console.log('reorderFolder called with:', { draggedFolder, targetFolder });

        if (draggedFolder === targetFolder) {
            console.log('Same folder, skipping reorder');
            return;
        }

        try {
            // 获取所有文件夹名称
            const allFolders = [...new Set(this.files
                .map(f => f.folder)
                .filter(folder => folder && folder !== 'root')
            )];

            console.log('All folders:', allFolders);

            // 获取当前保存的顺序，如果没有则使用字母顺序
            let currentOrder = [];
            try {
                const savedOrder = localStorage.getItem('qnotes-folder-order');
                if (savedOrder) {
                    currentOrder = JSON.parse(savedOrder);
                    // 确保所有当前文件夹都在顺序中
                    allFolders.forEach(folder => {
                        if (!currentOrder.includes(folder)) {
                            currentOrder.push(folder);
                        }
                    });
                    // 移除不存在的文件夹
                    currentOrder = currentOrder.filter(folder => allFolders.includes(folder));
                } else {
                    currentOrder = [...allFolders].sort();
                }
            } catch (error) {
                console.warn('Failed to load folder order:', error);
                currentOrder = [...allFolders].sort();
            }

            console.log('Current folder order:', currentOrder);

            const draggedIndex = currentOrder.indexOf(draggedFolder);
            const targetIndex = currentOrder.indexOf(targetFolder);

            console.log('Folder indices:', { draggedIndex, targetIndex });

            if (draggedIndex === -1 || targetIndex === -1) {
                console.error('Folder indices not found');
                return;
            }

            // 确定插入位置
            const targetElement = event.target.closest('.folder-item');
            const rect = targetElement.getBoundingClientRect();
            const midY = rect.top + rect.height / 2;
            const insertBefore = event.clientY < midY;

            console.log('Insert position:', { insertBefore, clientY: event.clientY, midY });

            // 重新排列文件夹数组
            const reorderedFolders = [...currentOrder];
            const [movedFolder] = reorderedFolders.splice(draggedIndex, 1);

            let insertIndex = targetIndex;
            if (draggedIndex < targetIndex && !insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex < targetIndex && insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex > targetIndex && insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex > targetIndex && !insertBefore) {
                insertIndex = targetIndex + 1;
            }

            reorderedFolders.splice(insertIndex, 0, movedFolder);

            console.log('New folder order:', reorderedFolders);

            // 保存文件夹顺序到本地存储
            localStorage.setItem('qnotes-folder-order', JSON.stringify(reorderedFolders));
            console.log('Folder order saved to localStorage');

            // 重新渲染文件列表
            this.renderFiles();
            CoreUtils.showNotification(`文件夹"${draggedFolder}"已重新排序`, 'success');

        } catch (error) {
            console.error('文件夹排序失败:', error);
            CoreUtils.showNotification('文件夹排序失败: ' + error.message, 'error');
        }
    }

    async reorderFile(draggedFileId, targetFileId, event) {
        console.log('reorderFile called with:', { draggedFileId, targetFileId });

        if (draggedFileId === targetFileId) {
            console.log('Same file, skipping reorder');
            return;
        }

        const draggedFile = this.files.find(f => f.id === draggedFileId);
        const targetFile = this.files.find(f => f.id === targetFileId);

        console.log('Found files:', { draggedFile: draggedFile?.title, targetFile: targetFile?.title });

        if (!draggedFile || !targetFile) {
            console.error('Files not found');
            return;
        }

        // 检查是否在同一个文件夹中
        if (draggedFile.folder !== targetFile.folder) {
            console.warn('Files not in same folder');
            CoreUtils.showNotification('只能在同一文件夹内排序文件', 'warning');
            return;
        }

        try {
            // 获取当前文件夹中的所有文件
            const folderFiles = this.files.filter(f => f.folder === draggedFile.folder);
            console.log('Folder files count:', folderFiles.length);

            // 按当前显示顺序排序（按创建时间倒序）
            folderFiles.sort((a, b) => {
                const dateA = new Date(a.created_at || a.createdAt || 0);
                const dateB = new Date(b.created_at || b.createdAt || 0);
                return dateB - dateA;
            });

            // 找到拖拽文件和目标文件的索引
            const draggedIndex = folderFiles.findIndex(f => f.id === draggedFileId);
            const targetIndex = folderFiles.findIndex(f => f.id === targetFileId);

            console.log('File indices:', { draggedIndex, targetIndex });

            if (draggedIndex === -1 || targetIndex === -1) {
                console.error('File indices not found');
                return;
            }

            // 确定插入位置（基于鼠标位置）
            const targetElement = event.target.closest('.file-item');
            const rect = targetElement.getBoundingClientRect();
            const midY = rect.top + rect.height / 2;
            const insertBefore = event.clientY < midY;

            console.log('Insert position:', { insertBefore, clientY: event.clientY, midY });

            // 重新排列数组
            const reorderedFiles = [...folderFiles];
            const [movedFile] = reorderedFiles.splice(draggedIndex, 1);

            let insertIndex = targetIndex;
            if (draggedIndex < targetIndex && !insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex < targetIndex && insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex > targetIndex && insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex > targetIndex && !insertBefore) {
                insertIndex = targetIndex + 1;
            }

            reorderedFiles.splice(insertIndex, 0, movedFile);

            console.log('Reordering files, new order:', reorderedFiles.map(f => f.title));

            // 使用简单的排序权重而不是时间戳
            const baseTime = Date.now();
            const updatePromises = [];

            for (let i = 0; i < reorderedFiles.length; i++) {
                const file = reorderedFiles[i];
                // 为每个文件分配新的排序权重
                const sortOrder = reorderedFiles.length - i;
                const newTimestamp = new Date(baseTime - i * 1000).toISOString();

                console.log(`Updating file ${file.title} with sort order ${sortOrder}`);

                const updatePromise = ipcRenderer.invoke('update-document', file.id, {
                    created_at: newTimestamp,
                    sort_order: sortOrder
                }).then(() => {
                    // 更新本地数据
                    const localFile = this.files.find(f => f.id === file.id);
                    if (localFile) {
                        localFile.created_at = newTimestamp;
                        localFile.createdAt = newTimestamp;
                        localFile.sort_order = sortOrder;
                    }
                }).catch(error => {
                    console.error(`更新文件 ${file.id} 排序失败:`, error);
                });

                updatePromises.push(updatePromise);
            }

            // 等待所有更新完成
            await Promise.all(updatePromises);
            console.log('All file updates completed');

            // 重新渲染文件列表
            this.renderFiles();
            CoreUtils.showNotification(`文件"${draggedFile.title}"已重新排序`, 'success');

        } catch (error) {
            console.error('文件排序失败:', error);
            CoreUtils.showNotification('文件排序失败: ' + error.message, 'error');
        }
    }

    // 操作反馈方法
    showOperationFeedback(element, operation) {
        if (!element) return;

        element.classList.add(operation);

        if (operation === 'creating') {
            setTimeout(() => {
                element.classList.remove('creating');
            }, 300);
        }
    }

    addLoadingOverlay(element) {
        if (!element || element.querySelector('.local-loading-overlay')) return;

        const overlay = document.createElement('div');
        overlay.className = 'local-loading-overlay';
        overlay.innerHTML = '<div class="loading-spinner"></div>';

        element.style.position = 'relative';
        element.appendChild(overlay);

        return overlay;
    }

    removeLoadingOverlay(element) {
        if (!element) return;

        const overlay = element.querySelector('.local-loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    showOperationStatus(message, type = 'success') {
        // 移除现有的状态指示器
        const existing = document.querySelector('.operation-status');
        if (existing) {
            existing.remove();
        }

        const status = document.createElement('div');
        status.className = `operation-status ${type}`;
        status.textContent = message;

        document.body.appendChild(status);

        // 显示动画
        setTimeout(() => {
            status.classList.add('show');
        }, 10);

        // 3秒后自动隐藏
        setTimeout(() => {
            status.classList.remove('show');
            setTimeout(() => {
                if (status.parentNode) {
                    status.remove();
                }
            }, 300);
        }, 3000);
    }

    addButtonClickFeedback(button) {
        if (!button) return;

        button.classList.add('clicked');
        setTimeout(() => {
            button.classList.remove('clicked');
        }, 200);
    }

    async duplicateFile(fileId) {
        const file = this.files.find(f => f.id === fileId);
        if (!file) {
            CoreUtils.showNotification('文件不存在', 'error');
            return;
        }

        try {
            // 生成新的文件名
            let newTitle = `${file.title} - 副本`;
            let counter = 1;

            // 检查重复名称
            const existingTitles = this.files
                .filter(f => f.folder === file.folder)
                .map(f => f.title);

            while (existingTitles.includes(newTitle)) {
                counter++;
                newTitle = `${file.title} - 副本${counter}`;
            }

            // 创建副本
            const result = await ipcRenderer.invoke('create-document', {
                title: newTitle,
                content: file.content || '',
                folder: file.folder
            });

            if (result) {
                // 立即添加到本地文件列表
                this.files.push(result);
                this.app.files = this.files;
                this.renderFiles();

                CoreUtils.showNotification(`已创建文件副本: ${newTitle}`, 'success');
            }

        } catch (error) {
            console.error('复制文件失败:', error);
            CoreUtils.showNotification('复制失败: ' + error.message, 'error');
        }
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FilesManager;
} else {
    window.FilesManager = FilesManager;
}
