class EditorManager {
    constructor(app) {
        this.app = app;
        this.currentDocument = null;
        this.editorContent = '';
        this.isModified = false;
        this.autoSaveInterval = null;
        this.undoHistory = [];
        this.redoHistory = [];
        this.maxHistorySize = 50;

        // 延迟初始化，确保DOM已加载
        setTimeout(() => {
            this.bindEvents();
            this.setupAutoSave();
            this.setupUndoRedo();
        }, 0);
    }

    bindEvents() {
        // 编辑器工具栏按钮
        document.getElementById('saveBtn')?.addEventListener('click', () => this.saveDocument());
        document.getElementById('exportBtn')?.addEventListener('click', () => this.showExportModal());
        document.getElementById('settingsBtn')?.addEventListener('click', () => this.openSettings());
        
        // 文档标题输入已移除 - 标题现在独立于文件名
        
        // 导出模态框按钮
        document.getElementById('exportMarkdownBtn')?.addEventListener('click', () => this.exportAs('md'));
        document.getElementById('exportHtmlBtn')?.addEventListener('click', () => this.exportAs('html'));
        document.getElementById('exportPdfBtn')?.addEventListener('click', () => this.exportAs('pdf'));
        document.getElementById('exportTxtBtn')?.addEventListener('click', () => this.exportAs('txt'));
        
        // 监听编辑器内容变化
        const editor = document.getElementById('markdownEditor');
        if (editor) {
            editor.addEventListener('input', () => {
                this.handleContentChange();
                this.showSaveStatus('未保存', 'warning');
            });

            editor.addEventListener('selectionchange', () => {
                this.app.aiManager.handleEditorSelection();
            });
        }
    }

    handleContentChange() {
        const editor = document.getElementById('markdownEditor');
        if (!editor) return;

        this.editorContent = editor.value;
        this.isModified = true;

        // 延迟保存到历史记录，避免频繁保存
        clearTimeout(this.historyTimeout);
        this.historyTimeout = setTimeout(() => {
            this.saveToHistory();
        }, 1000);

        // 更新字数统计
        this.app.uiManager.updateWordCount();

        // 更新预览
        if (this.app.uiManager.currentRightPanel === 'preview') {
            this.app.uiManager.updatePreview();
        }

        // 更新标题显示修改状态
        this.updateTitleModifiedState();
    }

    updateTitleModifiedState() {
        const title = this.currentDocument?.title || '未命名文档';
        document.title = this.isModified ? `${title} - 已修改` : title;
    }

    clearEditor() {
        const editor = document.getElementById('markdownEditor');

        if (editor) {
            editor.value = '';
        }

        this.currentDocument = null;
        this.editorContent = '';
        this.isModified = false;
        this.undoHistory = [];
        this.redoHistory = [];

        this.updateTitleModifiedState();

        // 更新预览
        if (this.app.uiManager.currentRightPanel === 'preview') {
            this.app.uiManager.updatePreview();
        }

        // 更新字数统计
        this.app.uiManager.updateWordCount();
    }

    // updateDocumentTitle method removed - titles are now managed in file list

    async saveDocument() {
        try {
            const editor = document.getElementById('markdownEditor');

            if (!editor) return;

            const content = editor.value;
            const title = this.currentDocument?.title || '未命名文档';

            // 显示保存中状态
            this.showSaveStatus('保存中...', 'info');

            if (this.currentDocument) {
                // 更新现有文档
                await ipcRenderer.invoke('update-document', this.currentDocument.id, {
                    title: title,
                    content: content
                });

                this.currentDocument.title = title;
                this.currentDocument.content = content;
            } else {
                // 创建新文档
                const newDoc = await ipcRenderer.invoke('create-document', {
                    title: title,
                    content: content
                });

                this.currentDocument = newDoc;

                // 更新文件列表
                if (this.app.filesManager) {
                    await this.app.filesManager.loadFiles();
                }
            }

            this.isModified = false;
            this.editorContent = content;
            this.updateTitleModifiedState();

            this.showSaveStatus('已保存', 'success');

        } catch (error) {
            console.error('保存文档失败:', error);
            this.showSaveStatus('保存失败', 'error');
            CoreUtils.showNotification('保存失败: ' + error.message, 'error');
        }
    }

    async loadDocument(documentId) {
        try {
            const doc = await ipcRenderer.invoke('get-document', documentId);
            const content = await ipcRenderer.invoke('get-document-content', documentId);
            
            this.currentDocument = doc;
            this.editorContent = content || '';
            this.isModified = false;
            
            // 更新编辑器内容
            const editor = document.getElementById('markdownEditor');
            if (editor) {
                editor.value = this.editorContent;
                editor.dispatchEvent(new Event('input'));
            }
            
            this.updateTitleModifiedState();
            
        } catch (error) {
            console.error('加载文档失败:', error);
            CoreUtils.showNotification('加载文档失败: ' + error.message, 'error');
        }
    }

    newDocument() {
        this.currentDocument = null;
        this.editorContent = '';
        this.isModified = false;
        
        // 清空编辑器
        const editor = document.getElementById('markdownEditor');
        if (editor) {
            editor.value = '';
            editor.dispatchEvent(new Event('input'));
        }
        
        this.updateTitleModifiedState();
    }

    setupAutoSave() {
        // 暂时禁用自动保存来排除干扰
        /*
        // 每10秒自动保存
        this.autoSaveInterval = setInterval(() => {
            if (this.isModified && this.currentDocument) {
                this.autoSaveDocument();
            }
        }, 10000);
        */

        // 添加实时保存状态指示器
        this.setupSaveStatusIndicator();
    }

    async autoSaveDocument() {
        try {
            await this.saveDocument();
            this.showSaveStatus('已自动保存', 'success');
        } catch (error) {
            console.error('自动保存失败:', error);
            this.showSaveStatus('自动保存失败', 'error');
        }
    }

    setupSaveStatusIndicator() {
        // 创建保存状态指示器
        if (!document.getElementById('saveStatus')) {
            const statusDiv = document.createElement('div');
            statusDiv.id = 'saveStatus';
            statusDiv.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                z-index: 1000;
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: none;
            `;
            document.body.appendChild(statusDiv);
        }
    }

    showSaveStatus(message, type = 'info') {
        const statusDiv = document.getElementById('saveStatus');
        if (!statusDiv) return;

        statusDiv.textContent = message;
        statusDiv.className = `save-status ${type}`;

        // 设置颜色
        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            info: '#2196F3',
            warning: '#ff9800'
        };

        statusDiv.style.backgroundColor = colors[type] || colors.info;
        statusDiv.style.color = 'white';
        statusDiv.style.opacity = '1';

        // 2秒后淡出
        setTimeout(() => {
            statusDiv.style.opacity = '0';
        }, 2000);
    }

    setupUndoRedo() {
        // 监听键盘快捷键
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
                e.preventDefault();
                this.undo();
            } else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
                e.preventDefault();
                this.redo();
            }
        });
    }

    saveToHistory() {
        const editor = document.getElementById('markdownEditor');

        if (!editor) return;

        const state = {
            content: editor.value,
            title: this.currentDocument?.title || '',
            selectionStart: editor.selectionStart,
            selectionEnd: editor.selectionEnd,
            timestamp: Date.now()
        };

        // 避免保存相同的状态
        const lastState = this.undoHistory[this.undoHistory.length - 1];
        if (lastState && lastState.content === state.content && lastState.title === state.title) {
            return;
        }

        this.undoHistory.push(state);

        // 限制历史记录大小
        if (this.undoHistory.length > this.maxHistorySize) {
            this.undoHistory.shift();
        }

        // 清空重做历史
        this.redoHistory = [];
    }

    undo() {
        if (this.undoHistory.length === 0) return;

        const editor = document.getElementById('markdownEditor');

        if (!editor) return;

        // 保存当前状态到重做历史
        const currentState = {
            content: editor.value,
            title: this.currentDocument?.title || '',
            selectionStart: editor.selectionStart,
            selectionEnd: editor.selectionEnd,
            timestamp: Date.now()
        };

        this.redoHistory.push(currentState);

        // 恢复上一个状态
        const previousState = this.undoHistory.pop();
        if (previousState) {
            editor.value = previousState.content;
            // Title is now managed in file list, not in editor

            // 恢复光标位置
            editor.selectionStart = previousState.selectionStart;
            editor.selectionEnd = previousState.selectionEnd;

            // 更新内容
            this.editorContent = previousState.content;
            this.isModified = true;
            this.updateTitleModifiedState();
            this.showSaveStatus('已撤销', 'info');
        }
    }

    redo() {
        if (this.redoHistory.length === 0) return;

        const editor = document.getElementById('markdownEditor');

        if (!editor) return;

        // 保存当前状态到撤销历史
        const currentState = {
            content: editor.value,
            title: this.currentDocument?.title || '',
            selectionStart: editor.selectionStart,
            selectionEnd: editor.selectionEnd,
            timestamp: Date.now()
        };

        this.undoHistory.push(currentState);

        // 恢复重做状态
        const nextState = this.redoHistory.pop();
        if (nextState) {
            editor.value = nextState.content;
            // Title is now managed in file list, not in editor

            // 恢复光标位置
            editor.selectionStart = nextState.selectionStart;
            editor.selectionEnd = nextState.selectionEnd;

            // 更新内容
            this.editorContent = nextState.content;
            this.isModified = true;
            this.updateTitleModifiedState();
            this.showSaveStatus('已重做', 'info');
        }
    }

    showExportModal() {
        CoreUtils.showModal('exportModal');
    }

    async exportAs(format) {
        try {
            const editor = document.getElementById('markdownEditor');
            if (!editor) return;
            
            const content = editor.value;
            const title = this.currentDocument?.title || '未命名文档';
            
            let exportData;
            let mimeType;
            let extension;
            
            switch (format) {
                case 'md':
                    exportData = content;
                    mimeType = 'text/markdown';
                    extension = 'md';
                    break;
                    
                case 'html':
                    exportData = this.markdownToHtml(content);
                    mimeType = 'text/html';
                    extension = 'html';
                    break;
                    
                case 'txt':
                    exportData = this.markdownToText(content);
                    mimeType = 'text/plain';
                    extension = 'txt';
                    break;
                    
                case 'pdf':
                    // PDF导出需要特殊处理
                    await this.exportToPdf(content, title);
                    return;
                    
                default:
                    throw new Error('不支持的导出格式');
            }
            
            // 创建下载链接
            const blob = new Blob([exportData], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${title}.${extension}`;
            a.click();
            URL.revokeObjectURL(url);
            
            CoreUtils.hideModal('exportModal');
            CoreUtils.showNotification(`导出${format.toUpperCase()}成功`, 'success');
            
        } catch (error) {
            console.error('导出失败:', error);
            CoreUtils.showNotification('导出失败: ' + error.message, 'error');
        }
    }

    markdownToHtml(markdown) {
        // 简单的 Markdown 转 HTML
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${this.currentDocument?.title || '文档'}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2, h3 { color: #333; }
        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
        blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 20px; color: #666; }
    </style>
</head>
<body>
${this.app.uiManager.markdownToHtml(markdown)}
</body>
</html>
        `.trim();
    }

    markdownToText(markdown) {
        // 简单的 Markdown 转纯文本
        return markdown
            .replace(/^#{1,6}\s+/gm, '') // 移除标题标记
            .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体
            .replace(/\*(.*?)\*/g, '$1') // 移除斜体
            .replace(/`(.*?)`/g, '$1') // 移除代码标记
            .replace(/!\[.*?\]\(.*?\)/g, '[图片]') // 替换图片
            .replace(/\[([^\]]*)\]\([^\)]*\)/g, '$1') // 替换链接
            .replace(/^\s*[-*+]\s+/gm, '• ') // 替换列表标记
            .replace(/^\s*\d+\.\s+/gm, '• ') // 替换有序列表
            .replace(/^\s*>\s+/gm, '') // 移除引用标记
            .replace(/```[\s\S]*?```/g, '[代码块]') // 替换代码块
            .trim();
    }

    async exportToPdf(content, title) {
        try {
            // 使用 Electron 的打印功能导出PDF
            const htmlContent = this.markdownToHtml(content);
            const result = await ipcRenderer.invoke('export-pdf', htmlContent, title);
            
            if (result.success) {
                CoreUtils.hideModal('exportModal');
                CoreUtils.showNotification('导出PDF成功', 'success');
            } else {
                throw new Error(result.error);
            }
            
        } catch (error) {
            console.error('PDF导出失败:', error);
            CoreUtils.showNotification('PDF导出失败: ' + error.message, 'error');
        }
    }

    async openSettings() {
        try {
            // 打开设置窗口
            CoreUtils.showNotification('设置功能开发中...', 'info');
        } catch (error) {
            console.error('打开设置失败:', error);
        }
    }

    // 插入文本到光标位置
    insertText(text) {
        const editor = document.getElementById('markdownEditor');
        if (!editor) return;
        
        const start = editor.selectionStart;
        const end = editor.selectionEnd;
        const value = editor.value;
        
        editor.value = value.substring(0, start) + text + value.substring(end);
        editor.selectionStart = editor.selectionEnd = start + text.length;
        
        // 触发输入事件
        editor.dispatchEvent(new Event('input'));
        editor.focus();
    }

    // 获取编辑器内容
    getContent() {
        const editor = document.getElementById('markdownEditor');
        return editor ? editor.value : '';
    }

    // 设置编辑器内容
    setContent(content) {
        const editor = document.getElementById('markdownEditor');
        if (editor) {
            editor.value = content;
            editor.dispatchEvent(new Event('input'));
        }
    }

    // 获取选中的文本
    getSelectedText() {
        const editor = document.getElementById('markdownEditor');
        if (!editor) return '';
        
        return editor.value.substring(editor.selectionStart, editor.selectionEnd);
    }

    // 替换选中的文本
    replaceSelectedText(text) {
        const editor = document.getElementById('markdownEditor');
        if (!editor) return;
        
        const start = editor.selectionStart;
        const end = editor.selectionEnd;
        const value = editor.value;
        
        editor.value = value.substring(0, start) + text + value.substring(end);
        editor.selectionStart = start;
        editor.selectionEnd = start + text.length;
        
        // 触发输入事件
        editor.dispatchEvent(new Event('input'));
        editor.focus();
    }

    // 检查是否有未保存的更改
    hasUnsavedChanges() {
        return this.isModified;
    }

    // 提示保存
    async promptSave() {
        if (!this.isModified) return true;
        
        const result = confirm('文档有未保存的更改，是否保存？');
        if (result) {
            await this.saveDocument();
        }
        return true;
    }

    // 销毁
    destroy() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EditorManager;
} else {
    window.EditorManager = EditorManager;
}
