const { ipc<PERSON><PERSON>er } = require('electron');

class OrganizationApp {
    constructor() {
        this.materials = [];
        this.files = [];
        this.selectedMaterials = new Set();
        this.lastSelectedMaterial = null;
        this.currentDocument = null;
        this.editorContent = '';
        this.currentRightPanel = 'preview'; // 'preview' or 'ai'
        this.currentLeftTab = 'materials'; // 'materials' or 'files'
        this.aiHistory = [];
        this.selectedText = '';
        this.searchQuery = '';
        this.filters = {
            type: '',
            category: ''
        };
        
        // AI流式对话状态
        this.isAIStreaming = false;
        this.currentAIMessage = null;
        this.currentAIContent = '';
        this.streamUpdateThrottle = null;
        
        // 文件管理状态
        this.selectedFile = null;
        this.openFolders = new Set();
        
        // 为了向后兼容，保持 collections 引用
        this.collections = this.materials;
        
        // 全局图标库
        this.icons = {
            // 文件类型图标
            text: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h10v2H7V7zm0 4h10v2H7v-2zm0 4h7v2H7v-2z"/></svg>',
            image: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M5 3h14c1.1 0 2 .9 2 2v14c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V5c0-1.1.9-2 2-2zm0 2v10.5l3.5-3.5 2.5 2.5L16.5 9 19 11.5V5H5zm0 14h14v-1.5L16.5 13 11 18.5 8.5 16 5 19.5V19z"/></svg>',
            link: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M10.59 13.41c.41.39.41 1.03 0 1.42-.39.39-1.03.39-1.42 0a5.003 5.003 0 0 1 0-7.07l3.54-3.54a5.003 5.003 0 0 1 7.07 0 5.003 5.003 0 0 1 0 7.07l-1.49 1.49c.01-.82-.12-1.64-.4-2.42l.47-.48a2.982 2.982 0 0 0 0-4.24 2.982 2.982 0 0 0-4.24 0l-3.53 3.53a2.982 2.982 0 0 0 0-4.24zm2.82-4.24c.39-.39 1.03-.39 1.42 0a5.003 5.003 0 0 1 0 7.07l-3.54 3.54a5.003 5.003 0 0 1-7.07 0 5.003 5.003 0 0 1 0-7.07l1.49-1.49c-.01.82.12 1.64.4 2.42l-.47.48a2.982 2.982 0 0 0 0 4.24 2.982 2.982 0 0 0 4.24 0l3.53-3.53a2.982 2.982 0 0 0 0-4.24.973.973 0 0 1 0-1.42z"/></svg>',
            file: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h10c1.1 0 2-.9 2-2V8l-6-6H6zm7 7V3.5L18.5 9H13z"/></svg>',
            code: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M8.7 15.9L4.8 12l3.9-3.9c.39-.39.39-1.01 0-1.4s-1.01-.39-1.4 0l-4.59 4.59c-.39.39-.39 1.02 0 1.41l4.59 4.6c.39.39 1.01.39 1.4 0 .39-.39.39-1.01 0-1.4zm6.6 0l3.9-3.9-3.9-3.9c-.39-.39-.39-1.01 0-1.4s1.01-.39 1.4 0l4.59 4.59c.39.39.39 1.02 0 1.41L16.7 16.3c-.39.39-1.01.39-1.4 0-.39-.39-.39-1.01 0-1.4z"/></svg>',
            video: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"/></svg>',
            audio: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/></svg>',
            // UI 操作图标
            search: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>',
            add: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg>',
            settings: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/></svg>',
            close: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>',
            edit: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>',
            delete: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/></svg>',
            save: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/></svg>',
            folder: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/></svg>',
            statistics: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg>',
            collection: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/></svg>',
            export: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z"/></svg>',
            book: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/></svg>',
            exit: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M10.09 15.59L11.5 17l5-5-5-5-1.41 1.41L12.67 11H3v2h9.67l-2.58 2.59zM19 3H5c-1.11 0-2 .9-2 2v4h2V5h14v14H5v-4H3v4c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/></svg>',
            copy: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/></svg>',
            move: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg>',
            refresh: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/></svg>',
            check: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>',
            ai: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.12.23-2.19.64-3.17L6.5 10.5C6.19 11.29 6 12.13 6 13c0 3.31 2.69 6 6 6s6-2.69 6-6-2.69-6-6-6c-.87 0-1.71.19-2.5.5L7.83 5.83C9.1 4.67 10.5 4 12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/><circle cx="9" cy="9" r="2"/><circle cx="15" cy="15" r="2"/><path d="M9 15l6-6"/></svg>'
        };
        
        this.init();
    }

    async init() {
        try {
            // 将实例暴露给全局，以便MarkdownEditor访问
            window.organizationApp = this;
            
            // 初始化UI
            this.initializeUI();
            
            // 绑定事件
            this.bindEvents();
            
            // 绑定AI事件
            this.bindAIEvents();
            
            // 加载数据
            await this.loadMaterials();
            
            // 更新状态栏
            this.updateStatusBar();
            
            console.log('整理模式界面初始化完成');
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.showNotification('初始化失败: ' + error.message, 'error');
        }
    }

    initializeUI() {
        // 初始化 Markdown 编辑器
        this.initializeMarkdownEditor();
        
        // 设置初始状态
        this.updateWordCount();
        this.updateStatusBar();
        
        // 隐藏加载覆盖层
        this.hideLoading();
    }

    initializeMarkdownEditor() {
        const editorContainer = document.getElementById('editorContent');
        
        // 移除占位符内容
        editorContainer.innerHTML = '';
        
        // 创建编辑器文本区域
        const textarea = document.createElement('textarea');
        textarea.id = 'markdownTextarea';
        textarea.placeholder = '开始编写你的文档，或者从左侧拖拽素材到这里...';
        
        // 添加到容器
        editorContainer.appendChild(textarea);
        
        // 获取预览容器
        const previewContainer = document.getElementById('previewContent');
        
        // 初始化增强的Markdown编辑器
        this.markdownEditor = new MarkdownEditor(textarea, previewContainer);
        
        // 设置初始内容
        const initialContent = '# 开始编写你的文档\n\n在这里输入 Markdown 内容，或者从左侧拖拽素材到这里...';
        this.markdownEditor.setContent(initialContent);
        this.editorContent = initialContent;
        
        // 监听内容变化
        textarea.addEventListener('contentChanged', (e) => {
            this.editorContent = e.detail.content;
            this.updateWordCount();
        });
        
        // 监听键盘事件
        textarea.addEventListener('keydown', (e) => {
            this.handleEditorKeydown(e);
        });
        
        console.log('Markdown编辑器初始化完成，支持拖拽插入功能');
        
        // 存储textarea引用
        this.textarea = textarea;
        
        // 初始化预览
        this.updatePreview();
        this.updateWordCount();
        
        // 初始化拖拽功能
        this.initializeDragAndDrop();
    }

    handleEditorKeydown(e) {
        // Tab键插入缩进
        if (e.key === 'Tab') {
            e.preventDefault();
            this.insertAtCursor('    '); // 4个空格缩进
        }
        
        // Ctrl/Cmd + B 加粗
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            this.wrapSelection('**', '**');
        }
        
        // Ctrl/Cmd + I 斜体
        if ((e.ctrlKey || e.metaKey) && e.key === 'i') {
            e.preventDefault();
            this.wrapSelection('*', '*');
        }
        
        // Ctrl/Cmd + K 链接
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.insertLink();
        }
        
        // Enter键自动列表
        if (e.key === 'Enter') {
            const textarea = this.textarea;
            const cursorPos = textarea.selectionStart;
            const text = textarea.value;
            const lineStart = text.lastIndexOf('\n', cursorPos - 1) + 1;
            const currentLine = text.substring(lineStart, cursorPos);
            
            // 检查是否是列表项
            const listMatch = currentLine.match(/^(\s*)[\*\-\+]\s/);
            const orderedListMatch = currentLine.match(/^(\s*)(\d+)\.\s/);
            
            if (listMatch) {
                e.preventDefault();
                this.insertAtCursor('\n' + listMatch[1] + '* ');
            } else if (orderedListMatch) {
                e.preventDefault();
                const nextNum = parseInt(orderedListMatch[2]) + 1;
                this.insertAtCursor('\n' + orderedListMatch[1] + nextNum + '. ');
            }
        }
    }

    insertAtCursor(text) {
        const textarea = this.textarea;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const value = textarea.value;
        
        textarea.value = value.substring(0, start) + text + value.substring(end);
        textarea.selectionStart = textarea.selectionEnd = start + text.length;
        
        // 触发input事件更新预览
        this.editorContent = textarea.value;
        this.updateWordCount();
        this.updatePreview();
        
        textarea.focus();
    }

    wrapSelection(before, after) {
        const textarea = this.textarea;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        
        if (selectedText) {
            const wrappedText = before + selectedText + after;
            textarea.value = textarea.value.substring(0, start) + wrappedText + textarea.value.substring(end);
            textarea.selectionStart = start + before.length;
            textarea.selectionEnd = end + before.length;
        } else {
            this.insertAtCursor(before + after);
            textarea.selectionStart = textarea.selectionEnd = start + before.length;
        }
        
        // 触发input事件更新预览
        this.editorContent = textarea.value;
        this.updateWordCount();
        this.updatePreview();
        
        textarea.focus();
    }

    bindEvents() {
        // 左侧栏 Tab 切换
        document.getElementById('materialsTab').addEventListener('click', () => this.switchLeftTab('materials'));
        document.getElementById('filesTab').addEventListener('click', () => this.switchLeftTab('files'));
        
        // 素材管理
        document.getElementById('refreshMaterialsBtn').addEventListener('click', () => this.loadMaterials());
        document.getElementById('searchMaterialsBtn').addEventListener('click', () => this.toggleMaterialsSearch());
        document.getElementById('addMaterialBtn').addEventListener('click', () => this.addNewMaterial());
        document.getElementById('materialsSearchInput').addEventListener('input', (e) => this.handleMaterialsSearch(e.target.value));
        document.getElementById('materialsSearchCloseBtn').addEventListener('click', () => this.closeMaterialsSearch());
        document.getElementById('materialsTypeFilter').addEventListener('change', (e) => this.handleMaterialsFilter('type', e.target.value));
        document.getElementById('materialsCategoryFilter').addEventListener('change', (e) => this.handleMaterialsFilter('category', e.target.value));
        
        // 文件管理
        document.getElementById('refreshFilesBtn').addEventListener('click', () => this.loadFiles());
        document.getElementById('searchFilesBtn').addEventListener('click', () => this.toggleFilesSearch());
        document.getElementById('newFolderBtn').addEventListener('click', () => this.createNewFolder());
        document.getElementById('newFileBtn').addEventListener('click', () => this.createNewFile());
        document.getElementById('filesSearchInput').addEventListener('input', (e) => this.handleFilesSearch(e.target.value));
        document.getElementById('filesSearchCloseBtn').addEventListener('click', () => this.closeFilesSearch());
        
        // 右侧栏切换
        document.getElementById('previewToggleBtn').addEventListener('click', () => this.switchRightPanel('preview'));
        document.getElementById('aiToggleBtn').addEventListener('click', () => this.switchRightPanel('ai'));
        
        // 工具栏
        document.getElementById('saveDocBtn').addEventListener('click', () => this.saveDocument());
        document.getElementById('exportBtn').addEventListener('click', () => this.showExportDialog());
        document.getElementById('settingsBtn').addEventListener('click', () => this.openSettings());
        
        // 编辑器功能
        document.getElementById('boldBtn').addEventListener('click', () => this.formatText('bold'));
        document.getElementById('italicBtn').addEventListener('click', () => this.formatText('italic'));
        document.getElementById('linkBtn').addEventListener('click', () => this.insertLink());
        document.getElementById('imageBtn').addEventListener('click', () => this.insertImage());
        
        // 编辑器内容变化
        document.getElementById('editorContent').addEventListener('input', () => this.handleEditorChange());
        document.getElementById('documentTitle').addEventListener('input', () => this.handleTitleChange());
        
        // AI 功能
        document.getElementById('aiSendBtn').addEventListener('click', () => this.sendAIMessage());
        document.getElementById('aiClearBtn').addEventListener('click', () => this.clearAIHistory());
        document.getElementById('aiInput').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                this.sendAIMessage();
            }
        });
        
        // 文本处理按钮
        document.querySelectorAll('.text-processing-buttons button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                this.processSelectedText(action);
            });
        });
        
        // 编辑器选中文本监听
        document.addEventListener('selectionchange', () => this.handleTextSelection());
        
        // 面板关闭按钮
        document.getElementById('previewCloseBtn').addEventListener('click', () => this.hideRightPanel());
        document.getElementById('aiCloseBtn').addEventListener('click', () => this.hideRightPanel());
        
        // 模态窗口
        document.getElementById('modalClose').addEventListener('click', () => this.hideModal());
        document.getElementById('modal').addEventListener('click', (e) => {
            if (e.target.id === 'modal') this.hideModal();
        });
        
        // 键盘快捷键
        this.bindKeyboardShortcuts();
        
        // IPC 事件监听
        this.bindIPCEvents();
    }

    // 左侧栏 Tab 切换
    switchLeftTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(tabName + 'Tab').classList.add('active');
        
        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName + 'TabContent').classList.add('active');
        
        this.currentLeftTab = tabName;
        
        // 加载对应数据
        if (tabName === 'materials') {
            this.loadMaterials();
        } else if (tabName === 'files') {
            this.loadFiles();
        }
    }

    // 右侧栏切换
    switchRightPanel(panelName) {
        // 更新按钮状态
        document.getElementById('previewToggleBtn').classList.remove('active');
        document.getElementById('aiToggleBtn').classList.remove('active');
        document.getElementById(panelName + 'ToggleBtn').classList.add('active');
        
        // 更新面板显示
        document.querySelectorAll('.right-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(panelName + 'Panel').classList.add('active');
        
        this.currentRightPanel = panelName;
        
        // 如果切换到预览面板，更新预览内容
        if (panelName === 'preview') {
            this.updatePreview();
        }
    }

    hideRightPanel() {
        document.querySelectorAll('.right-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById('previewToggleBtn').classList.remove('active');
        document.getElementById('aiToggleBtn').classList.remove('active');
        this.currentRightPanel = null;
    }

    // 素材管理方法
    async loadMaterials() {
        try {
            this.showElementLoading('materialsList');
            
            const options = {
                orderBy: 'created_at',
                order: 'DESC'
            };
            
            if (this.filters.type) {
                options.type = this.filters.type;
            }
            
            if (this.filters.category) {
                options.category = this.filters.category;
            }
            
            if (this.searchQuery) {
                options.search = this.searchQuery;
            }
            
            const result = await ipcRenderer.invoke('get-collections', options);
            this.materials = Array.isArray(result) ? result : [];
            this.collections = this.materials; // 保持引用同步
            console.log('加载素材结果:', this.materials);
            this.renderMaterials();
            this.updateStatusBar();
            
        } catch (error) {
            console.error('加载素材失败:', error);
            this.materials = [];
            this.collections = this.materials;
            this.renderMaterials();
            this.showNotification('加载素材失败: ' + error.message, 'error');
        } finally {
            this.hideElementLoading('materialsList');
        }
    }

    renderMaterials() {
        const container = document.getElementById('materialsList');
        
        if (!this.materials || this.materials.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <h3>暂无素材</h3>
                    <p>点击"+"按钮添加新的素材</p>
                </div>
            `;
            return;
        }
        
        const html = this.materials.map(material => this.renderMaterialItem(material)).join('');
        container.innerHTML = html;
        
        // 绑定素材项事件
        container.querySelectorAll('.material-item').forEach(item => {
            item.addEventListener('click', (e) => this.selectMaterial(item.dataset.id, e));
            item.addEventListener('dblclick', () => this.editMaterial(item.dataset.id));
            item.addEventListener('dragstart', (e) => this.handleDragStart(e, item.dataset.id));
            
            // 拖拽结束时清理样式
            item.addEventListener('dragend', (e) => {
                e.target.closest('.material-item')?.classList.remove('dragging');
            });
            
            // 右键菜单
            item.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showMaterialContextMenu(e, item.dataset.id);
            });
        });
        
        this.updateBatchActions();
    }
    
    updateBatchActions() {
        const selectedCount = this.selectedMaterials.size;
        const batchActions = document.getElementById('batchActions');
        const selectAllBtn = document.getElementById('selectAllBtn');
        const batchInsertBtn = document.getElementById('batchInsertBtn');
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        
        if (batchActions) {
            if (selectedCount > 0) {
                batchActions.style.display = 'flex';
                
                // 更新选择全部按钮状态
                if (selectAllBtn) {
                    selectAllBtn.textContent = selectedCount === this.materials.length ? '取消全选' : '全选';
                }
                
                // 更新批量操作按钮状态
                if (batchInsertBtn) {
                    batchInsertBtn.textContent = `插入 ${selectedCount} 个素材`;
                    batchInsertBtn.disabled = false;
                }
                
                if (batchDeleteBtn) {
                    batchDeleteBtn.textContent = `删除 ${selectedCount} 个素材`;
                    batchDeleteBtn.disabled = false;
                }
            } else {
                batchActions.style.display = 'none';
            }
        }
    }
    
    selectAllMaterials() {
        if (this.selectedMaterials.size === this.materials.length) {
            // 取消全选
            this.selectedMaterials.clear();
        } else {
            // 全选
            this.selectedMaterials.clear();
            this.materials.forEach(material => {
                this.selectedMaterials.add(material.id);
            });
        }
        
        this.renderMaterials();
    }
    
    async insertSelectedMaterials() {
        if (this.selectedMaterials.size === 0) {
            this.showNotification('请先选择要插入的素材', 'warning');
            return;
        }
        
        const textarea = document.getElementById('markdownTextarea');
        if (!textarea) return;
        
        const insertPosition = textarea.selectionStart;
        let insertText = '';
        
        // 按顺序获取选中的素材
        const selectedMaterials = this.materials.filter(material => 
            this.selectedMaterials.has(material.id)
        );
        
        selectedMaterials.forEach((material, index) => {
            let materialText = '';
            
            switch (material.type) {
                case 'text':
                    materialText = material.content;
                    break;
                case 'link':
                    materialText = `[${material.content}](${material.content})`;
                    break;
                case 'image':
                    materialText = `![图片](${material.content})`;
                    break;
                case 'code':
                    materialText = `\`\`\`\n${material.content}\n\`\`\``;
                    break;
                default:
                    materialText = material.content;
            }
            
            insertText += materialText;
            
            // 添加分隔符（除了最后一个）
            if (index < selectedMaterials.length - 1) {
                insertText += '\n\n';
            }
        });
        
        // 插入内容
        const currentValue = textarea.value;
        const beforeText = currentValue.substring(0, insertPosition);
        const afterText = currentValue.substring(insertPosition);
        
        // 添加适当的换行符
        let prefix = '';
        let suffix = '';
        
        if (beforeText && !beforeText.endsWith('\n')) {
            prefix = '\n\n';
        } else if (beforeText && !beforeText.endsWith('\n\n')) {
            prefix = '\n';
        }
        
        if (afterText && !afterText.startsWith('\n')) {
            suffix = '\n\n';
        } else if (afterText && !afterText.startsWith('\n\n')) {
            suffix = '\n';
        }
        
        const fullInsertText = prefix + insertText + suffix;
        const newValue = beforeText + fullInsertText + afterText;
        
        textarea.value = newValue;
        textarea.selectionStart = textarea.selectionEnd = insertPosition + fullInsertText.length;
        
        // 更新内容并预览
        this.editorContent = newValue;
        this.updateWordCount();
        this.updatePreview();
        
        textarea.focus();
        this.showNotification(`已插入 ${selectedMaterials.length} 个素材`, 'success');
        
        // 清除选择
        this.selectedMaterials.clear();
        this.renderMaterials();
    }
    
    async batchDeleteMaterials() {
        if (this.selectedMaterials.size === 0) {
            this.showNotification('请先选择要删除的素材', 'warning');
            return;
        }
        
        const confirmed = confirm(`确定要删除选中的 ${this.selectedMaterials.size} 个素材吗？此操作无法撤销。`);
        if (!confirmed) return;
        
        try {
            const deletePromises = Array.from(this.selectedMaterials).map(id => 
                ipcRenderer.invoke('delete-collection', id)
            );
            
            await Promise.all(deletePromises);
            
            this.selectedMaterials.clear();
            this.loadMaterials();
            this.showNotification('批量删除成功', 'success');
            
        } catch (error) {
            console.error('批量删除失败:', error);
            this.showNotification('批量删除失败: ' + error.message, 'error');
        }
    }

    renderMaterialItem(material) {
        if (!material) return '';
        
        const isSelected = this.selectedMaterials.has(material.id);
        const typeIcon = this.getTypeIcon(material.type || 'text');
        
        // 截断长内容显示
        const content = material.content || '';
        const preview = this.generateContentPreview(content, material.type);
        
        // 生成内容第一行作为标题
        const firstLine = content.split('\n')[0].trim();
        const title = firstLine.length > 50 ? firstLine.substring(0, 50) + '...' : firstLine;
        
        return `
            <div class="material-item ${isSelected ? 'selected' : ''}" 
                 data-id="${material.id || ''}" 
                 data-type="${material.type || 'text'}"
                 draggable="true"
                 onclick="organizationApp.selectMaterial('${material.id}', event)">
                
                <!-- 选择指示器 -->
                <div class="material-selector">
                    <div class="material-checkbox ${isSelected ? 'checked' : ''}">
                        ${isSelected ? this.getIcon('check') : ''}
                    </div>
                </div>
                
                <!-- 主要内容区域 -->
                <div class="material-content-area">
                    <!-- 标题区域 -->
                    <div class="material-header">
                        <div class="material-icon">${typeIcon}</div>
                        <h3 class="material-title">${this.escapeHtml(title || '未命名素材')}</h3>
                    </div>
                    
                    <!-- 内容预览 -->
                    <div class="material-preview">${preview}</div>
                    
                    <!-- 元数据 -->
                    <div class="material-meta">
                        <div class="material-meta-left">
                            ${material.category ? `<span class="material-tag category">${this.escapeHtml(material.category)}</span>` : ''}
                            ${material.keywords ? material.keywords.split(',').map(k => 
                                `<span class="material-tag keyword">${this.escapeHtml(k.trim())}</span>`
                            ).join('') : ''}
                        </div>
                        <div class="material-meta-right">
                            <span class="material-time">${this.formatTime(material.timestamp || material.created_at)}</span>
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="material-actions">
                    <button class="material-action-btn" onclick="event.stopPropagation(); organizationApp.editMaterial('${material.id || ''}')" title="编辑">
                        ${this.getIcon('edit')}
                    </button>
                    <button class="material-action-btn danger" onclick="event.stopPropagation(); organizationApp.deleteMaterial('${material.id || ''}')" title="删除">
                        ${this.getIcon('delete')}
                    </button>
                </div>
                
                <!-- 拖拽指示器 -->
                <div class="material-drag-indicator">
                    <div class="drag-dots">
                        <div class="drag-dot"></div>
                        <div class="drag-dot"></div>
                        <div class="drag-dot"></div>
                        <div class="drag-dot"></div>
                    </div>
                </div>
            </div>
        `;
    }

    generateContentPreview(content, type) {
        if (!content) return '<span class="preview-empty">无内容</span>';
        
        // 根据类型生成不同的预览
        switch (type) {
            case 'image':
                // 如果是图片，显示图片预览或图片描述
                if (content.includes('data:image/') || content.startsWith('http')) {
                    return `<div class="preview-image">${this.getIcon('image')} 图片内容</div>`;
                }
                return this.truncateContent(content, 80);
                
            case 'link':
                try {
                    const url = new URL(content);
                    return `<div class="preview-link">
                        <span class="link-icon">${this.getIcon('link')}</span>
                        <span class="link-domain">${url.hostname}</span>
                        <span class="link-path">${url.pathname}</span>
                    </div>`;
                } catch (e) {
                    return this.truncateContent(content, 80);
                }
                
            case 'code':
                return `<div class="preview-code">${this.truncateContent(content, 60)}</div>`;
                
            default:
                return this.truncateContent(content, 100);
        }
    }

    truncateContent(content, maxLength) {
        // 移除多余的空白和换行
        const cleaned = content.replace(/\s+/g, ' ').trim();
        if (cleaned.length <= maxLength) {
            return this.escapeHtml(cleaned);
        }
        return this.escapeHtml(cleaned.substring(0, maxLength)) + '<span class="preview-more">...</span>';
    }

    selectMaterial(id, event) {
        if (event && event.ctrlKey || event && event.metaKey) {
            // Ctrl/Cmd + 点击：多选模式
            if (this.selectedMaterials.has(id)) {
                this.selectedMaterials.delete(id);
            } else {
                this.selectedMaterials.add(id);
            }
        } else if (event && event.shiftKey && this.lastSelectedMaterial) {
            // Shift + 点击：范围选择
            const materialsArray = this.materials.map(m => m.id);
            const lastIndex = materialsArray.indexOf(this.lastSelectedMaterial);
            const currentIndex = materialsArray.indexOf(id);
            
            if (lastIndex !== -1 && currentIndex !== -1) {
                const start = Math.min(lastIndex, currentIndex);
                const end = Math.max(lastIndex, currentIndex);
                
                for (let i = start; i <= end; i++) {
                    this.selectedMaterials.add(materialsArray[i]);
                }
            }
        } else {
            // 普通点击：单选模式
            if (this.selectedMaterials.has(id) && this.selectedMaterials.size === 1) {
                this.selectedMaterials.delete(id);
            } else {
                this.selectedMaterials.clear();
                this.selectedMaterials.add(id);
            }
        }
        
        this.lastSelectedMaterial = id;
        this.renderMaterials();
        this.updateBatchActions();
    }

    async editMaterial(id) {
        try {
            const material = await ipcRenderer.invoke('get-collection', id);
            this.showEditMaterialModal(material);
        } catch (error) {
            console.error('获取素材详情失败:', error);
            this.showNotification('获取素材详情失败: ' + error.message, 'error');
        }
    }

    async deleteMaterial(id) {
        const confirmed = confirm('确定要删除这个素材吗？此操作无法撤销。');
        if (!confirmed) return;
        
        try {
            await ipcRenderer.invoke('delete-collection', id);
            this.loadMaterials();
            this.selectedMaterials.delete(id);
            this.showNotification('素材已删除', 'success');
            
        } catch (error) {
            console.error('删除素材失败:', error);
            this.showNotification('删除素材失败: ' + error.message, 'error');
        }
    }

    addNewMaterial() {
        this.showAddMaterialModal();
    }

    toggleMaterialsSearch() {
        const container = document.getElementById('materialsSearchContainer');
        const input = document.getElementById('materialsSearchInput');
        
        if (container.style.display === 'none') {
            container.style.display = 'flex';
            input.focus();
        } else {
            this.closeMaterialsSearch();
        }
    }

    handleMaterialsSearch(query) {
        this.searchQuery = query;
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.loadMaterials();
        }, 300);
    }

    handleMaterialsFilter(type, value) {
        this.filters[type] = value;
        this.loadMaterials();
    }

    closeMaterialsSearch() {
        const container = document.getElementById('materialsSearchContainer');
        const input = document.getElementById('materialsSearchInput');
        
        container.style.display = 'none';
        input.value = '';
        this.searchQuery = '';
        this.loadMaterials();
    }

    bindKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + S: 保存
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.saveDocument();
            }
            
            // Ctrl/Cmd + F: 搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                this.showSearch();
            }
            
            // Ctrl/Cmd + N: 新建
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                this.newDocument();
            }
            
            // Ctrl/Cmd + O: AI分析
            if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
                e.preventDefault();
                this.triggerAIAnalysis();
            }
            
            // Ctrl/Cmd + P: 预览
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                this.togglePreview();
            }
            
            // Escape: 关闭模态或搜索
            if (e.key === 'Escape') {
                this.hideModal();
                this.closeSearch();
            }
        });
    }

    bindIPCEvents() {
        // 监听收藏数据加载
        ipcRenderer.on('collections-loaded', (event, collections) => {
            this.collections = collections;
            this.renderCollections();
        });
        
        // 监听AI分析结果
        ipcRenderer.on('ai-analysis-started', () => {
            this.showLoading('AI分析中...');
            document.getElementById('analyzeBtn').disabled = true;
        });
        
        ipcRenderer.on('ai-analysis-completed', (event, result) => {
            this.hideLoading();
            document.getElementById('analyzeBtn').disabled = false;
            
            if (result.success) {
                this.analysisResults = result.results;
                this.renderAnalysisResults();
                document.getElementById('generateBtn').disabled = false;
                this.showNotification('AI分析完成', 'success');
            } else {
                this.showNotification('AI分析失败: ' + (result.error || result.message), 'error');
            }
        });
        
        // 监听Markdown生成结果
        ipcRenderer.on('markdown-generation-started', () => {
            this.showLoading('生成Markdown中...');
            document.getElementById('generateBtn').disabled = true;
        });
        
        ipcRenderer.on('markdown-generation-completed', (event, result) => {
            this.hideLoading();
            document.getElementById('generateBtn').disabled = false;
            
            if (result.success) {
                this.insertMarkdown(result.markdown);
                this.showNotification('Markdown生成完成', 'success');
            } else {
                this.showNotification('Markdown生成失败: ' + result.error, 'error');
            }
        });
        
        // 监听重复检测结果
        ipcRenderer.on('duplicates-detected', (event, duplicates) => {
            this.showDuplicatesModal(duplicates);
        });
        
        // 监听关键词提取结果
        ipcRenderer.on('keywords-extracted', (event, results) => {
            this.showKeywordsModal(results);
        });
        
        // 监听统计信息
        ipcRenderer.on('show-statistics', (event, stats) => {
            this.showStatisticsModal(stats);
        });
        
        // 监听新建文档
        ipcRenderer.on('new-document', () => {
            this.newDocument();
        });
        
        // 监听打开文档
        ipcRenderer.on('open-document', (event, data) => {
            this.openDocumentContent(data.content, data.path);
        });
        
        // 监听保存请求
        ipcRenderer.on('save-document-request', () => {
            this.saveDocument();
        });
        
        ipcRenderer.on('save-document-as-request', () => {
            this.saveDocumentAs();
        });
        
        // 监听界面控制
        ipcRenderer.on('show-search', () => {
            this.showSearch();
        });
        
        ipcRenderer.on('show-replace', () => {
            // TODO: 实现替换功能
        });
        
        ipcRenderer.on('toggle-sidebar', () => {
            this.toggleSidebar();
        });
        
        ipcRenderer.on('toggle-preview', () => {
            this.togglePreview();
        });
    }

    async loadMaterials() {
        try {
            this.showElementLoading('materialsList');
            
            const options = {
                orderBy: 'created_at',
                order: 'DESC'
            };
            
            if (this.filters.type) {
                options.type = this.filters.type;
            }
            
            if (this.filters.category) {
                options.category = this.filters.category;
            }
            
            if (this.searchQuery) {
                options.search = this.searchQuery;
            }
            
            this.materials = await ipcRenderer.invoke('get-collections', options);
            this.collections = this.materials; // 保持向后兼容性
            this.renderMaterials();
            this.updateStatusBar();
            
        } catch (error) {
            console.error('加载素材失败:', error);
            this.showNotification('加载素材失败: ' + error.message, 'error');
        } finally {
            this.hideElementLoading('materialsList');
        }
    }

    renderMaterials() {
        const container = document.getElementById('materialsList');
        
        if (this.materials.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <h3>暂无素材</h3>
                    <p>点击"+"按钮添加新的素材，或切换到收集模式</p>
                </div>
            `;
            return;
        }
        
        const html = this.materials.map(material => this.renderMaterialItem(material)).join('');
        container.innerHTML = html;
        
        // 绑定素材项事件
        container.querySelectorAll('.material-item').forEach(item => {
            item.addEventListener('click', () => this.selectMaterial(item.dataset.id));
            item.addEventListener('dblclick', () => this.editMaterial(item.dataset.id));
            item.addEventListener('dragstart', (e) => this.handleDragStart(e, item.dataset.id));
            
            // 拖拽结束时清理样式
            item.addEventListener('dragend', (e) => {
                e.target.closest('.material-item')?.classList.remove('dragging');
            });
            
            // 右键菜单
            item.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showMaterialContextMenu(e, item.dataset.id);
            });
        });
    }

    renderMaterialItem(material) {
        const isSelected = this.selectedMaterials.has(material.id);
        const typeIcon = this.getTypeIcon(material.type);
        const categoryClass = material.category ? 'has-category' : '';
        
        // 根据类型渲染不同的内容
        let contentHtml = '';
        if (material.type === 'image' && material.image_data) {
            // 显示图片
            contentHtml = `
                <div class="material-image">
                    <img src="${material.image_data}" alt="图片素材" style="max-width: 100%; max-height: 150px; object-fit: contain;">
                </div>
                ${material.content ? `<div class="material-text">${this.escapeHtml(material.content.length > 50 ? material.content.substring(0, 50) + '...' : material.content)}</div>` : ''}
            `;
        } else {
            // 显示文本内容
            const truncatedContent = material.content.length > 100 
                ? material.content.substring(0, 100) + '...' 
                : material.content;
            contentHtml = `<div class="material-text">${this.escapeHtml(truncatedContent)}</div>`;
        }
        
        return `
            <div class="material-item ${isSelected ? 'selected' : ''} ${categoryClass}" 
                 data-id="${material.id}" 
                 draggable="true">
                <div class="material-header">
                    <div class="material-type-info">
                        <span class="material-type">${typeIcon} ${material.type}</span>
                        <span class="material-time">${this.formatTime(material.timestamp)}</span>
                    </div>
                    <div class="material-actions">
                        <button class="material-action-btn" onclick="organizationApp.editMaterial('${material.id}')" title="编辑">
                            ${this.getIcon('edit')}
                        </button>
                        <button class="material-action-btn" onclick="organizationApp.deleteMaterial('${material.id}')" title="删除">
                            ${this.getIcon('delete')}
                        </button>
                    </div>
                </div>
                <div class="material-content" title="${this.escapeHtml(material.content || '')}">
                    ${contentHtml}
                </div>
                <div class="material-meta">
                    ${material.category ? `<span class="material-category">${material.category}</span>` : ''}
                    ${material.keywords ? `<span class="material-keywords">${material.keywords}</span>` : ''}
                </div>
                <div class="material-overlay">
                    <div class="drag-handle" title="拖拽到编辑器">${this.getIcon('file')}</div>
                </div>
            </div>
        `;
    }

    getTypeIcon(type) {
        const icons = {
            text: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h10v2H7V7zm0 4h10v2H7v-2zm0 4h7v2H7v-2z"/></svg>',
            image: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M5 3h14c1.1 0 2 .9 2 2v14c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V5c0-1.1.9-2 2-2zm0 2v10.5l3.5-3.5 2.5 2.5L16.5 9 19 11.5V5H5zm0 14h14v-1.5L16.5 13 11 18.5 8.5 16 5 19.5V19z"/></svg>',
            link: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M10.59 13.41c.41.39.41 1.03 0 1.42-.39.39-1.03.39-1.42 0a5.003 5.003 0 0 1 0-7.07l3.54-3.54a5.003 5.003 0 0 1 7.07 0 5.003 5.003 0 0 1 0 7.07l-1.49 1.49c.01-.82-.12-1.64-.4-2.42l.47-.48a2.982 2.982 0 0 0 0-4.24 2.982 2.982 0 0 0-4.24 0l-3.53 3.53a2.982 2.982 0 0 0 0-4.24zm2.82-4.24c.39-.39 1.03-.39 1.42 0a5.003 5.003 0 0 1 0 7.07l-3.54 3.54a5.003 5.003 0 0 1-7.07 0 5.003 5.003 0 0 1 0-7.07l1.49-1.49c-.01.82.12 1.64.4 2.42l-.47.48a2.982 2.982 0 0 0 0 4.24 2.982 2.982 0 0 0 4.24 0l3.53-3.53a2.982 2.982 0 0 0 0-4.24.973.973 0 0 1 0-1.42z"/></svg>',
            file: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h10c1.1 0 2-.9 2-2V8l-6-6H6zm7 7V3.5L18.5 9H13z"/></svg>',
            code: '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M8.7 15.9L4.8 12l3.9-3.9c.39-.39.39-1.01 0-1.4s-1.01-.39-1.4 0l-4.59 4.59c-.39.39-.39 1.02 0 1.41l4.59 4.6c.39.39 1.01.39 1.4 0 .39-.39.39-1.01 0-1.4zm6.6 0l3.9-3.9-3.9-3.9c-.39-.39-.39-1.01 0-1.4s1.01-.39 1.4 0l4.59 4.59c.39.39.39 1.02 0 1.41L16.7 16.3c-.39.39-1.01.39-1.4 0-.39-.39-.39-1.01 0-1.4z"/></svg>'
        };
        return this.getIcon(type);
    }

    getIcon(iconName) {
        return this.icons[iconName] || this.icons.file;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
        if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
        if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';
        
        return date.toLocaleDateString('zh-CN');
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    selectMaterial(id) {
        if (this.selectedMaterials.has(id)) {
            this.selectedMaterials.delete(id);
        } else {
            this.selectedMaterials.add(id);
        }
        
        this.renderMaterials();
        this.updateAnalyzeButton();
    }

    updateAnalyzeButton() {
        const analyzeBtn = document.getElementById('analyzeBtn');
        analyzeBtn.disabled = this.selectedMaterials.size === 0;
    }

    async editMaterial(id) {
        try {
            const material = await ipcRenderer.invoke('get-collection', id);
            this.showEditMaterialModal(material);
        } catch (error) {
            console.error('获取素材详情失败:', error);
            this.showNotification('获取素材详情失败: ' + error.message, 'error');
        }
    }

    showEditMaterialModal(material) {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="material-edit-container">
                <div class="material-edit-header">
                    <div class="material-type-icon">${this.getTypeIcon(material.type)}</div>
                    <div class="material-edit-title">
                        <h3>编辑素材</h3>
                        <span class="material-edit-subtitle">修改素材的内容和属性</span>
                    </div>
                </div>

                <div class="material-edit-form">
                    <div class="form-row">
                        <div class="form-field form-field-full">
                            <label for="editContent" class="form-label">
                                <span class="label-text">内容</span>
                                <span class="label-required">*</span>
                            </label>
                            <textarea 
                                id="editContent" 
                                class="form-textarea" 
                                rows="4" 
                                placeholder="输入素材内容..."
                            >${this.escapeHtml(material.content)}</textarea>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-field">
                            <label for="editType" class="form-label">
                                <span class="label-text">类型</span>
                            </label>
                            <select id="editType" class="form-select">
                                <option value="text" ${material.type === 'text' ? 'selected' : ''}>文本</option>
                                <option value="image" ${material.type === 'image' ? 'selected' : ''}>图片</option>
                                <option value="link" ${material.type === 'link' ? 'selected' : ''}>链接</option>
                                <option value="code" ${material.type === 'code' ? 'selected' : ''}>代码</option>
                            </select>
                        </div>

                        <div class="form-field">
                            <label for="editCategory" class="form-label">
                                <span class="label-text">分类</span>
                            </label>
                            <select id="editCategory" class="form-select">
                                <option value="" ${!material.category ? 'selected' : ''}>无分类</option>
                                <option value="技术文档" ${material.category === '技术文档' ? 'selected' : ''}>技术文档</option>
                                <option value="学习笔记" ${material.category === '学习笔记' ? 'selected' : ''}>学习笔记</option>
                                <option value="工作相关" ${material.category === '工作相关' ? 'selected' : ''}>工作相关</option>
                                <option value="生活记录" ${material.category === '生活记录' ? 'selected' : ''}>生活记录</option>
                                <option value="想法灵感" ${material.category === '想法灵感' ? 'selected' : ''}>想法灵感</option>
                                <option value="参考资料" ${material.category === '参考资料' ? 'selected' : ''}>参考资料</option>
                                <option value="其他" ${material.category === '其他' ? 'selected' : ''}>其他</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-field">
                            <label for="editKeywords" class="form-label">
                                <span class="label-text">关键词</span>
                                <span class="label-hint">用逗号分隔</span>
                            </label>
                            <input 
                                type="text" 
                                id="editKeywords" 
                                class="form-input" 
                                value="${material.keywords || ''}" 
                                placeholder="例如：学习, 笔记, 重要"
                            >
                        </div>

                        <div class="form-field">
                            <label for="editSource" class="form-label">
                                <span class="label-text">来源</span>
                            </label>
                            <input 
                                type="text" 
                                id="editSource" 
                                class="form-input" 
                                value="${material.source || ''}" 
                                placeholder="素材来源"
                            >
                        </div>
                    </div>
                </div>

                <div class="material-edit-actions">
                    <button type="button" class="btn btn-secondary" onclick="organizationApp.hideModal()">
                        <span class="btn-icon">${this.getIcon('close')}</span>
                        取消
                    </button>
                    <button type="button" class="btn btn-danger" onclick="organizationApp.deleteMaterial('${material.id}')">
                        <span class="btn-icon">${this.getIcon('delete')}</span>
                        删除
                    </button>
                    <button type="button" class="btn btn-primary" onclick="organizationApp.saveMaterialEdit('${material.id}')">
                        <span class="btn-icon">${this.getIcon('save')}</span>
                        保存更改
                    </button>
                </div>
            </div>
        `;
        
        this.showModal('');
    }

    async saveMaterialEdit(id) {
        try {
            const content = document.getElementById('editContent').value.trim();
            if (!content) {
                this.showNotification('请输入素材内容', 'warning');
                return;
            }

            const data = {
                content: content,
                type: document.getElementById('editType').value,
                category: document.getElementById('editCategory').value || null,
                keywords: document.getElementById('editKeywords').value.trim() || null,
                source: document.getElementById('editSource').value.trim() || null
            };
            
            await ipcRenderer.invoke('update-collection', id, data);
            this.hideModal();
            this.loadMaterials();
            this.showNotification('素材已更新', 'success');
            
        } catch (error) {
            console.error('更新素材失败:', error);
            this.showNotification('更新素材失败: ' + error.message, 'error');
        }
    }

    async deleteMaterial(id) {
        const confirmed = confirm('确定要删除这个素材吗？此操作无法撤销。');
        if (!confirmed) return;
        
        try {
            await ipcRenderer.invoke('delete-collection', id);
            this.hideModal();
            this.loadMaterials();
            this.selectedMaterials.delete(id);
            this.showNotification('素材已删除', 'success');
            
        } catch (error) {
            console.error('删除素材失败:', error);
            this.showNotification('删除素材失败: ' + error.message, 'error');
        }
    }

    handleSearch(query) {
        this.searchQuery = query;
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.loadCollections();
        }, 300);
    }

    toggleSearch() {
        const container = document.getElementById('searchContainer');
        const input = document.getElementById('searchInput');
        
        if (container.style.display === 'none') {
            container.style.display = 'flex';
            input.focus();
        } else {
            this.closeSearch();
        }
    }

    closeSearch() {
        const container = document.getElementById('searchContainer');
        const input = document.getElementById('searchInput');
        
        container.style.display = 'none';
        input.value = '';
        this.searchQuery = '';
        this.loadCollections();
    }

    showSearch() {
        this.toggleSearch();
    }

    addNewCollection() {
        // 显示添加收藏的模态框
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="add-collection-form">
                <div class="form-group">
                    <label for="newContent">内容</label>
                    <textarea id="newContent" rows="6" placeholder="输入收藏内容..."></textarea>
                </div>
                <div class="form-group">
                    <label for="newType">类型</label>
                    <select id="newType">
                        <option value="text">文本</option>
                        <option value="image">图片</option>
                        <option value="link">链接</option>
                        <option value="file">文件</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="newCategory">分类</label>
                    <input type="text" id="newCategory" placeholder="输入分类...">
                </div>
                <div class="form-group">
                    <label for="newKeywords">关键词</label>
                    <input type="text" id="newKeywords" placeholder="用逗号分隔">
                </div>
                <div class="form-group">
                    <label for="newSource">来源</label>
                    <input type="text" id="newSource" placeholder="输入来源...">
                </div>
                <div class="form-actions">
                    <button class="btn-secondary" onclick="organizationApp.hideModal()">取消</button>
                    <button class="btn-primary" onclick="organizationApp.saveNewCollection()">保存</button>
                </div>
            </div>
        `;
        
        document.getElementById('modalTitle').textContent = '添加收藏';
        this.showModal();
    }

    async saveNewCollection() {
        try {
            const content = document.getElementById('newContent').value.trim();
            const type = document.getElementById('newType').value;
            const category = document.getElementById('newCategory').value.trim();
            const keywords = document.getElementById('newKeywords').value.trim();
            const source = document.getElementById('newSource').value.trim();
            
            if (!content) {
                this.showNotification('请输入收藏内容', 'warning');
                return;
            }
            
            const newCollection = {
                content,
                type,
                category: category || null,
                keywords: keywords || null,
                source: source || null,
                timestamp: new Date().toISOString()
            };
            
            await ipcRenderer.invoke('quick-capture', newCollection);
            
            this.hideModal();
            this.loadCollections();
            this.showNotification('收藏添加成功', 'success');
            
        } catch (error) {
            console.error('添加收藏失败:', error);
            this.showNotification('添加收藏失败: ' + error.message, 'error');
        }
    }

    showModal() {
        document.getElementById('modal').style.display = 'flex';
    }

    hideModal() {
        document.getElementById('modal').style.display = 'none';
    }

    handleFilter(type, value) {
        this.filters[type] = value;
        this.loadCollections();
    }

    async triggerAIAnalysis() {
        if (this.selectedCollections.size === 0) {
            this.showNotification('请先选择要分析的收藏', 'warning');
            return;
        }
        
        try {
            const selectedItems = this.collections.filter(c => this.selectedCollections.has(c.id));
            await ipcRenderer.invoke('analyze-collections', selectedItems);
        } catch (error) {
            console.error('AI分析失败:', error);
            this.showNotification('AI分析失败: ' + error.message, 'error');
        }
    }

    renderAnalysisResults() {
        const container = document.getElementById('aiResults');
        
        if (!this.analysisResults || !this.analysisResults.categories) {
            container.innerHTML = '<div class="ai-placeholder">暂无分析结果</div>';
            return;
        }
        
        const categoriesHtml = Object.entries(this.analysisResults.categories)
            .map(([name, category]) => this.renderAICategory(name, category))
            .join('');
        
        const insightsHtml = this.analysisResults.insights ? 
            `<div class="ai-insights">
                <h4>💡 分析建议</h4>
                <p>${this.escapeHtml(this.analysisResults.insights)}</p>
            </div>` : '';
        
        container.innerHTML = categoriesHtml + insightsHtml;
    }

    renderAICategory(name, category) {
        const keywordsHtml = category.keywords
            ? category.keywords.map(kw => `<span class="ai-keyword">${this.escapeHtml(kw)}</span>`).join('')
            : '';
        
        return `
            <div class="ai-category">
                <div class="ai-category-header">
                    <span class="ai-category-title">${this.escapeHtml(name)}</span>
                    <span class="ai-category-count">${category.items.length} 项</span>
                </div>
                <div class="ai-category-summary">${this.escapeHtml(category.summary)}</div>
                <div class="ai-category-keywords">${keywordsHtml}</div>
            </div>
        `;
    }

    async generateMarkdown() {
        if (!this.analysisResults) {
            this.showNotification('请先进行AI分析', 'warning');
            return;
        }
        
        try {
            const selectedItems = this.collections.filter(c => this.selectedCollections.has(c.id));
            await ipcRenderer.invoke('generate-markdown', this.analysisResults.categories, selectedItems);
        } catch (error) {
            console.error('生成Markdown失败:', error);
            this.showNotification('生成Markdown失败: ' + error.message, 'error');
        }
    }

    insertMarkdown(markdown) {
        const title = document.getElementById('documentTitle');
        
        if (!title.value) {
            title.value = '整理文档 - ' + new Date().toLocaleDateString('zh-CN');
        }
        
        if (this.markdownEditor) {
            this.markdownEditor.setContent(markdown);
        }
        
        this.editorContent = markdown;
        this.updateWordCount();
    }

    // 编辑器功能
    newDocument() {
        const title = document.getElementById('documentTitle');
        const textarea = document.getElementById('markdownTextarea');
        
        title.value = '';
        
        const newContent = '# 新文档\n\n开始编写你的内容...';
        
        if (textarea) {
            textarea.value = newContent;
        } else if (this.markdownEditor) {
            this.markdownEditor.setContent(newContent);
        }
        
        this.editorContent = newContent;
        this.currentDocument = null;
        this.updateWordCount();
        this.updatePreview();
        
        // 聚焦到编辑器
        if (textarea) {
            textarea.focus();
        }
    }

    async openDocument() {
        try {
            const result = await ipcRenderer.invoke('show-open-dialog', {
                filters: [
                    { name: 'Markdown', extensions: ['md', 'markdown'] },
                    { name: 'Text', extensions: ['txt'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });
            
            if (!result.canceled && result.filePaths.length > 0) {
                const content = await ipcRenderer.invoke('read-file', result.filePaths[0]);
                this.openDocumentContent(content, result.filePaths[0]);
            }
        } catch (error) {
            console.error('打开文档失败:', error);
            this.showNotification('打开文档失败: ' + error.message, 'error');
        }
    }

    openDocumentContent(content, filePath) {
        const title = document.getElementById('documentTitle');
        
        this.editorContent = content;
        
        if (this.markdownEditor) {
            this.markdownEditor.setContent(content);
        }
        
        if (filePath) {
            const fileName = filePath.split(/[/\\]/).pop().replace(/\.[^/.]+$/, '');
            title.value = fileName;
        }
        
        this.currentDocument = { path: filePath, content };
        this.updateWordCount();
        this.showNotification('文档已打开', 'success');
    }

    async saveDocument() {
        try {
            // 获取当前编辑器内容
            const textarea = document.getElementById('markdownTextarea');
            if (textarea) {
                this.editorContent = textarea.value;
            } else if (this.markdownEditor) {
                this.editorContent = this.markdownEditor.getContent();
            }
            
            const title = document.getElementById('documentTitle').value || '未命名文档';
            
            if (this.currentDocument && this.currentDocument.id) {
                // 更新现有文档
                await ipcRenderer.invoke('update-document', this.currentDocument.id, {
                    title: title,
                    content: this.editorContent
                });
                this.currentDocument.title = title;
                this.currentDocument.content = this.editorContent;
                this.showNotification('文档已保存', 'success');
                
                // 刷新文件列表
                this.loadFiles();
            } else {
                // 保存为新文档
                const result = await ipcRenderer.invoke('save-document', {
                    title: title,
                    content: this.editorContent,
                    folder: null
                });
                
                this.currentDocument = result;
                this.showNotification('文档已保存', 'success');
                
                // 刷新文件列表
                this.loadFiles();
            }
        } catch (error) {
            console.error('保存文档失败:', error);
            this.showNotification('保存文档失败: ' + error.message, 'error');
        }
    }

    async saveDocumentAs() {
        try {
            // 确保获取最新的编辑器内容
            const textarea = document.getElementById('markdownTextarea');
            if (textarea) {
                this.editorContent = textarea.value;
            } else if (this.markdownEditor) {
                this.editorContent = this.markdownEditor.getContent();
            }
            
            const title = document.getElementById('documentTitle').value || '未命名文档';
            
            const result = await ipcRenderer.invoke('show-save-dialog', {
                defaultPath: title + '.md',
                filters: [
                    { name: 'Markdown', extensions: ['md'] },
                    { name: 'Text', extensions: ['txt'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });
            
            if (!result.canceled && result.filePath) {
                await ipcRenderer.invoke('write-file', result.filePath, this.editorContent);
                this.currentDocument = { path: result.filePath, content: this.editorContent };
                this.showNotification('文档已保存', 'success');
            }
        } catch (error) {
            console.error('另存为失败:', error);
            this.showNotification('另存为失败: ' + error.message, 'error');
        }
    }

    handleEditorChange() {
        if (this.textarea) {
            this.editorContent = this.textarea.value;
            this.updateWordCount();
            this.updatePreview();
        }
    }

    handleTitleChange() {
        // 标题变化处理
    }

    formatText(format) {
        if (!this.textarea) return;
        
        switch (format) {
            case 'bold':
                this.wrapSelection('**', '**');
                break;
            case 'italic':
                this.wrapSelection('*', '*');
                break;
        }
    }

    insertLink() {
        const url = prompt('请输入链接地址:');
        if (!url) return;
        
        const text = prompt('请输入链接文本:') || url;
        const linkText = `[${text}](${url})`;
        this.insertAtCursor(linkText);
    }

    insertImage() {
        const url = prompt('请输入图片地址:');
        if (!url) return;
        
        const alt = prompt('请输入图片描述:') || '图片';
        const imageText = `![${alt}](${url})`;
        this.insertAtCursor(imageText);
    }

    insertTextAtCursor(text) {
        // 兼容旧方法，调用新方法
        this.insertAtCursor(text);
    }

    updateWordCount() {
        const content = this.editorContent || '';
        const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
        const charCount = content.length;
        
        const wordCountEl = document.getElementById('wordCount');
        const charCountEl = document.getElementById('charCount');
        
        if (wordCountEl) wordCountEl.textContent = `${wordCount} 字`;
        if (charCountEl) charCountEl.textContent = `${charCount} 字符`;
    }

    togglePreview() {
        const previewPanel = document.getElementById('previewPanel');
        const isVisible = previewPanel.style.display !== 'none';
        
        if (isVisible) {
            this.hidePreview();
        } else {
            this.showPreview();
        }
    }

    showPreview() {
        const previewPanel = document.getElementById('previewPanel');
        previewPanel.style.display = 'flex';
        this.isPreviewVisible = true;
        this.updatePreview();
    }

    hidePreview() {
        const previewPanel = document.getElementById('previewPanel');
        previewPanel.style.display = 'none';
        this.isPreviewVisible = false;
    }

    updatePreview() {
        if (this.currentRightPanel !== 'preview') return;
        
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;
        
        try {
            const content = this.editorContent || '';
            if (!content || content.trim() === '') {
                previewContent.innerHTML = '<div class="preview-placeholder">在左侧编辑器中输入内容以查看预览</div>';
                return;
            }
            
            const markdownHtml = this.renderMarkdown(content);
            previewContent.innerHTML = `<div class="preview-content">${markdownHtml}</div>`;
            
            // 如果预览面板可见，同步滚动
            if (this.textarea) {
                this.syncPreviewScroll();
            }
        } catch (error) {
            console.error('预览更新失败:', error);
            previewContent.innerHTML = '<div class="preview-placeholder">预览生成失败</div>';
        }
    }

    syncPreviewScroll() {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent || !this.textarea) return;
        
        const scrollRatio = this.textarea.scrollTop / (this.textarea.scrollHeight - this.textarea.clientHeight);
        previewContent.scrollTop = scrollRatio * (previewContent.scrollHeight - previewContent.clientHeight);
    }

    renderMarkdown(content) {
        if (!content) return '<p class="empty-preview">预览内容为空</p>';
        
        // 转义HTML特殊字符
        let html = content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');
        
        // 代码块（需要在其他处理之前）
        html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
            const language = lang || '';
            return `<pre><code class="language-${language}">${code.trim()}</code></pre>`;
        });
        
        // 行内代码
        html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        // 标题
        html = html.replace(/^# (.+)$/gm, '<h1>$1</h1>');
        html = html.replace(/^## (.+)$/gm, '<h2>$1</h2>');
        html = html.replace(/^### (.+)$/gm, '<h3>$1</h3>');
        html = html.replace(/^#### (.+)$/gm, '<h4>$1</h4>');
        html = html.replace(/^##### (.+)$/gm, '<h5>$1</h5>');
        html = html.replace(/^###### (.+)$/gm, '<h6>$1</h6>');
        
        // 水平线
        html = html.replace(/^---$/gm, '<hr>');
        html = html.replace(/^\*\*\*$/gm, '<hr>');
        
        // 引用
        html = html.replace(/^&gt; (.+)$/gm, '<blockquote>$1</blockquote>');
        
        // 加粗和斜体
        html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
        html = html.replace(/\*(.+?)\*/g, '<em>$1</em>');
        html = html.replace(/__(.+?)__/g, '<strong>$1</strong>');
        html = html.replace(/_(.+?)_/g, '<em>$1</em>');
        
        // 删除线
        html = html.replace(/~~(.+?)~~/g, '<del>$1</del>');
        
        // 链接
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
        
        // 图片
        html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1">');
        
        // 无序列表
        const lines = html.split('\n');
        let inUl = false;
        let inOl = false;
        const processedLines = [];
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const ulMatch = line.match(/^\s*[\*\-] (.+)$/);
            const olMatch = line.match(/^\s*(\d+)\. (.+)$/);
            
            if (ulMatch) {
                if (inOl) {
                    processedLines.push('</ol>');
                    inOl = false;
                }
                if (!inUl) {
                    processedLines.push('<ul>');
                    inUl = true;
                }
                processedLines.push(`<li>${ulMatch[1]}</li>`);
            } else if (olMatch) {
                if (inUl) {
                    processedLines.push('</ul>');
                    inUl = false;
                }
                if (!inOl) {
                    processedLines.push('<ol>');
                    inOl = true;
                }
                processedLines.push(`<li>${olMatch[2]}</li>`);
            } else {
                if (inUl) {
                    processedLines.push('</ul>');
                    inUl = false;
                }
                if (inOl) {
                    processedLines.push('</ol>');
                    inOl = false;
                }
                processedLines.push(line);
            }
        }
        
        // 关闭未闭合的列表
        if (inUl) processedLines.push('</ul>');
        if (inOl) processedLines.push('</ol>');
        
        html = processedLines.join('\n');
        
        // 表格（简单支持）
        html = html.replace(/\|(.+)\|/g, (match, content) => {
            const cells = content.split('|').map(cell => cell.trim());
            return '<tr>' + cells.map(cell => `<td>${cell}</td>`).join('') + '</tr>';
        });
        
        html = html.replace(/(<tr>.*<\/tr>)/s, '<table>$1</table>');
        
        // 段落处理
        const paragraphs = html.split(/\n\s*\n/);
        const processedParagraphs = paragraphs.map(paragraph => {
            paragraph = paragraph.trim();
            if (!paragraph) return '';
            
            // 跳过已经是HTML标签的内容
            if (paragraph.match(/^<(h[1-6]|ul|ol|pre|blockquote|hr|table)/)) {
                return paragraph;
            }
            
            // 包装为段落
            return `<p>${paragraph}</p>`;
        });
        
        html = processedParagraphs.filter(p => p).join('\n');
        
        // 清理多余的换行
        html = html.replace(/\n+/g, '\n');
        
        return html;
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('hidden');
    }

    // 拖拽功能
    initializeDragAndDrop() {
        if (!this.textarea || !this.markdownEditor) return;
        
        // 使用MarkdownEditor的拖拽功能，但需要集成我们的素材处理逻辑
        console.log('集成拖拽功能到MarkdownEditor');
        
        // 确保MarkdownEditor已经初始化了拖拽功能
        if (this.markdownEditor.bindDragEvents) {
            this.markdownEditor.bindDragEvents();
        }
        
        // 监听来自MarkdownEditor的拖拽事件
        this.textarea.addEventListener('materialInserted', (e) => {
            console.log('素材已插入:', e.detail);
            this.updatePreview();
            this.updateWordCount();
        });
    }

    showDragIndicator(x, y) {
        if (!this.dragIndicator) {
            this.dragIndicator = document.createElement('div');
            this.dragIndicator.className = 'drag-indicator';
            this.dragIndicator.style.cssText = `
                position: fixed;
                width: 3px;
                height: 24px;
                background: linear-gradient(180deg, #3b82f6, #1d4ed8);
                border-radius: 2px;
                pointer-events: none;
                z-index: 1000;
                opacity: 0.9;
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
                animation: dragIndicatorPulse 1.2s infinite;
            `;
            
            // 添加CSS动画
            if (!document.getElementById('drag-indicator-styles')) {
                const style = document.createElement('style');
                style.id = 'drag-indicator-styles';
                style.textContent = `
                    @keyframes dragIndicatorPulse {
                        0%, 100% { 
                            opacity: 0.9; 
                            transform: scaleX(1);
                        }
                        50% { 
                            opacity: 1; 
                            transform: scaleX(1.5);
                        }
                    }
                    
                    .drag-indicator::before {
                        content: '';
                        position: absolute;
                        top: -3px;
                        left: -2px;
                        width: 7px;
                        height: 7px;
                        background: #3b82f6;
                        border-radius: 50%;
                        opacity: 0.8;
                    }
                    
                    .drag-indicator::after {
                        content: '';
                        position: absolute;
                        bottom: -3px;
                        left: -2px;
                        width: 7px;
                        height: 7px;
                        background: #3b82f6;
                        border-radius: 50%;
                        opacity: 0.8;
                    }
                `;
                document.head.appendChild(style);
            }
            
            document.body.appendChild(this.dragIndicator);
        }
        
        // 计算更精确的位置 - 使用textarea内的位置
        const textareaRect = this.textarea.getBoundingClientRect();
        const targetPosition = this.getTextPositionFromPoint(x, y);
        
        // 根据文本位置计算实际的屏幕坐标
        const coords = this.getCoordinatesFromTextPosition(targetPosition);
        
        this.dragIndicator.style.left = (coords.x || x) + 'px';
        this.dragIndicator.style.top = (coords.y || y - 12) + 'px';
        this.dragIndicator.style.display = 'block';
    }
    
    getCoordinatesFromTextPosition(position) {
        if (!this.textarea) return { x: 0, y: 0 };
        
        try {
            // 临时设置光标位置来获取坐标
            const originalStart = this.textarea.selectionStart;
            const originalEnd = this.textarea.selectionEnd;
            
            this.textarea.setSelectionRange(position, position);
            
            // 创建一个临时的测量元素
            const measureElement = document.createElement('span');
            measureElement.style.cssText = `
                position: absolute;
                visibility: hidden;
                white-space: pre;
                font-family: ${window.getComputedStyle(this.textarea).fontFamily};
                font-size: ${window.getComputedStyle(this.textarea).fontSize};
                font-weight: ${window.getComputedStyle(this.textarea).fontWeight};
                line-height: ${window.getComputedStyle(this.textarea).lineHeight};
            `;
            
            const textBeforeCursor = this.textarea.value.substring(0, position);
            const lines = textBeforeCursor.split('\n');
            const currentLine = lines.length - 1;
            const currentCol = lines[currentLine].length;
            
            // 计算行的位置
            const lineHeight = this.getActualLineHeight();
            const textareaRect = this.textarea.getBoundingClientRect();
            const style = window.getComputedStyle(this.textarea);
            
            const x = textareaRect.left + 
                     parseInt(style.paddingLeft) + 
                     currentCol * this.getCharacterMetrics().average;
            const y = textareaRect.top + 
                     parseInt(style.paddingTop) + 
                     currentLine * lineHeight + lineHeight / 2;
            
            // 恢复原始选择
            this.textarea.setSelectionRange(originalStart, originalEnd);
            
            return { x, y };
        } catch (e) {
            console.warn('Failed to get coordinates from text position:', e);
            return { x: 0, y: 0 };
        }
    }

    hideDragIndicator() {
        if (this.dragIndicator) {
            this.dragIndicator.style.display = 'none';
        }
    }

    getTextPositionFromPoint(clientX, clientY) {
        if (!this.textarea) return 0;
        
        try {
            // 使用现代浏览器的原生API获取精确位置
            if (document.caretPositionFromPoint) {
                const range = document.caretPositionFromPoint(clientX, clientY);
                if (range && range.offsetNode) {
                    // 创建一个临时的Range来计算位置
                    const tempRange = document.createRange();
                    tempRange.setStart(this.textarea, 0);
                    tempRange.setEnd(range.offsetNode, range.offset);
                    return tempRange.toString().length;
                }
            } else if (document.caretRangeFromPoint) {
                const range = document.caretRangeFromPoint(clientX, clientY);
                if (range) {
                    const tempRange = document.createRange();
                    tempRange.setStart(this.textarea, 0);
                    tempRange.setEnd(range.startContainer, range.startOffset);
                    return tempRange.toString().length;
                }
            }
        } catch (e) {
            console.warn('Native caret position API failed, using fallback');
        }
        
        // 回退到更精确的计算方法
        const rect = this.textarea.getBoundingClientRect();
        const style = window.getComputedStyle(this.textarea);
        
        // 获取更精确的位置计算
        const paddingLeft = parseInt(style.paddingLeft) || 0;
        const paddingTop = parseInt(style.paddingTop) || 0;
        const borderLeft = parseInt(style.borderLeftWidth) || 0;
        const borderTop = parseInt(style.borderTopWidth) || 0;
        
        const x = clientX - rect.left - paddingLeft - borderLeft;
        const y = clientY - rect.top - paddingTop - borderTop;
        
        // 获取准确的行高和字体度量
        const lineHeight = this.getActualLineHeight();
        const charMetrics = this.getCharacterMetrics();
        
        const lines = this.textarea.value.split('\n');
        const targetLine = Math.max(0, Math.min(Math.floor(y / lineHeight), lines.length - 1));
        
        // 使用更精确的字符宽度计算
        let targetCol = 0;
        if (lines[targetLine]) {
            const lineText = lines[targetLine];
            let accumulatedWidth = 0;
            
            for (let i = 0; i <= lineText.length; i++) {
                const charWidth = this.getCharWidth(lineText[i] || ' ', charMetrics);
                if (accumulatedWidth + charWidth / 2 > x) {
                    targetCol = i;
                    break;
                }
                accumulatedWidth += charWidth;
                targetCol = i + 1;
            }
        }
        
        // 计算最终位置
        let position = 0;
        for (let i = 0; i < targetLine; i++) {
            position += lines[i].length + 1; // +1 for newline
        }
        position += Math.min(targetCol, lines[targetLine] ? lines[targetLine].length : 0);
        
        return position;
    }
    
    getActualLineHeight() {
        if (!this.textarea) return 20;
        
        // 创建一个测试元素来获取实际行高
        const testDiv = document.createElement('div');
        const style = window.getComputedStyle(this.textarea);
        
        testDiv.style.cssText = `
            position: absolute;
            visibility: hidden;
            font-family: ${style.fontFamily};
            font-size: ${style.fontSize};
            font-weight: ${style.fontWeight};
            line-height: ${style.lineHeight};
            white-space: pre;
        `;
        testDiv.textContent = 'Ag\nAg';
        document.body.appendChild(testDiv);
        
        const height = testDiv.offsetHeight / 2;
        document.body.removeChild(testDiv);
        
        return height || 20;
    }
    
    getCharacterMetrics() {
        if (!this.textarea) return { average: 8 };
        
        // 缓存字符度量以提高性能
        if (this._charMetrics) return this._charMetrics;
        
        const testDiv = document.createElement('div');
        const style = window.getComputedStyle(this.textarea);
        
        testDiv.style.cssText = `
            position: absolute;
            visibility: hidden;
            font-family: ${style.fontFamily};
            font-size: ${style.fontSize};
            font-weight: ${style.fontWeight};
            white-space: pre;
        `;
        
        // 测试常见字符的宽度
        const testChars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 .,;:!?';
        testDiv.textContent = testChars;
        document.body.appendChild(testDiv);
        
        const averageWidth = testDiv.offsetWidth / testChars.length;
        document.body.removeChild(testDiv);
        
        this._charMetrics = { average: averageWidth || 8 };
        return this._charMetrics;
    }
    
    getCharWidth(char, metrics) {
        // 为不同字符类型提供更准确的宽度估算
        if (!char || char === '\n') return 0;
        
        // 中文字符通常比英文字符宽
        if (/[\u4e00-\u9fff]/.test(char)) {
            return metrics.average * 1.8;
        }
        
        // 数字和大部分英文字符
        if (/[0-9a-zA-Z]/.test(char)) {
            return metrics.average;
        }
        
        // 标点符号通常较窄
        if (/[.,;:!?\s]/.test(char)) {
            return metrics.average * 0.6;
        }
        
        // 其他字符使用平均宽度
        return metrics.average;
    }

    handleDragStart(e, collectionId) {
        e.dataTransfer.setData('text/plain', collectionId);
        e.target.classList.add('dragging');
        
        // 设置拖拽效果
        e.dataTransfer.effectAllowed = 'copy';
        
        setTimeout(() => {
            e.target.classList.remove('dragging');
        }, 100);
    }

    async insertCollectionContentAtPosition(collectionId, position) {
        try {
            const collection = this.materials.find(c => c.id === collectionId);
            if (!collection) return;
            
            let insertText = '';
            switch (collection.type) {
                case 'text':
                    insertText = collection.content;
                    break;
                case 'link':
                    insertText = `[${collection.content}](${collection.content})`;
                    break;
                case 'image':
                    if (collection.image_data) {
                        // 使用图片数据创建markdown图片语法
                        const altText = collection.content || '图片';
                        insertText = `![${altText}](${collection.image_data})`;
                    } else {
                        // 如果没有图片数据，则插入文本内容
                        insertText = collection.content || '图片';
                    }
                    break;
                case 'code':
                    insertText = `\`\`\`\n${collection.content}\n\`\`\``;
                    break;
                default:
                    insertText = collection.content;
            }
            
            // 在指定位置插入内容
            const textarea = this.textarea;
            const currentValue = textarea.value;
            const beforeText = currentValue.substring(0, position);
            const afterText = currentValue.substring(position);
            
            // 添加适当的换行符
            let prefix = '';
            let suffix = '';
            
            if (beforeText && !beforeText.endsWith('\n')) {
                prefix = '\n\n';
            } else if (beforeText && !beforeText.endsWith('\n\n')) {
                prefix = '\n';
            }
            
            if (afterText && !afterText.startsWith('\n')) {
                suffix = '\n\n';
            } else if (afterText && !afterText.startsWith('\n\n')) {
                suffix = '\n';
            }
            
            const fullInsertText = prefix + insertText + suffix;
            const newValue = beforeText + fullInsertText + afterText;
            
            textarea.value = newValue;
            textarea.selectionStart = textarea.selectionEnd = position + fullInsertText.length;
            
            // 更新内容并预览
            this.editorContent = newValue;
            this.updateWordCount();
            this.updatePreview();
            
            textarea.focus();
            this.showNotification('内容已插入到指定位置', 'success');
            
        } catch (error) {
            console.error('插入内容失败:', error);
            this.showNotification('插入内容失败: ' + error.message, 'error');
        }
    }

    // 兼容旧方法
    async insertCollectionContent(collectionId) {
        if (this.textarea) {
            const position = this.textarea.selectionStart;
            await this.insertCollectionContentAtPosition(collectionId, position);
        }
    }

    // 工具函数
    showModal(title) {
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modal').style.display = 'flex';
    }

    hideModal() {
        document.getElementById('modal').style.display = 'none';
    }

    showInputModal(title, message, defaultValue = '', callback) {
        // 设置模态框内容
        document.getElementById('modalBody').innerHTML = `
            <div class="input-modal-form">
                <div class="form-group">
                    <label class="form-label">${message}</label>
                    <input type="text" id="modalInput" class="form-input" value="${this.escapeHtml(defaultValue)}" placeholder="请输入...">
                </div>
                <div class="form-actions">
                    <button class="btn-secondary" onclick="organizationApp.hideModal()">取消</button>
                    <button class="btn-primary" onclick="organizationApp.handleInputModalSubmit()">确认</button>
                </div>
            </div>
        `;
        
        // 保存回调函数
        this.inputModalCallback = callback;
        
        // 显示模态框
        this.showModal(title);
        
        // 聚焦输入框并选中默认文本
        setTimeout(() => {
            const input = document.getElementById('modalInput');
            if (input) {
                input.focus();
                if (defaultValue) {
                    input.select();
                }
                
                // 绑定回车键提交
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.handleInputModalSubmit();
                    }
                });
            }
        }, 100);
    }

    handleInputModalSubmit() {
        const input = document.getElementById('modalInput');
        if (input && this.inputModalCallback) {
            const value = input.value.trim();
            this.hideModal();
            
            // 执行回调
            if (this.inputModalCallback) {
                this.inputModalCallback(value);
                this.inputModalCallback = null;
            }
        }
    }

    showLoading(text = '加载中...') {
        document.getElementById('loadingText').textContent = text;
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    showElementLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = '<div class="loading">加载中...</div>';
        }
    }

    hideElementLoading(elementId) {
        // 加载完成后，内容会被渲染方法替换，所以这里不需要特别处理
    }

    // 模态框方法
    showAddMaterialModal() {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="add-material-form">
                <div class="form-group">
                    <label for="newMaterialContent">内容</label>
                    <textarea id="newMaterialContent" rows="6" placeholder="输入素材内容..."></textarea>
                </div>
                <div class="form-group">
                    <label for="newMaterialType">类型</label>
                    <select id="newMaterialType">
                        <option value="text">文本</option>
                        <option value="image">图片</option>
                        <option value="link">链接</option>
                        <option value="code">代码</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="newMaterialCategory">分类</label>
                    <input type="text" id="newMaterialCategory" placeholder="输入分类...">
                </div>
                <div class="form-group">
                    <label for="newMaterialKeywords">关键词</label>
                    <input type="text" id="newMaterialKeywords" placeholder="用逗号分隔">
                </div>
                <div class="form-actions">
                    <button class="btn-secondary" onclick="organizationApp.hideModal()">取消</button>
                    <button class="btn-primary" onclick="organizationApp.saveNewMaterial()">保存</button>
                </div>
            </div>
        `;
        
        document.getElementById('modalTitle').textContent = '添加素材';
        this.showModal();
    }

    showEditMaterialModal(material) {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="edit-material-form">
                <div class="form-group">
                    <label for="editMaterialContent">内容</label>
                    <textarea id="editMaterialContent" rows="6">${this.escapeHtml(material.content)}</textarea>
                </div>
                <div class="form-group">
                    <label for="editMaterialType">类型</label>
                    <select id="editMaterialType">
                        <option value="text" ${material.type === 'text' ? 'selected' : ''}>文本</option>
                        <option value="image" ${material.type === 'image' ? 'selected' : ''}>图片</option>
                        <option value="link" ${material.type === 'link' ? 'selected' : ''}>链接</option>
                        <option value="code" ${material.type === 'code' ? 'selected' : ''}>代码</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editMaterialCategory">分类</label>
                    <input type="text" id="editMaterialCategory" value="${material.category || ''}">
                </div>
                <div class="form-group">
                    <label for="editMaterialKeywords">关键词</label>
                    <input type="text" id="editMaterialKeywords" value="${material.keywords || ''}" placeholder="用逗号分隔">
                </div>
                <div class="form-actions">
                    <button class="btn-secondary" onclick="organizationApp.hideModal()">取消</button>
                    <button class="btn-danger" onclick="organizationApp.deleteMaterial('${material.id}')">删除</button>
                    <button class="btn-primary" onclick="organizationApp.saveEditedMaterial('${material.id}')">保存</button>
                </div>
            </div>
        `;
        
        document.getElementById('modalTitle').textContent = '编辑素材';
        this.showModal();
    }

    async saveNewMaterial() {
        try {
            const content = document.getElementById('newMaterialContent').value.trim();
            const type = document.getElementById('newMaterialType').value;
            const category = document.getElementById('newMaterialCategory').value.trim();
            const keywords = document.getElementById('newMaterialKeywords').value.trim();
            
            if (!content) {
                this.showNotification('请输入素材内容', 'warning');
                return;
            }
            
            const newMaterial = {
                content,
                type,
                category: category || null,
                keywords: keywords || null,
                timestamp: new Date().toISOString()
            };
            
            await ipcRenderer.invoke('quick-capture', newMaterial);
            
            this.hideModal();
            this.loadMaterials();
            this.showNotification('素材添加成功', 'success');
            
        } catch (error) {
            console.error('添加素材失败:', error);
            this.showNotification('添加素材失败: ' + error.message, 'error');
        }
    }

    async saveEditedMaterial(id) {
        try {
            const data = {
                content: document.getElementById('editMaterialContent').value,
                type: document.getElementById('editMaterialType').value,
                category: document.getElementById('editMaterialCategory').value || null,
                keywords: document.getElementById('editMaterialKeywords').value || null
            };
            
            await ipcRenderer.invoke('update-collection', id, data);
            this.hideModal();
            this.loadMaterials();
            this.showNotification('素材已更新', 'success');
            
        } catch (error) {
            console.error('更新素材失败:', error);
            this.showNotification('更新素材失败: ' + error.message, 'error');
        }
    }

    showNotification(message, type = 'info', duration = 3000) {
        const container = document.getElementById('notifications');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        // 创建通知内容结构
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${this.escapeHtml(message)}</span>
                <button class="notification-close" title="关闭">×</button>
            </div>
        `;
        
        // 绑定关闭按钮事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.hideNotification(notification);
        });
        
        container.appendChild(notification);
        
        // 添加进入动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // 自动隐藏（缩短时间到3秒）
        const autoHideTimer = setTimeout(() => {
            this.hideNotification(notification);
        }, duration);
        
        // 保存定时器引用，以便手动关闭时清除
        notification.dataset.timerId = autoHideTimer;
        
        return notification;
    }

    hideNotification(notification) {
        if (!notification || !notification.parentElement) return;
        
        // 清除自动隐藏定时器
        if (notification.dataset.timerId) {
            clearTimeout(notification.dataset.timerId);
        }
        
        // 添加退出动画
        notification.classList.add('hide');
        
        // 动画完成后移除元素
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }

    updateStatusBar() {
        const statusText = document.getElementById('statusText');
        const materialCount = this.materials ? this.materials.length : 0;
        statusText.textContent = `共 ${materialCount} 个素材`;
    }

    async addNewCollection() {
        // 切换到收集模式
        await ipcRenderer.invoke('switch-mode', 'collection');
    }

    async openSettings() {
        try {
            // 这里可以打开设置窗口或面板
            this.showNotification('设置功能开发中...', 'info');
        } catch (error) {
            console.error('打开设置失败:', error);
        }
    }

    showExportDialog() {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="export-options">
                <h4>选择导出格式</h4>
                <div class="export-buttons">
                    <button class="btn-primary" onclick="app.exportAs('markdown')">Markdown</button>
                    <button class="btn-primary" onclick="app.exportAs('html')">HTML</button>
                    <button class="btn-primary" onclick="app.exportAs('pdf')">PDF</button>
                    <button class="btn-primary" onclick="app.exportAs('json')">JSON</button>
                </div>
            </div>
        `;
        this.showModal('导出数据');
    }

    showExportDialog() {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="export-dialog">
                <h3>导出选项</h3>
                <div class="export-options">
                    <div class="export-option">
                        <button class="btn-primary" onclick="organizationApp.exportAs('json')">
                            导出为 JSON
                        </button>
                        <p>导出所有收藏数据为 JSON 格式</p>
                    </div>
                    <div class="export-option">
                        <button class="btn-primary" onclick="organizationApp.exportAs('csv')">
                            导出为 CSV
                        </button>
                        <p>导出收藏数据为 CSV 表格格式</p>
                    </div>
                    <div class="export-option">
                        <button class="btn-primary" onclick="organizationApp.exportCurrentDocument()">
                            导出当前文档
                        </button>
                        <p>导出当前编辑的 Markdown 文档</p>
                    </div>
                    <div class="export-option">
                        <button class="btn-primary" onclick="organizationApp.exportAs('pdf')">
                            导出为 PDF
                        </button>
                        <p>导出当前文档为 PDF 格式</p>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-secondary" onclick="organizationApp.hideModal()">取消</button>
                </div>
            </div>
        `;
        
        document.getElementById('modalTitle').textContent = '导出数据';
        this.showModal();
    }

    async exportCurrentDocument() {
        try {
            const title = document.getElementById('documentTitle').value || '未命名文档';
            const content = this.editorContent || '';
            
            if (!content.trim()) {
                this.showNotification('文档内容为空', 'warning');
                return;
            }
            
            // 创建下载
            const blob = new Blob([content], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${title}.md`;
            a.click();
            URL.revokeObjectURL(url);
            
            this.hideModal();
            this.showNotification('文档导出成功', 'success');
            
        } catch (error) {
            console.error('导出文档失败:', error);
            this.showNotification('导出文档失败: ' + error.message, 'error');
        }
    }

    async exportAs(format) {
        try {
            const data = await ipcRenderer.invoke('export-data', format);
            
            // 触发下载
            const blob = new Blob([data], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `qnotes-export.${format}`;
            a.click();
            URL.revokeObjectURL(url);
            
            this.hideModal();
            this.showNotification(`导出${format.toUpperCase()}成功`, 'success');
            
        } catch (error) {
            console.error('导出失败:', error);
            this.showNotification('导出失败: ' + error.message, 'error');
        }
    }

    showCollectionContextMenu(e, collectionId) {
        // 简单的右键菜单实现
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.position = 'fixed';
        menu.style.left = e.clientX + 'px';
        menu.style.top = e.clientY + 'px';
        menu.innerHTML = `
            <div class="menu-item" onclick="app.editCollection('${collectionId}')">编辑</div>
            <div class="menu-item" onclick="app.deleteCollection('${collectionId}')">删除</div>
            <div class="menu-item" onclick="app.copyCollectionContent('${collectionId}')">复制内容</div>
        `;
        
        document.body.appendChild(menu);
        
        const removeMenu = () => {
            menu.remove();
            document.removeEventListener('click', removeMenu);
        };
        
        setTimeout(() => {
            document.addEventListener('click', removeMenu);
        }, 100);
    }

    async copyCollectionContent(id) {
        try {
            const collection = this.materials.find(c => c.id === id);
            if (collection) {
                await navigator.clipboard.writeText(collection.content);
                this.showNotification('内容已复制到剪贴板', 'success');
            }
        } catch (error) {
            console.error('复制失败:', error);
            this.showNotification('复制失败: ' + error.message, 'error');
        }
    }

    showStatisticsModal(stats) {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="statistics-content">
                <div class="stat-item">
                    <span class="stat-label">总收藏数</span>
                    <span class="stat-value">${stats.totalCollections}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">AI已处理</span>
                    <span class="stat-value">${stats.aiProcessed}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">待处理</span>
                    <span class="stat-value">${stats.aiUnprocessed}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">生成文档</span>
                    <span class="stat-value">${stats.totalDocuments}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最近30天</span>
                    <span class="stat-value">${stats.recentCollections}</span>
                </div>
            </div>
        `;
        this.showModal('统计信息');
    }

    async openSettings() {
        // 获取当前AI配置
        let aiConfig = { baseURL: '', apiKey: '', model: '', enabled: false };
        try {
            aiConfig = await ipcRenderer.invoke('ai-get-config');
        } catch (error) {
            console.error('获取AI配置失败:', error);
        }

        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="settings-content">
                <h3>应用设置</h3>
                
                <div class="setting-group">
                    <h4>AI服务配置</h4>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="aiEnabled" ${aiConfig.enabled ? 'checked' : ''}>
                            启用AI功能
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            Base URL:
                            <input type="text" id="aiBaseURL" value="${aiConfig.baseURL || 'https://api.tu-zi.com/v1'}" placeholder="输入AI服务的Base URL">
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            API Key:
                            <input type="password" id="aiApiKey" value="${aiConfig.apiKey || ''}" placeholder="输入你的API Key">
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            模型:
                            <select id="aiModel">
                                <option value="claude-sonnet-4-20250514" ${aiConfig.model === 'claude-sonnet-4-20250514' ? 'selected' : ''}>Claude Sonnet 4</option>
                                <option value="claude-3-sonnet-20240229" ${aiConfig.model === 'claude-3-sonnet-20240229' ? 'selected' : ''}>Claude 3 Sonnet</option>
                                <option value="claude-3-haiku-20240307" ${aiConfig.model === 'claude-3-haiku-20240307' ? 'selected' : ''}>Claude 3 Haiku</option>
                                <option value="gpt-4" ${aiConfig.model === 'gpt-4' ? 'selected' : ''}>GPT-4</option>
                                <option value="gpt-3.5-turbo" ${aiConfig.model === 'gpt-3.5-turbo' ? 'selected' : ''}>GPT-3.5 Turbo</option>
                            </select>
                        </label>
                    </div>
                    <div class="setting-item">
                        <button class="btn-secondary" onclick="organizationApp.testAIConnection()">测试连接</button>
                        <span id="aiTestResult"></span>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>编辑器设置</h4>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="enablePreview" checked>
                            启用实时预览
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="enableWordWrap" checked>
                            启用自动换行
                        </label>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h4>导出设置</h4>
                    <div class="setting-item">
                        <label>
                            默认导出格式:
                            <select id="defaultExportFormat">
                                <option value="markdown">Markdown</option>
                                <option value="json">JSON</option>
                                <option value="csv">CSV</option>
                            </select>
                        </label>
                    </div>
                </div>
                
                <div class="modal-actions">
                    <button class="btn-secondary" onclick="organizationApp.hideModal()">取消</button>
                    <button class="btn-primary" onclick="organizationApp.saveSettings()">保存设置</button>
                </div>
            </div>
        `;
        
        document.getElementById('modalTitle').textContent = '应用设置';
        this.showModal();
    }

    async saveSettings() {
        try {
            // 保存AI配置
            const aiConfig = {
                enabled: document.getElementById('aiEnabled').checked,
                baseURL: document.getElementById('aiBaseURL').value,
                apiKey: document.getElementById('aiApiKey').value,
                model: document.getElementById('aiModel').value
            };
            
            await ipcRenderer.invoke('ai-update-config', aiConfig);
            
            // 保存其他设置
            const settings = {
                enablePreview: document.getElementById('enablePreview').checked,
                enableWordWrap: document.getElementById('enableWordWrap').checked,
                defaultExportFormat: document.getElementById('defaultExportFormat').value
            };
            
            await ipcRenderer.invoke('update-settings', settings);
            this.hideModal();
            this.showNotification('设置已保存', 'success');
            
        } catch (error) {
            console.error('保存设置失败:', error);
            this.showNotification('保存设置失败: ' + error.message, 'error');
        }
    }

    // 测试AI连接
    async testAIConnection() {
        const resultSpan = document.getElementById('aiTestResult');
        resultSpan.textContent = '测试中...';
        resultSpan.className = 'test-result testing';
        
        try {
            // 先保存当前配置
            const aiConfig = {
                enabled: document.getElementById('aiEnabled').checked,
                baseURL: document.getElementById('aiBaseURL').value,
                apiKey: document.getElementById('aiApiKey').value,
                model: document.getElementById('aiModel').value
            };
            
            await ipcRenderer.invoke('ai-update-config', aiConfig);
            
            // 测试连接
            const result = await ipcRenderer.invoke('ai-test-connection');
            
            if (result.success) {
                resultSpan.textContent = '✓ 连接成功';
                resultSpan.className = 'test-result success';
            } else {
                resultSpan.textContent = `✗ ${result.message}`;
                resultSpan.className = 'test-result error';
            }
            
        } catch (error) {
            console.error('测试连接失败:', error);
            resultSpan.textContent = `✗ 测试失败: ${error.message}`;
            resultSpan.className = 'test-result error';
        }
    }

    // 辅助工具方法
    escapeHtml(text) {
        if (!text) return '';
        return String(text)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    showElementLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = '<div class="loading">加载中...</div>';
        }
    }

    hideElementLoading(elementId) {
        // This is handled by the specific render methods
    }

    formatTime(timestamp) {
        if (!timestamp) return '';
        try {
            const date = new Date(timestamp);
            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays === 1) {
                return '昨天';
            } else if (diffDays < 7) {
                return `${diffDays}天前`;
            } else {
                return date.toLocaleDateString('zh-CN');
            }
        } catch (error) {
            return '';
        }
    }

    getTypeIcon(type) {
        const icons = {
            'text': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h10v2H7V7zm0 4h10v2H7v-2zm0 4h7v2H7v-2z"/></svg>',
            'link': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M10.59 13.41c.41.39.41 1.03 0 1.42-.39.39-1.03.39-1.42 0a5.003 5.003 0 0 1 0-7.07l3.54-3.54a5.003 5.003 0 0 1 7.07 0 5.003 5.003 0 0 1 0 7.07l-1.49 1.49c.01-.82-.12-1.64-.4-2.42l.47-.48a2.982 2.982 0 0 0 0-4.24 2.982 2.982 0 0 0-4.24 0l-3.53 3.53a2.982 2.982 0 0 0 0-4.24zm2.82-4.24c.39-.39 1.03-.39 1.42 0a5.003 5.003 0 0 1 0 7.07l-3.54 3.54a5.003 5.003 0 0 1-7.07 0 5.003 5.003 0 0 1 0-7.07l1.49-1.49c-.01.82.12 1.64.4 2.42l-.47.48a2.982 2.982 0 0 0 0 4.24 2.982 2.982 0 0 0 4.24 0l3.53-3.53a2.982 2.982 0 0 0 0-4.24.973.973 0 0 1 0-1.42z"/></svg>',
            'image': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M5 3h14c1.1 0 2 .9 2 2v14c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V5c0-1.1.9-2 2-2zm0 2v10.5l3.5-3.5 2.5 2.5L16.5 9 19 11.5V5H5zm0 14h14v-1.5L16.5 13 11 18.5 8.5 16 5 19.5V19z"/></svg>',
            'code': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M8.7 15.9L4.8 12l3.9-3.9c.39-.39.39-1.01 0-1.4s-1.01-.39-1.4 0l-4.59 4.59c-.39.39-.39 1.02 0 1.41l4.59 4.6c.39.39 1.01.39 1.4 0 .39-.39.39-1.01 0-1.4zm6.6 0l3.9-3.9-3.9-3.9c-.39-.39-.39-1.01 0-1.4s1.01-.39 1.4 0l4.59 4.59c.39.39.39 1.02 0 1.41L16.7 16.3c-.39.39-1.01.39-1.4 0-.39-.39-.39-1.01 0-1.4z"/></svg>',
            'video': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"/></svg>',
            'audio': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/></svg>'
        };
        return this.getIcon(type);
    }

    // 素材管理相关方法占位
    showAddMaterialModal() {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="add-material-form">
                <h3>添加新素材</h3>
                <div class="form-group">
                    <label for="materialType">类型:</label>
                    <select id="materialType">
                        <option value="text">文本</option>
                        <option value="link">链接</option>
                        <option value="image">图片</option>
                        <option value="code">代码</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="materialContent">内容:</label>
                    <textarea id="materialContent" rows="6" placeholder="输入素材内容..."></textarea>
                </div>
                <div class="form-group">
                    <label for="materialCategory">分类:</label>
                    <select id="materialCategory">
                        <option value="">无分类</option>
                        <option value="技术文档">技术文档</option>
                        <option value="学习笔记">学习笔记</option>
                        <option value="工作相关">工作相关</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="modal-actions">
                    <button class="btn-secondary" onclick="organizationApp.hideModal()">取消</button>
                    <button class="btn-primary" onclick="organizationApp.saveNewMaterial()">保存</button>
                </div>
            </div>
        `;
        document.getElementById('modalTitle').textContent = '添加素材';
        this.showModal();
    }

    showEditMaterialModal(material) {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="edit-material-form">
                <h3>编辑素材</h3>
                <div class="form-group">
                    <label for="editMaterialType">类型:</label>
                    <select id="editMaterialType">
                        <option value="text" ${material.type === 'text' ? 'selected' : ''}>文本</option>
                        <option value="link" ${material.type === 'link' ? 'selected' : ''}>链接</option>
                        <option value="image" ${material.type === 'image' ? 'selected' : ''}>图片</option>
                        <option value="code" ${material.type === 'code' ? 'selected' : ''}>代码</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editMaterialContent">内容:</label>
                    <textarea id="editMaterialContent" rows="6">${this.escapeHtml(material.content || '')}</textarea>
                </div>
                <div class="form-group">
                    <label for="editMaterialCategory">分类:</label>
                    <select id="editMaterialCategory">
                        <option value="" ${!material.category ? 'selected' : ''}>无分类</option>
                        <option value="技术文档" ${material.category === '技术文档' ? 'selected' : ''}>技术文档</option>
                        <option value="学习笔记" ${material.category === '学习笔记' ? 'selected' : ''}>学习笔记</option>
                        <option value="工作相关" ${material.category === '工作相关' ? 'selected' : ''}>工作相关</option>
                        <option value="其他" ${material.category === '其他' ? 'selected' : ''}>其他</option>
                    </select>
                </div>
                <div class="modal-actions">
                    <button class="btn-secondary" onclick="organizationApp.hideModal()">取消</button>
                    <button class="btn-primary" onclick="organizationApp.saveEditedMaterial('${material.id}')">保存</button>
                </div>
            </div>
        `;
        document.getElementById('modalTitle').textContent = '编辑素材';
        this.showModal();
    }

    async saveNewMaterial() {
        try {
            const type = document.getElementById('materialType').value;
            const content = document.getElementById('materialContent').value;
            const category = document.getElementById('materialCategory').value;
            
            if (!content.trim()) {
                this.showNotification('请输入素材内容', 'warning');
                return;
            }
            
            const materialData = {
                type,
                content: content.trim(),
                category: category || null,
                timestamp: new Date().toISOString(),
                source: 'manual'
            };
            
            await ipcRenderer.invoke('quick-capture', materialData);
            this.hideModal();
            this.loadMaterials();
            this.showNotification('素材已保存', 'success');
            
        } catch (error) {
            console.error('保存素材失败:', error);
            this.showNotification('保存素材失败: ' + error.message, 'error');
        }
    }

    async saveEditedMaterial(id) {
        try {
            const type = document.getElementById('editMaterialType').value;
            const content = document.getElementById('editMaterialContent').value;
            const category = document.getElementById('editMaterialCategory').value;
            
            if (!content.trim()) {
                this.showNotification('请输入素材内容', 'warning');
                return;
            }
            
            const updateData = {
                type,
                content: content.trim(),
                category: category || null
            };
            
            await ipcRenderer.invoke('update-collection', id, updateData);
            this.hideModal();
            this.loadMaterials();
            this.showNotification('素材已更新', 'success');
            
        } catch (error) {
            console.error('更新素材失败:', error);
            this.showNotification('更新素材失败: ' + error.message, 'error');
        }
    }

    // 文件管理相关方法
    async loadFiles() {
        this.showElementLoading('filesTree');
        try {
            // 获取文件列表
            const files = await ipcRenderer.invoke('get-documents');
            this.files = Array.isArray(files) ? files : [];
            this.renderFiles();
        } catch (error) {
            console.error('加载文件失败:', error);
            this.files = [];
            this.renderFiles();
            this.showNotification('加载文件失败: ' + error.message, 'error');
        } finally {
            this.hideElementLoading('filesTree');
        }
    }
    
    renderFiles() {
        const container = document.getElementById('filesTree');
        
        if (!this.files || this.files.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <h3>暂无文件</h3>
                    <p>点击"新建文件"按钮创建第一个文档</p>
                </div>
            `;
            return;
        }
        
        // 按文件夹分组
        const filesByFolder = this.groupFilesByFolder(this.files);
        
        const html = this.renderFileTree(filesByFolder);
        container.innerHTML = html;
        
        // 绑定文件操作事件
        container.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectFile(item.dataset.id);
            });
            item.addEventListener('dblclick', (e) => {
                e.stopPropagation();
                this.openFile(item.dataset.id);
            });
            item.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showFileContextMenu(e, item.dataset.id);
            });
        });
        
        // 绑定文件夹操作事件
        container.querySelectorAll('.folder-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleFolder(item.dataset.folder);
            });
            item.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showFolderContextMenu(e, item.dataset.folder);
            });
        });
    }
    
    groupFilesByFolder(files) {
        const grouped = {};
        
        files.forEach(file => {
            const folder = file.folder || 'root';
            if (!grouped[folder]) {
                grouped[folder] = [];
            }
            grouped[folder].push(file);
        });
        
        return grouped;
    }
    
    renderFileTree(filesByFolder) {
        let html = '';
        
        // 渲染根文件夹
        if (filesByFolder.root) {
            html += filesByFolder.root.map(file => this.renderFileItem(file)).join('');
        }
        
        // 渲染其他文件夹
        Object.keys(filesByFolder).forEach(folder => {
            if (folder !== 'root') {
                html += this.renderFolderItem(folder, filesByFolder[folder]);
            }
        });
        
        return html;
    }
    
    renderFolderItem(folderName, files) {
        const isOpen = this.openFolders && this.openFolders.has(folderName);
        
        return `
            <div class="folder-item ${isOpen ? 'open' : ''}" data-folder="${folderName}">
                <div class="folder-header">
                    <span class="folder-icon">${this.getIcon('folder')}</span>
                    <span class="folder-name">${folderName}</span>
                    <span class="folder-count">(${files.length})</span>
                </div>
                ${isOpen ? `
                    <div class="folder-content">
                        ${files.map(file => this.renderFileItem(file, true)).join('')}
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    renderFileItem(file, isInFolder = false) {
        const selected = this.selectedFile === file.id;
        const indent = isInFolder ? 'style="margin-left: 20px;"' : '';
        
        return `
            <div class="file-item ${selected ? 'selected' : ''}" data-id="${file.id}" ${indent}>
                <div class="file-header">
                    <span class="file-icon">${this.getIcon('file')}</span>
                    <span class="file-name">${file.title || '未命名文档'}</span>
                    <span class="file-size">${this.formatFileSize(file.size || 0)}</span>
                </div>
                <div class="file-meta">
                    <span class="file-date">${this.formatTime(file.created_at)}</span>
                    <div class="file-actions">
                        <button class="btn-icon" onclick="organizationApp.openFile('${file.id}')" title="打开">${this.getIcon('book')}</button>
                        <button class="btn-icon" onclick="organizationApp.renameFile('${file.id}')" title="重命名">${this.getIcon('edit')}</button>
                        <button class="btn-icon" onclick="organizationApp.deleteFile('${file.id}')" title="删除">${this.getIcon('delete')}</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    selectFile(id) {
        this.selectedFile = id;
        this.renderFiles();
    }
    
    async openFile(id) {
        try {
            const file = this.files.find(f => f.id === id);
            if (!file) {
                this.showNotification('文件不存在', 'error');
                return;
            }
            
            // 加载文件内容到编辑器
            const textarea = document.getElementById('markdownTextarea');
            const titleInput = document.getElementById('documentTitle');
            
            if (textarea && titleInput) {
                textarea.value = file.content || '';
                titleInput.value = file.title || '';
                this.editorContent = file.content || '';
                this.currentDocument = file;
                this.updateWordCount();
                this.updatePreview();
                
                // 切换到编辑器
                textarea.focus();
                
                this.showNotification('文件已加载', 'success');
            }
        } catch (error) {
            console.error('打开文件失败:', error);
            this.showNotification('打开文件失败: ' + error.message, 'error');
        }
    }
    
    async renameFile(id) {
        try {
            const file = this.files.find(f => f.id === id);
            if (!file) {
                this.showNotification('文件不存在', 'error');
                return;
            }
            
            const newName = prompt('请输入新的文件名:', file.title || '未命名文档');
            if (!newName || newName.trim() === '') return;
            
            // 更新文件名
            await ipcRenderer.invoke('update-document', id, {
                title: newName.trim()
            });
            
            // 重新加载文件列表
            this.loadFiles();
            this.showNotification('文件重命名成功', 'success');
            
        } catch (error) {
            console.error('重命名文件失败:', error);
            this.showNotification('重命名文件失败: ' + error.message, 'error');
        }
    }
    
    async deleteFile(id) {
        try {
            const file = this.files.find(f => f.id === id);
            if (!file) {
                this.showNotification('文件不存在', 'error');
                return;
            }
            
            const confirmed = confirm(`确定要删除文件"${file.title || '未命名文档'}"吗？此操作无法撤销。`);
            if (!confirmed) return;
            
            // 删除文件
            await ipcRenderer.invoke('delete-document', id);
            
            // 重新加载文件列表
            this.loadFiles();
            this.showNotification('文件删除成功', 'success');
            
        } catch (error) {
            console.error('删除文件失败:', error);
            this.showNotification('删除文件失败: ' + error.message, 'error');
        }
    }
    
    toggleFolder(folderName) {
        if (!this.openFolders) {
            this.openFolders = new Set();
        }
        
        if (this.openFolders.has(folderName)) {
            this.openFolders.delete(folderName);
        } else {
            this.openFolders.add(folderName);
        }
        
        this.renderFiles();
    }
    
    showFileContextMenu(e, fileId) {
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.position = 'fixed';
        menu.style.left = e.clientX + 'px';
        menu.style.top = e.clientY + 'px';
        menu.innerHTML = `
            <div class="menu-item" onclick="organizationApp.openFile('${fileId}')">${this.getIcon('book')} 打开</div>
            <div class="menu-item" onclick="organizationApp.renameFile('${fileId}')">${this.getIcon('edit')} 重命名</div>
            <div class="menu-item" onclick="organizationApp.duplicateFile('${fileId}')">${this.getIcon('copy')} 复制</div>
            <div class="menu-item" onclick="organizationApp.moveFile('${fileId}')">${this.getIcon('move')} 移动</div>
            <div class="menu-separator"></div>
            <div class="menu-item danger" onclick="organizationApp.deleteFile('${fileId}')">${this.getIcon('delete')} 删除</div>
        `;
        
        document.body.appendChild(menu);
        
        const removeMenu = () => {
            menu.remove();
            document.removeEventListener('click', removeMenu);
        };
        
        setTimeout(() => {
            document.addEventListener('click', removeMenu);
        }, 100);
    }
    
    showFolderContextMenu(e, folderName) {
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.position = 'fixed';
        menu.style.left = e.clientX + 'px';
        menu.style.top = e.clientY + 'px';
        menu.innerHTML = `
            <div class="menu-item" onclick="organizationApp.createNewFileInFolder('${folderName}')">${this.getIcon('file')} 新建文件</div>
            <div class="menu-item" onclick="organizationApp.renameFolder('${folderName}')">${this.getIcon('edit')} 重命名</div>
            <div class="menu-separator"></div>
            <div class="menu-item danger" onclick="organizationApp.deleteFolder('${folderName}')">${this.getIcon('delete')} 删除</div>
        `;
        
        document.body.appendChild(menu);
        
        const removeMenu = () => {
            menu.remove();
            document.removeEventListener('click', removeMenu);
        };
        
        setTimeout(() => {
            document.addEventListener('click', removeMenu);
        }, 100);
    }
    
    async duplicateFile(id) {
        try {
            const file = this.files.find(f => f.id === id);
            if (!file) {
                this.showNotification('文件不存在', 'error');
                return;
            }
            
            const newTitle = `${file.title || '未命名文档'} - 副本`;
            
            // 创建文件副本
            await ipcRenderer.invoke('save-document', {
                title: newTitle,
                content: file.content || '',
                folder: file.folder || null
            });
            
            // 重新加载文件列表
            this.loadFiles();
            this.showNotification('文件复制成功', 'success');
            
        } catch (error) {
            console.error('复制文件失败:', error);
            this.showNotification('复制文件失败: ' + error.message, 'error');
        }
    }
    
    async moveFile(id) {
        try {
            const file = this.files.find(f => f.id === id);
            if (!file) {
                this.showNotification('文件不存在', 'error');
                return;
            }
            
            // 获取所有文件夹
            const folders = [...new Set(this.files.map(f => f.folder).filter(Boolean))];
            
            let folderOptions = '<option value="">根目录</option>';
            folders.forEach(folder => {
                folderOptions += `<option value="${folder}">${folder}</option>`;
            });
            
            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <div class="move-file-form">
                    <h4>移动文件: ${file.title || '未命名文档'}</h4>
                    <div class="form-group">
                        <label>目标文件夹:</label>
                        <select id="targetFolder">
                            ${folderOptions}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>或创建新文件夹:</label>
                        <input type="text" id="newFolderName" placeholder="输入新文件夹名称">
                    </div>
                    <div class="form-actions">
                        <button class="btn-secondary" onclick="organizationApp.hideModal()">取消</button>
                        <button class="btn-primary" onclick="organizationApp.confirmMoveFile('${id}')">移动</button>
                    </div>
                </div>
            `;
            
            document.getElementById('modalTitle').textContent = '移动文件';
            this.showModal();
            
        } catch (error) {
            console.error('移动文件失败:', error);
            this.showNotification('移动文件失败: ' + error.message, 'error');
        }
    }
    
    async confirmMoveFile(id) {
        try {
            const targetFolder = document.getElementById('targetFolder').value;
            const newFolderName = document.getElementById('newFolderName').value.trim();
            
            const folder = newFolderName || targetFolder || null;
            
            // 更新文件文件夹
            await ipcRenderer.invoke('update-document', id, { folder });
            
            this.hideModal();
            this.loadFiles();
            this.showNotification('文件移动成功', 'success');
            
        } catch (error) {
            console.error('移动文件失败:', error);
            this.showNotification('移动文件失败: ' + error.message, 'error');
        }
    }

    toggleFilesSearch() {
        const container = document.getElementById('filesSearchContainer');
        const input = document.getElementById('filesSearchInput');
        
        if (container.style.display === 'none') {
            container.style.display = 'flex';
            input.focus();
        } else {
            this.closeFilesSearch();
        }
    }

    closeFilesSearch() {
        const container = document.getElementById('filesSearchContainer');
        const input = document.getElementById('filesSearchInput');
        container.style.display = 'none';
        input.value = '';
    }

    handleFilesSearch(query) {
        // 文件搜索占位实现
        console.log('搜索文件:', query);
    }

    createNewFolder() {
        this.showInputModal('新建文件夹', '请输入文件夹名称:', '', async (folderName) => {
            if (!folderName || folderName.trim() === '') {
                this.showNotification('文件夹名称不能为空', 'warning');
                return;
            }
            
            const trimmedName = folderName.trim();
            
            // 检查文件夹是否已存在
            const existingFolders = [...new Set(this.files.map(f => f.folder).filter(Boolean))];
            if (existingFolders.includes(trimmedName)) {
                this.showNotification('文件夹已存在', 'warning');
                return;
            }
            
            try {
                // 创建文件夹（通过创建一个隐藏的配置文件）
                await ipcRenderer.invoke('save-document', {
                    title: '.folder_config',
                    content: JSON.stringify({
                        folderName: trimmedName,
                        created: new Date().toISOString(),
                        type: 'folder_config'
                    }),
                    folder: trimmedName
                });
                
                // 重新加载文件列表
                this.loadFiles();
                this.showNotification(`已创建文件夹: ${trimmedName}`, 'success');
                
                // 展开新创建的文件夹
                this.openFolders.add(trimmedName);
                
            } catch (error) {
                console.error('创建文件夹失败:', error);
                this.showNotification('创建文件夹失败: ' + error.message, 'error');
            }
        });
    }

    createNewFile() {
        this.showInputModal('新建文件', '请输入文件名称:', '', async (fileName) => {
            if (!fileName || fileName.trim() === '') {
                this.showNotification('文件名称不能为空', 'warning');
                return;
            }
            
            const title = fileName.trim();
            
            // 检查文件名是否已存在（在根目录）
            const existingFiles = this.files.filter(f => !f.folder).map(f => f.title);
            if (existingFiles.includes(title)) {
                this.showNotification('文件名已存在', 'warning');
                return;
            }
            
            try {
                // 保存新文件
                const result = await ipcRenderer.invoke('save-document', {
                    title: title,
                    content: `# ${title}\n\n开始编写内容...`,
                    folder: null
                });
                
                // 重新加载文件列表
                await this.loadFiles();
                this.showNotification(`已创建新文件: ${title}`, 'success');
                
                // 自动打开新创建的文件
                if (result && result.id) {
                    setTimeout(() => {
                        this.openFile(result.id);
                    }, 100);
                }
                
            } catch (error) {
                console.error('创建文件失败:', error);
                this.showNotification('创建文件失败: ' + error.message, 'error');
            }
        });
    }
    
    createNewFileInFolder(folderName) {
        this.showInputModal('新建文件', `请输入文件名称 (将创建在"${folderName}"文件夹中):`, '', async (fileName) => {
            if (!fileName || fileName.trim() === '') {
                this.showNotification('文件名称不能为空', 'warning');
                return;
            }
            
            const title = fileName.trim();
            
            // 检查文件名是否已存在（在指定文件夹中）
            const existingFiles = this.files.filter(f => f.folder === folderName).map(f => f.title);
            if (existingFiles.includes(title)) {
                this.showNotification(`文件夹"${folderName}"中已存在同名文件`, 'warning');
                return;
            }
            
            try {
                // 保存新文件到指定文件夹
                const result = await ipcRenderer.invoke('save-document', {
                    title: title,
                    content: `# ${title}\n\n开始编写内容...`,
                    folder: folderName
                });
                
                // 重新加载文件列表
                await this.loadFiles();
                this.showNotification(`已在文件夹"${folderName}"中创建新文件: ${title}`, 'success');
                
                // 确保文件夹展开并自动打开新文件
                this.openFolders.add(folderName);
                if (result && result.id) {
                    setTimeout(() => {
                        this.openFile(result.id);
                    }, 100);
                }
                
            } catch (error) {
                console.error('创建文件失败:', error);
                this.showNotification('创建文件失败: ' + error.message, 'error');
            }
        });
    }
    
    async renameFolder(folderName) {
        const newName = prompt('请输入新的文件夹名称:', folderName);
        if (!newName || newName.trim() === '' || newName.trim() === folderName) return;
        
        try {
            // 获取该文件夹下的所有文件
            const filesInFolder = this.files.filter(f => f.folder === folderName);
            
            if (filesInFolder.length === 0) {
                this.showNotification('文件夹为空', 'warning');
                return;
            }
            
            // 更新所有文件的文件夹
            const updatePromises = filesInFolder.map(file => 
                ipcRenderer.invoke('update-document', file.id, { folder: newName.trim() })
            );
            
            await Promise.all(updatePromises);
            
            // 重新加载文件列表
            this.loadFiles();
            this.showNotification('文件夹重命名成功', 'success');
            
        } catch (error) {
            console.error('重命名文件夹失败:', error);
            this.showNotification('重命名文件夹失败: ' + error.message, 'error');
        }
    }
    
    async deleteFolder(folderName) {
        const filesInFolder = this.files.filter(f => f.folder === folderName);
        
        if (filesInFolder.length === 0) {
            this.showNotification('文件夹为空', 'warning');
            return;
        }
        
        const confirmed = confirm(`确定要删除文件夹"${folderName}"及其中的 ${filesInFolder.length} 个文件吗？此操作无法撤销。`);
        if (!confirmed) return;
        
        try {
            // 删除文件夹下的所有文件
            const deletePromises = filesInFolder.map(file => 
                ipcRenderer.invoke('delete-document', file.id)
            );
            
            await Promise.all(deletePromises);
            
            // 重新加载文件列表
            this.loadFiles();
            this.showNotification('文件夹删除成功', 'success');
            
        } catch (error) {
            console.error('删除文件夹失败:', error);
            this.showNotification('删除文件夹失败: ' + error.message, 'error');
        }
    }

    showMaterialContextMenu(e, materialId) {
        // 右键菜单占位实现
        console.log('显示素材右键菜单:', materialId);
    }

    // AI相关方法
    async sendAIMessage() {
        const aiInput = document.getElementById('aiInput');
        const aiChatHistory = document.getElementById('aiChatHistory');
        
        if (!aiInput || !aiChatHistory) return;
        
        const message = aiInput.value.trim();
        if (!message) {
            this.showNotification('请输入消息内容', 'warning');
            return;
        }
        
        // 清空输入框
        aiInput.value = '';
        
        // 添加用户消息
        const userMessage = document.createElement('div');
        userMessage.className = 'ai-message user';
        userMessage.textContent = message;
        aiChatHistory.appendChild(userMessage);
        
        // 滚动到底部
        aiChatHistory.scrollTop = aiChatHistory.scrollHeight;
        
        // 添加AI回复容器
        const aiReply = document.createElement('div');
        aiReply.className = 'ai-message assistant streaming';
        aiReply.innerHTML = '<div class="typing-indicator">AI正在思考...</div>';
        aiChatHistory.appendChild(aiReply);
        aiChatHistory.scrollTop = aiChatHistory.scrollHeight;
        
        // 保存当前对话状态
        this.currentAIMessage = aiReply;
        this.currentAIContent = '';
        this.isAIStreaming = true;
        
        try {
            // 获取历史对话记录
            const history = this.getAIHistory();
            
            // 设置流式数据监听
            this.setupAIStreamListeners();
            
            // 调用流式AI服务
            const response = await ipcRenderer.invoke('ai-chat-stream', message, history);
            
            // 保存到历史记录
            this.aiHistory.push({ role: 'user', content: message });
            this.aiHistory.push({ role: 'assistant', content: this.currentAIContent });
            
            // 限制历史记录长度
            if (this.aiHistory.length > 20) {
                this.aiHistory = this.aiHistory.slice(-20);
            }
            
            if (!response.success) {
                this.handleAIError(response.error);
            }
            
        } catch (error) {
            console.error('AI对话失败:', error);
            this.handleAIError(error.message);
        }
    }
    
    setupAIStreamListeners() {
        // 移除旧的监听器
        ipcRenderer.removeAllListeners('ai-stream-data');
        ipcRenderer.removeAllListeners('ai-stream-end');
        ipcRenderer.removeAllListeners('ai-stream-error');
        
        // 流式数据监听
        ipcRenderer.on('ai-stream-data', (event, chunk) => {
            if (!this.isAIStreaming || !this.currentAIMessage) return;
            
            this.currentAIContent += chunk;
            
            // 移除打字指示器
            if (this.currentAIMessage.querySelector('.typing-indicator')) {
                this.currentAIMessage.innerHTML = '';
            }
            
            // 更新显示内容
            this.currentAIMessage.innerHTML = this.formatAIContent(this.currentAIContent);
            
            // 滚动到底部
            const aiChatHistory = document.getElementById('aiChatHistory');
            if (aiChatHistory) {
                aiChatHistory.scrollTop = aiChatHistory.scrollHeight;
            }
        });
        
        // 流式结束监听
        ipcRenderer.on('ai-stream-end', () => {
            this.finishAIStream();
        });
        
        // 流式错误监听
        ipcRenderer.on('ai-stream-error', (event, error) => {
            this.handleAIError(error);
        });
    }
    
    formatAIContent(content) {
        // 简单的markdown格式化
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
    
    finishAIStream() {
        if (this.currentAIMessage) {
            this.currentAIMessage.classList.remove('streaming');
            
            // 添加操作按钮
            const actionButtons = document.createElement('div');
            actionButtons.className = 'ai-message-actions';
            actionButtons.innerHTML = `
                <button class="btn-secondary btn-sm" onclick="organizationApp.copyAIResponse('${this.currentAIContent.replace(/'/g, '\\\'')}')" title="复制">${this.getIcon('copy')}</button>
                <button class="btn-secondary btn-sm" onclick="organizationApp.insertAIResponse('${this.currentAIContent.replace(/'/g, '\\\'')}')" title="插入到编辑器">${this.getIcon('edit')}</button>
                <button class="btn-secondary btn-sm" onclick="organizationApp.regenerateAIResponse()" title="重新生成">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                    </svg>
                </button>
            `;
            this.currentAIMessage.appendChild(actionButtons);
        }
        
        this.isAIStreaming = false;
        this.currentAIMessage = null;
    }
    
    // 统一的AI错误处理
    handleAIError(error) {
        // 统一错误处理 - 支持流式和非流式
        if (this.currentAIMessage) {
            this.currentAIMessage.classList.remove('streaming');
            this.currentAIMessage.innerHTML = `
                <div class="ai-message-content">
                    <div class="ai-message-header">
                        <div class="ai-avatar error">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5.5 7.5L15 12l2.5 2.5L16 16l-2.5-2.5L11 16l-1.5-1.5L12 12l-2.5-2.5L11 8l2.5 2.5L16 8l1.5 1.5z"/>
                            </svg>
                        </div>
                        <div class="ai-status">
                            <span class="ai-status-text">处理失败</span>
                            <div class="ai-status-indicator error"></div>
                        </div>
                    </div>
                    <div class="ai-error-message">
                        <p>AI服务暂时不可用，请稍后重试。</p>
                        <details>
                            <summary>错误详情</summary>
                            <p class="error-details">${this.escapeHtml(error)}</p>
                        </details>
                    </div>
                </div>
            `;
        }
        
        // 清理状态
        this.cleanupAIState();
        
        // 显示用户友好的通知
        this.showNotification('AI处理失败，请稍后重试', 'error');
        
        console.error('AI Error:', error);
    }
    
    // 清理AI状态的辅助函数
    cleanupAIState() {
        this.isAIStreaming = false;
        this.currentAIMessage = null;
        this.currentAIContent = '';
        
        // 清理throttle
        if (this.streamUpdateThrottle) {
            clearTimeout(this.streamUpdateThrottle);
            this.streamUpdateThrottle = null;
        }
    }
    
    copyAIResponse(content) {
        navigator.clipboard.writeText(content).then(() => {
            this.showNotification('AI回复已复制到剪贴板', 'success');
        }).catch(() => {
            this.showNotification('复制失败', 'error');
        });
    }
    
    insertAIResponse(content) {
        const textarea = document.getElementById('markdownTextarea');
        if (!textarea) return;
        
        const insertPosition = textarea.selectionStart;
        const currentValue = textarea.value;
        const beforeText = currentValue.substring(0, insertPosition);
        const afterText = currentValue.substring(insertPosition);
        
        // 添加适当的换行符
        let prefix = '';
        let suffix = '';
        
        if (beforeText && !beforeText.endsWith('\n')) {
            prefix = '\n\n';
        } else if (beforeText && !beforeText.endsWith('\n\n')) {
            prefix = '\n';
        }
        
        if (afterText && !afterText.startsWith('\n')) {
            suffix = '\n\n';
        } else if (afterText && !afterText.startsWith('\n\n')) {
            suffix = '\n';
        }
        
        const fullInsertText = prefix + content + suffix;
        const newValue = beforeText + fullInsertText + afterText;
        
        textarea.value = newValue;
        textarea.selectionStart = textarea.selectionEnd = insertPosition + fullInsertText.length;
        
        // 更新内容并预览
        this.editorContent = newValue;
        this.updateWordCount();
        this.updatePreview();
        
        textarea.focus();
        this.showNotification('AI回复已插入到编辑器', 'success');
    }
    
    regenerateAIResponse() {
        // 重新生成最后一条AI回复
        if (this.aiHistory.length >= 2) {
            const lastUserMessage = this.aiHistory[this.aiHistory.length - 2];
            if (lastUserMessage.role === 'user') {
                // 移除最后的AI回复
                this.aiHistory.pop();
                
                // 重新发送消息
                const aiInput = document.getElementById('aiInput');
                if (aiInput) {
                    aiInput.value = lastUserMessage.content;
                    this.sendAIMessage();
                }
            }
        }
    }

    // 获取AI历史记录格式
    getAIHistory() {
        return this.aiHistory.slice(-10); // 只保留最近10条对话
    }

    clearAIHistory() {
        const chatHistory = document.getElementById('aiChatHistory');
        chatHistory.innerHTML = `
            <div class="ai-welcome">
                <p>👋 您好！我是您的AI写作助手。</p>
                <p>我可以帮您：</p>
                <ul>
                    <li>💬 回答问题和提供建议</li>
                    <li>✍️ 重写或改进选中的文本</li>
                    <li>扩展内容或总结要点</li>
                    <li>调整写作风格和语调</li>
                </ul>
            </div>
        `;
    }

    // REMOVED: Duplicate function definition - kept the more complete version at line 4717

    // 复制处理后的文本
    copyProcessedText() {
        if (!window.currentTextProcessingData) return;
        
        const text = window.currentTextProcessingData.processedText;
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showNotification('已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                this.showNotification('复制失败', 'error');
            });
        } else {
            // 兼容性处理
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showNotification('已复制到剪贴板', 'success');
        }
    }
    
    // 插入处理后的文本到当前光标位置
    insertProcessedText() {
        if (!window.currentTextProcessingData) return;
        
        const textarea = document.getElementById('markdownTextarea');
        if (!textarea) return;
        
        const data = window.currentTextProcessingData;
        
        // 使用MarkdownEditor的精准插入功能
        if (this.markdownEditor && this.markdownEditor.insertTextAtPosition) {
            const insertPosition = textarea.selectionStart;
            this.markdownEditor.insertTextAtPosition(data.processedText, insertPosition);
            
            // 触发自定义事件通知主应用
            textarea.dispatchEvent(new CustomEvent('materialInserted', {
                detail: { content: data.processedText, position: insertPosition }
            }));
            
            this.showNotification('已插入生成的内容', 'success');
        } else {
            // 兜底方案：使用传统方法
            const insertPosition = textarea.selectionStart;
            const currentValue = textarea.value;
            const beforeText = currentValue.substring(0, insertPosition);
            const afterText = currentValue.substring(insertPosition);
            
            // 添加适当的换行符
            let prefix = '';
            let suffix = '';
            
            if (beforeText && !beforeText.endsWith('\n')) {
                prefix = '\n\n';
            } else if (beforeText && !beforeText.endsWith('\n\n')) {
                prefix = '\n';
            }
            
            if (afterText && !afterText.startsWith('\n')) {
                suffix = '\n\n';
            } else if (afterText && !afterText.startsWith('\n\n')) {
                suffix = '\n';
            }
            
            const fullInsertText = prefix + data.processedText + suffix;
            const newValue = beforeText + fullInsertText + afterText;
            
            textarea.value = newValue;
            textarea.selectionStart = textarea.selectionEnd = insertPosition + fullInsertText.length;
            
            // 更新内容并预览
            this.editorContent = newValue;
            this.updateWordCount();
            this.updatePreview();
            
            this.showNotification('已插入生成的内容', 'success');
        }
        
        textarea.focus();
    }
    
    // 替换选中的文本或整个文档
    replaceProcessedText() {
        if (!window.currentTextProcessingData) return;
        
        const textarea = document.getElementById('markdownTextarea');
        if (!textarea) return;
        
        const data = window.currentTextProcessingData;
        
        if (data.isFullDocument) {
            // 替换整个文档内容
            textarea.value = data.processedText;
            textarea.selectionStart = 0;
            textarea.selectionEnd = 0;
            this.showNotification('已替换整个文档', 'success');
        } else {
            // 替换选中的文本
            const currentValue = textarea.value;
            const beforeText = currentValue.substring(0, data.selectionStart);
            const afterText = currentValue.substring(data.selectionEnd);
            
            const newValue = beforeText + data.processedText + afterText;
            textarea.value = newValue;
            
            // 选中新插入的文本
            textarea.selectionStart = data.selectionStart;
            textarea.selectionEnd = data.selectionStart + data.processedText.length;
            
            this.showNotification('已替换选中的文本', 'success');
        }
        
        // 更新内容并预览
        this.editorContent = textarea.value;
        this.updateWordCount();
        this.updatePreview();
        
        textarea.focus();
    }
    
    // 重新生成处理后的文本
    async regenerateProcessedText() {
        if (!window.currentTextProcessingData) return;
        
        const data = window.currentTextProcessingData;
        
        // 重新调用处理函数
        await this.processSelectedText(data.action);
    }

    // 获取操作名称
    getActionName(action) {
        const actionNames = {
            'rewrite': '重写',
            'expand': '扩写',
            'summarize': '总结',
            'improve': '改进',
            'translate': '翻译',
            'explain': '解释',
            'correct': '纠错',
            'outline': '大纲生成'
        };
        return actionNames[action] || action;
    }

    handleTextSelection() {
        const selection = window.getSelection();
        const selectedText = selection.toString();
        const processor = document.getElementById('selectedTextProcessor');
        const preview = document.getElementById('selectedTextPreview');
        
        if (processor && preview) {
            if (selectedText && selectedText.trim()) {
                preview.textContent = selectedText;
                processor.style.display = 'block';
            } else {
                processor.style.display = 'none';
            }
        }
    }

    // 拖拽处理方法
    handleDragStart(e, materialId) {
        const material = this.materials.find(m => m.id === materialId);
        if (!material) return;
        
        // 设置拖拽数据 - 修复：传递素材ID而不是内容
        e.dataTransfer.setData('text/plain', materialId);
        e.dataTransfer.setData('application/x-material', JSON.stringify({
            id: material.id,
            type: material.type || 'text',
            content: material.content || '',
            title: this.generateMaterialTitle(material),
            category: material.category,
            keywords: material.keywords
        }));
        
        // 设置拖拽效果
        e.dataTransfer.effectAllowed = 'copy';
        
        // 添加拖拽样式
        const dragElement = e.target.closest('.material-item');
        if (dragElement) {
            dragElement.classList.add('dragging');
            
            // 创建拖拽预览
            this.createDragPreview(e, material);
        }
        
        console.log('开始拖拽素材:', material.id);
    }

    // 生成素材标题
    generateMaterialTitle(material) {
        if (!material.content) return '未命名素材';
        
        const firstLine = material.content.split('\n')[0].trim();
        return firstLine.length > 50 ? firstLine.substring(0, 50) + '...' : firstLine;
    }

    // 创建拖拽预览
    createDragPreview(e, material) {
        const preview = document.createElement('div');
        preview.className = 'drag-preview';
        preview.style.cssText = `
            position: absolute;
            top: -1000px;
            left: -1000px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            max-width: 200px;
            z-index: 1000;
            font-size: 12px;
            pointer-events: none;
        `;
        
        const typeIcon = this.getTypeIcon(material.type || 'text');
        const title = this.generateMaterialTitle(material);
        
        preview.innerHTML = `
            <div style="display: flex; align-items: center; gap: 6px;">
                <div style="color: #666;">${typeIcon}</div>
                <div style="font-weight: 500; color: #333;">${this.escapeHtml(title)}</div>
            </div>
        `;
        
        document.body.appendChild(preview);
        
        // 设置拖拽预览图像
        e.dataTransfer.setDragImage(preview, 10, 10);
        
        // 延迟移除预览元素
        setTimeout(() => {
            if (preview.parentNode) {
                preview.parentNode.removeChild(preview);
            }
        }, 100);
    }

    // AI相关事件监听
    bindAIEvents() {
        // 流式输出监听
        ipcRenderer.on('ai-stream-chunk', (event, chunk) => {
            this.handleAIStreamChunk(chunk);
        });
        
        // 流式开始监听
        ipcRenderer.on('ai-stream-start', (event, data) => {
            this.startAIStream(data);
        });
        
        // 流式结束监听
        ipcRenderer.on('ai-stream-end', (event, data) => {
            this.finishAIStream(data);
        });
        
        // 流式错误监听
        ipcRenderer.on('ai-stream-error', (event, error) => {
            this.handleAIStreamError(error);
        });
    }
    
    // 开始AI流式输出
    startAIStream(data) {
        if (this.currentAIMessage) {
            this.currentAIMessage.innerHTML = `
                <div class="ai-message-content">
                    <div class="ai-message-header">
                        <div class="ai-avatar">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.12.23-2.19.64-3.17L6.5 10.5C6.19 11.29 6 12.13 6 13c0 3.31 2.69 6 6 6s6-2.69 6-6-2.69-6-6-6c-.87 0-1.71.19-2.5.5L7.83 5.83C9.1 4.67 10.5 4 12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/>
                                <circle cx="9" cy="9" r="2"/>
                                <circle cx="15" cy="15" r="2"/>
                                <path d="M9 15l6-6"/>
                            </svg>
                        </div>
                        <div class="ai-status">
                            <span class="ai-status-text">AI正在思考中</span>
                            <div class="ai-typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                    <div class="ai-stream-content"></div>
                </div>
            `;
            
            this.currentAIMessage.classList.add('streaming');
            this.currentAIContent = '';
            this.isAIStreaming = true;
            
            // 滚动到底部
            const aiChatHistory = document.getElementById('aiChatHistory');
            if (aiChatHistory) {
                aiChatHistory.scrollTop = aiChatHistory.scrollHeight;
            }
        }
    }
    
    // 处理AI流式输出块
    handleAIStreamChunk(chunk) {
        if (!this.currentAIMessage || !this.isAIStreaming) return;
        
        this.currentAIContent += chunk;
        
        // 使用throttle避免过于频繁的DOM更新
        if (this.streamUpdateThrottle) {
            clearTimeout(this.streamUpdateThrottle);
        }
        
        this.streamUpdateThrottle = setTimeout(() => {
            // 更新状态文本
            const statusText = this.currentAIMessage.querySelector('.ai-status-text');
            if (statusText) {
                statusText.textContent = 'AI正在输出中...';
            }
            
            // 更新流式内容
            const streamContent = this.currentAIMessage.querySelector('.ai-stream-content');
            if (streamContent) {
                try {
                    // 优化的markdown实时渲染
                    streamContent.innerHTML = `<div class="ai-processed-text">${this.formatStreamContent(this.currentAIContent)}</div>`;
                } catch (error) {
                    console.error('流式内容格式化失败:', error);
                    // fallback到纯文本显示
                    streamContent.innerHTML = `<div class="ai-processed-text">${this.escapeHtml(this.currentAIContent)}</div>`;
                }
            }
            
            // 自动滚动到底部 - 使用requestAnimationFrame优化
            requestAnimationFrame(() => {
                const aiChatHistory = document.getElementById('aiChatHistory');
                if (aiChatHistory) {
                    aiChatHistory.scrollTop = aiChatHistory.scrollHeight;
                }
            });
        }, 50); // 20 FPS throttle
    }
    
    // 格式化流式内容
    formatStreamContent(content) {
        if (!content) return '';
        
        try {
            // 转义HTML字符 - 但是保持换行符
            let escaped = content
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;');
            
            // 更好的markdown格式化 - 先处理代码块避免干扰
            let formatted = escaped
                // 处理代码块
                .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
                // 处理行内代码
                .replace(/`([^`]+)`/g, '<code>$1</code>')
                // 处理粗体和斜体 - 使用更精确的正则
                .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                .replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, '<em>$1</em>')
                // 处理链接
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
                // 处理段落和换行
                .replace(/\n\n+/g, '</p><p>')
                .replace(/\n/g, '<br>');
            
            // 包装在段落中
            if (formatted && !formatted.startsWith('<pre>') && !formatted.startsWith('<ul>') && !formatted.startsWith('<ol>')) {
                formatted = '<p>' + formatted + '</p>';
            }
            
            // 清理空段落
            formatted = formatted.replace(/<p><\/p>/g, '').replace(/<p><br><\/p>/g, '');
            
            return formatted;
        } catch (error) {
            console.error('formatStreamContent error:', error);
            // fallback到基本HTML转义
            return this.escapeHtml(content).replace(/\n/g, '<br>');
        }
    }
    
    // 完成AI流式输出
    finishAIStream(data) {
        if (!this.currentAIMessage) return;
        
        this.currentAIMessage.classList.remove('streaming');
        
        // 清理throttle
        if (this.streamUpdateThrottle) {
            clearTimeout(this.streamUpdateThrottle);
            this.streamUpdateThrottle = null;
        }
        
        // 更新最终内容
        const finalContent = data?.finalContent || this.currentAIContent;
        this.currentAIContent = finalContent;
        
        // 更新消息内容
        this.currentAIMessage.innerHTML = `
            <div class="ai-message-content">
                <div class="ai-message-header">
                    <div class="ai-avatar">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.12.23-2.19.64-3.17L6.5 10.5C6.19 11.29 6 12.13 6 13c0 3.31 2.69 6 6 6s6-2.69 6-6-2.69-6-6-6c-.87 0-1.71.19-2.5.5L7.83 5.83C9.1 4.67 10.5 4 12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/>
                            <circle cx="9" cy="9" r="2"/>
                            <circle cx="15" cy="15" r="2"/>
                            <path d="M9 15l6-6"/>
                        </svg>
                    </div>
                    <div class="ai-status">
                        <span class="ai-status-text">生成完成</span>
                        <div class="ai-status-indicator success"></div>
                    </div>
                </div>
                <div class="ai-processed-text">${this.formatStreamContent(finalContent)}</div>
            </div>
        `;
        
        // 添加操作按钮
        const actionButtons = document.createElement('div');
        actionButtons.className = 'ai-message-actions';
        
        // 获取当前文本处理数据
        const textData = window.currentTextProcessingData;
        if (textData) {
            textData.processedText = finalContent;
            
            actionButtons.innerHTML = `
                <button class="ai-action-btn ai-action-copy" onclick="organizationApp.copyProcessedText()" title="复制生成的内容">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                    <span>复制</span>
                </button>
                <button class="ai-action-btn ai-action-insert" onclick="organizationApp.insertProcessedText()" title="插入到光标位置">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    <span>插入</span>
                </button>
                ${!textData.isFullDocument ? `
                    <button class="ai-action-btn ai-action-replace" onclick="organizationApp.replaceProcessedText()" title="替换选中的文本">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                        <span>替换</span>
                    </button>
                ` : `
                    <button class="ai-action-btn ai-action-replace" onclick="organizationApp.replaceProcessedText()" title="替换整个文档">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                        <span>替换文档</span>
                    </button>
                `}
                <button class="ai-action-btn ai-action-regenerate" onclick="organizationApp.regenerateProcessedText()" title="重新生成">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                    </svg>
                    <span>重新生成</span>
                </button>
            `;
        }
        
        this.currentAIMessage.appendChild(actionButtons);
        
        // 重置状态
        this.isAIStreaming = false;
        this.currentAIMessage = null;
        
        // 滚动到底部
        const aiChatHistory = document.getElementById('aiChatHistory');
        if (aiChatHistory) {
            aiChatHistory.scrollTop = aiChatHistory.scrollHeight;
        }
    }
    
    // 处理AI流式错误 - 现在使用统一的错误处理
    handleAIStreamError(error) {
        this.handleAIError(error);
    }
    
    // 格式化AI内容
    formatAIContent(content) {
        // 简单的markdown格式化
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
    
    // 旧版本兼容方法
    finishAIStream() {
        if (this.currentAIMessage) {
            this.currentAIMessage.classList.remove('streaming');
            
            // 添加操作按钮
            const actionButtons = document.createElement('div');
            actionButtons.className = 'ai-message-actions';
            actionButtons.innerHTML = `
                <button class="btn-secondary btn-sm" onclick="organizationApp.copyAIResponse('${this.currentAIContent.replace(/'/g, '\\\'')}')" title="复制">${this.getIcon('copy')}</button>
                <button class="btn-secondary btn-sm" onclick="organizationApp.insertAIResponse('${this.currentAIContent.replace(/'/g, '\\\'')}')" title="插入到编辑器">${this.getIcon('edit')}</button>
                <button class="btn-secondary btn-sm" onclick="organizationApp.regenerateAIResponse()" title="重新生成">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                    </svg>
                </button>
            `;
            this.currentAIMessage.appendChild(actionButtons);
        }
        
        this.isAIStreaming = false;
        this.currentAIMessage = null;
    }
    
    // 旧版本兼容方法
    handleAIError(error) {
        if (this.currentAIMessage) {
            this.currentAIMessage.className = 'ai-message system';
            this.currentAIMessage.textContent = `AI服务错误: ${error}`;
        }
        
        this.isAIStreaming = false;
        this.currentAIMessage = null;
    }
    
    copyAIResponse(content) {
        navigator.clipboard.writeText(content).then(() => {
            this.showNotification('AI回复已复制到剪贴板', 'success');
        }).catch(() => {
            this.showNotification('复制失败', 'error');
        });
    }
    
    insertAIResponse(content) {
        const textarea = document.getElementById('markdownTextarea');
        if (!textarea) return;
        
        const insertPosition = textarea.selectionStart;
        const currentValue = textarea.value;
        const beforeText = currentValue.substring(0, insertPosition);
        const afterText = currentValue.substring(insertPosition);
        
        // 添加适当的换行符
        let prefix = '';
        let suffix = '';
        
        if (beforeText && !beforeText.endsWith('\n')) {
            prefix = '\n\n';
        } else if (beforeText && !beforeText.endsWith('\n\n')) {
            prefix = '\n';
        }
        
        if (afterText && !afterText.startsWith('\n')) {
            suffix = '\n\n';
        } else if (afterText && !afterText.startsWith('\n\n')) {
            suffix = '\n';
        }
        
        const fullInsertText = prefix + content + suffix;
        const newValue = beforeText + fullInsertText + afterText;
        
        textarea.value = newValue;
        textarea.selectionStart = textarea.selectionEnd = insertPosition + fullInsertText.length;
        
        // 更新内容并预览
        this.editorContent = newValue;
        this.updateWordCount();
        this.updatePreview();
        
        textarea.focus();
        this.showNotification('AI回复已插入到编辑器', 'success');
    }
    

    // 获取AI历史记录格式
    getAIHistory() {
        return this.aiHistory.slice(-10); // 只保留最近10条对话
    }

    clearAIHistory() {
        this.aiHistory = [];
        const chatHistory = document.getElementById('aiChatHistory');
        chatHistory.innerHTML = `
            <div class="ai-welcome">
                <div class="ai-welcome-header">
                    <div class="ai-welcome-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.12.23-2.19.64-3.17L6.5 10.5C6.19 11.29 6 12.13 6 13c0 3.31 2.69 6 6 6s6-2.69 6-6-2.69-6-6-6c-.87 0-1.71.19-2.5.5L7.83 5.83C9.1 4.67 10.5 4 12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/>
                            <circle cx="9" cy="9" r="2"/>
                            <circle cx="15" cy="15" r="2"/>
                            <path d="M9 15l6-6"/>
                        </svg>
                    </div>
                    <h3>AI写作助手</h3>
                </div>
                <div class="ai-welcome-content">
                    <p>👋 您好！我是您的AI写作助手，支持实时流式对话。</p>
                    <div class="ai-capabilities">
                        <div class="ai-capability">
                            <span class="capability-icon">💬</span>
                            <span>智能对话与问答</span>
                        </div>
                        <div class="ai-capability">
                            <span class="capability-icon">✍️</span>
                            <span>文本重写与优化</span>
                        </div>
                        <div class="ai-capability">
                            <span class="capability-icon">📝</span>
                            <span>内容扩展与总结</span>
                        </div>
                        <div class="ai-capability">
                            <span class="capability-icon">🎨</span>
                            <span>风格调整与润色</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async processSelectedText(action) {
        const textarea = document.getElementById('markdownTextarea');
        if (!textarea) return;
        
        const selectedText = textarea.value.substring(textarea.selectionStart, textarea.selectionEnd);
        let textToProcess = selectedText;
        let isFullDocument = false;
        
        // 保存选中文本的位置信息
        const selectionStart = textarea.selectionStart;
        const selectionEnd = textarea.selectionEnd;
        
        // 如果没有选中文本，处理整个文档内容
        if (!selectedText || selectedText.trim() === '') {
            textToProcess = textarea.value;
            isFullDocument = true;
            
            if (!textToProcess || textToProcess.trim() === '') {
                this.showNotification('文档内容为空，请先输入内容', 'warning');
                return;
            }
        }
        
        // 切换到AI面板
        this.switchRightPanel('ai');
        
        // 添加用户请求消息到AI对话历史
        const aiChatHistory = document.getElementById('aiChatHistory');
        const actionName = this.getActionName(action);
        const userMessage = document.createElement('div');
        userMessage.className = 'ai-message user';
        userMessage.innerHTML = `
            <div class="ai-message-content">
                <div class="ai-message-header">
                    <div class="user-avatar">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                    </div>
                    <div class="message-info">
                        <span class="message-role">您</span>
                        <span class="message-time">${new Date().toLocaleTimeString()}</span>
                    </div>
                </div>
                <div class="user-request">
                    <p><strong>请${actionName}以下内容：</strong></p>
                    <div class="user-content-preview">${isFullDocument ? 
                        '<span class="content-type">整个文档内容</span>' : 
                        `<span class="content-preview">${this.escapeHtml(textToProcess.substring(0, 200) + (textToProcess.length > 200 ? '...' : ''))}</span>`
                    }</div>
                </div>
            </div>
        `;
        aiChatHistory.appendChild(userMessage);
        
        // 添加AI回复容器
        const aiReply = document.createElement('div');
        aiReply.className = 'ai-message assistant';
        aiChatHistory.appendChild(aiReply);
        aiChatHistory.scrollTop = aiChatHistory.scrollHeight;
        
        // 保存当前对话状态
        this.currentAIMessage = aiReply;
        this.currentAIContent = '';
        this.isAIStreaming = true;
        
        // 创建操作按钮数据
        const buttonData = {
            originalText: textToProcess,
            processedText: '', // 将在流式输出完成后更新
            action: action,
            isFullDocument: isFullDocument,
            selectionStart: selectionStart,
            selectionEnd: selectionEnd
        };
        
        // 将按钮数据存储到全局变量中
        window.currentTextProcessingData = buttonData;
        
        try {
            // 调用AI文本处理服务（支持流式输出）
            const response = await ipcRenderer.invoke('ai-process-text-stream', textToProcess, action);
            
            if (!response.success) {
                this.handleAIStreamError(response.error);
                return;
            }
            
            // 流式输出已通过事件处理
            // 保存到AI历史记录
            this.aiHistory.push({ 
                role: 'user', 
                content: `请${actionName}以下内容：\n${textToProcess}` 
            });
            
            // 限制历史记录长度
            if (this.aiHistory.length > 20) {
                this.aiHistory = this.aiHistory.slice(-20);
            }
            
        } catch (error) {
            console.error('文本处理失败:', error);
            this.handleAIStreamError(error.message);
        }
    }
}

// 全局实例
const organizationApp = new OrganizationApp();

// 导出供HTML调用
window.organizationApp = organizationApp;
window.app = organizationApp; // 保持向后兼容