const { ipcRenderer } = require('electron');

class DragPopup {
    constructor() {
        this.contentData = null;
        this.isProcessing = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupIPC();
    }

    bindEvents() {
        // 保存按钮
        document.getElementById('saveBtn').addEventListener('click', () => {
            this.saveContent();
        });

        // 取消按钮
        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.closePopup();
        });

        // 整理按钮
        document.getElementById('organizeBtn').addEventListener('click', () => {
            this.switchToOrganization();
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closePopup();
            } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                this.saveContent();
            }
        });

        // 防止窗口失去焦点时自动关闭
        window.addEventListener('blur', () => {
            setTimeout(() => {
                this.closePopup();
            }, 200);
        });
    }

    setupIPC() {
        // 监听内容拖拽事件
        ipcRenderer.on('content-dropped', (event, data) => {
            this.contentData = data;
            this.displayContent(data);
        });

        // 监听保存结果
        ipcRenderer.on('collection-saved', (event, result) => {
            if (result.success) {
                this.showSuccess();
                setTimeout(() => {
                    this.closePopup();
                }, 1000);
            } else {
                this.showError(result.error);
            }
        });
    }

    displayContent(data) {
        const { content, type, timestamp } = data;
        
        // 更新内容类型图标
        const typeIcon = this.getTypeIcon(type);
        document.getElementById('contentType').innerHTML = typeIcon;
        
        // 更新内容预览
        const preview = this.getContentPreview(content, type);
        document.getElementById('contentText').textContent = preview;
        
        // 添加类型样式
        const container = document.querySelector('.popup-container');
        container.className = `popup-container ${type}`;
    }

    getTypeIcon(type) {
        const icons = {
            'text': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h10v2H7V7zm0 4h10v2H7v-2zm0 4h7v2H7v-2z"/></svg>',
            'image': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M5 3h14c1.1 0 2 .9 2 2v14c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V5c0-1.1.9-2 2-2zm0 2v10.5l3.5-3.5 2.5 2.5L16.5 9 19 11.5V5H5zm0 14h14v-1.5L16.5 13 11 18.5 8.5 16 5 19.5V19z"/></svg>',
            'link': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M10.59 13.41c.41.39.41 1.03 0 1.42-.39.39-1.03.39-1.42 0a5.003 5.003 0 0 1 0-7.07l3.54-3.54a5.003 5.003 0 0 1 7.07 0 5.003 5.003 0 0 1 0 7.07l-1.49 1.49c.01-.82-.12-1.64-.4-2.42l.47-.48a2.982 2.982 0 0 0 0-4.24 2.982 2.982 0 0 0-4.24 0l-3.53 3.53a2.982 2.982 0 0 0 0 4.24zm2.82-4.24c.39-.39 1.03-.39 1.42 0a5.003 5.003 0 0 1 0 7.07l-3.54 3.54a5.003 5.003 0 0 1-7.07 0 5.003 5.003 0 0 1 0-7.07l1.49-1.49c-.01.82.12 1.64.4 2.42l-.47.48a2.982 2.982 0 0 0 0 4.24 2.982 2.982 0 0 0 4.24 0l3.53-3.53a2.982 2.982 0 0 0 0-4.24.973.973 0 0 1 0-1.42z"/></svg>',
            'file': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h10c1.1 0 2-.9 2-2V8l-6-6H6zm7 7V3.5L18.5 9H13z"/></svg>',
            'code': '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M8.7 15.9L4.8 12l3.9-3.9c.39-.39.39-1.01 0-1.4s-1.01-.39-1.4 0l-4.59 4.59c-.39.39-.39 1.02 0 1.41l4.59 4.6c.39.39 1.01.39 1.4 0 .39-.39.39-1.01 0-1.4zm6.6 0l3.9-3.9-3.9-3.9c-.39-.39-.39-1.01 0-1.4s1.01-.39 1.4 0l4.59 4.59c.39.39.39 1.02 0 1.41L16.7 16.3c-.39.39-1.01.39-1.4 0-.39-.39-.39-1.01 0-1.4z"/></svg>'
        };
        return icons[type] || icons.file;
    }

    getContentPreview(content, type) {
        const maxLength = 100;
        
        switch (type) {
            case 'image':
                return '图片内容';
            case 'link':
                try {
                    const url = new URL(content);
                    return `链接: ${url.hostname}`;
                } catch (e) {
                    return content.substring(0, maxLength) + (content.length > maxLength ? '...' : '');
                }
            case 'file':
                return `文件: ${content.split(/[/\\]/).pop()}`;
            default:
                return content.substring(0, maxLength) + (content.length > maxLength ? '...' : '');
        }
    }

    async saveContent() {
        if (this.isProcessing || !this.contentData) return;
        
        this.isProcessing = true;
        this.showLoading();
        
        try {
            const collectionData = {
                content: this.contentData.content,
                type: this.contentData.type,
                source: 'drag-drop',
                timestamp: this.contentData.timestamp
            };
            
            const result = await ipcRenderer.invoke('save-collection', collectionData);
            
            if (result) {
                this.showSuccess();
                setTimeout(() => {
                    this.closePopup();
                }, 1000);
            }
            
        } catch (error) {
            console.error('保存失败:', error);
            this.showError(error.message);
        } finally {
            this.isProcessing = false;
            this.hideLoading();
        }
    }

    async switchToOrganization() {
        try {
            await ipcRenderer.invoke('switch-to-organization');
        } catch (error) {
            console.error('切换模式失败:', error);
        }
    }

    closePopup() {
        const container = document.querySelector('.popup-container');
        container.classList.add('closing');
        
        setTimeout(() => {
            window.close();
        }, 200);
    }

    showLoading() {
        const container = document.querySelector('.popup-container');
        container.classList.add('loading');
        
        const saveBtn = document.getElementById('saveBtn');
        saveBtn.disabled = true;
        saveBtn.textContent = '保存中...';
    }

    hideLoading() {
        const container = document.querySelector('.popup-container');
        container.classList.remove('loading');
        
        const saveBtn = document.getElementById('saveBtn');
        saveBtn.disabled = false;
        saveBtn.textContent = '保存';
    }

    showSuccess() {
        const container = document.querySelector('.popup-container');
        container.classList.add('success');
        
        const saveBtn = document.getElementById('saveBtn');
        saveBtn.textContent = '已保存';
        saveBtn.style.background = 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)';
        
        // 显示成功消息
        this.showMessage('收藏已保存！', 'success');
    }

    showError(message) {
        const container = document.querySelector('.popup-container');
        container.classList.add('error');
        
        const saveBtn = document.getElementById('saveBtn');
        saveBtn.textContent = '重试';
        saveBtn.disabled = false;
        
        this.showMessage(message || '保存失败', 'error');
        
        // 3秒后恢复
        setTimeout(() => {
            container.classList.remove('error');
            saveBtn.textContent = '保存';
            saveBtn.style.background = '';
        }, 3000);
    }

    showMessage(text, type = 'info') {
        // 创建消息元素
        const message = document.createElement('div');
        message.className = `popup-message ${type}`;
        message.textContent = text;
        
        // 添加到容器
        const container = document.querySelector('.popup-container');
        container.appendChild(message);
        
        // 自动移除
        setTimeout(() => {
            message.remove();
        }, 3000);
    }

    // 处理特殊内容类型
    processImageContent(content) {
        // 如果是图片URL，可以显示缩略图
        if (content.startsWith('http') && this.isImageUrl(content)) {
            const img = document.createElement('img');
            img.src = content;
            img.style.maxWidth = '100px';
            img.style.maxHeight = '60px';
            img.style.objectFit = 'cover';
            
            const contentText = document.getElementById('contentText');
            contentText.innerHTML = '';
            contentText.appendChild(img);
        }
    }

    isImageUrl(url) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
        return imageExtensions.some(ext => url.toLowerCase().includes(ext));
    }

    processLinkContent(content) {
        try {
            const url = new URL(content);
            const contentText = document.getElementById('contentText');
            contentText.innerHTML = `
                <div class="link-preview">
                    <div class="link-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10.59 13.41c.41.39.41 1.03 0 1.42-.39.39-1.03.39-1.42 0a5.003 5.003 0 0 1 0-7.07l3.54-3.54a5.003 5.003 0 0 1 7.07 0 5.003 5.003 0 0 1 0 7.07l-1.49 1.49c.01-.82-.12-1.64-.4-2.42l.47-.48a2.982 2.982 0 0 0 0-4.24 2.982 2.982 0 0 0-4.24 0l-3.53 3.53a2.982 2.982 0 0 0 0 4.24zm2.82-4.24c.39-.39 1.03-.39 1.42 0a5.003 5.003 0 0 1 0 7.07l-3.54 3.54a5.003 5.003 0 0 1-7.07 0 5.003 5.003 0 0 1 0-7.07l1.49-1.49c-.01.82.12 1.64.4 2.42l-.47.48a2.982 2.982 0 0 0 0 4.24 2.982 2.982 0 0 0 4.24 0l3.53-3.53a2.982 2.982 0 0 0 0-4.24.973.973 0 0 1 0-1.42z"/>
                        </svg>
                    </div>
                    <div class="link-info">
                        <div class="link-title">${url.hostname}</div>
                        <div class="link-url">${url.pathname}</div>
                    </div>
                </div>
            `;
        } catch (e) {
            // 如果不是有效URL，按普通文本处理
        }
    }

    // 添加动画效果
    animateSuccess() {
        const container = document.querySelector('.popup-container');
        container.style.animation = 'successPulse 0.6s ease-out';
        
        setTimeout(() => {
            container.style.animation = '';
        }, 600);
    }

    animateError() {
        const container = document.querySelector('.popup-container');
        container.style.animation = 'errorShake 0.5s ease-out';
        
        setTimeout(() => {
            container.style.animation = '';
        }, 500);
    }
}

// 初始化拖拽弹窗
const dragPopup = new DragPopup();

// 导出供调试使用
window.dragPopup = dragPopup;