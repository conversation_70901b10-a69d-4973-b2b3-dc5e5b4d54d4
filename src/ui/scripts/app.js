// Electron IPC 将在运行时可用
// const { ipc<PERSON><PERSON><PERSON> } = window.require ? window.require('electron') : { ipcRenderer: null };

class OrganizationApp {
    constructor() {
        // 数据状态
        this.materials = [];
        this.files = [];
        this.currentDocument = null;
        this.editorContent = '';
        this.selectedText = '';
        
        // 为了向后兼容，保持 collections 引用
        this.collections = this.materials;
        
        // 初始化管理器
        this.initializeManagers();
        
        // 初始化应用
        this.init();
    }

    initializeManagers() {
        // 初始化各个管理器
        this.uiManager = new UIManager(this);
        this.materialsManager = new MaterialsManager(this);
        this.filesManager = new FilesManager(this);
        this.aiManager = new AIManager(this);
        this.editorManager = new EditorManager(this);
    }

    async init() {
        try {
            // 将实例暴露给全局，以便MarkdownEditor访问
            window.organizationApp = this;
            window.app = this; // 保持向后兼容
            
            // 绑定IPC事件
            this.bindIPCEvents();
            
            // 加载初始数据
            await this.loadInitialData();
            
            // 确保材料数据可用
            if (!this.materials || this.materials.length === 0) {
                console.log('正在加载材料数据...');
                await this.loadMaterials();
            }
            
            console.log('整理模式界面初始化完成，材料数量:', this.materials.length);
            
        } catch (error) {
            console.error('初始化失败:', error);
            CoreUtils.showNotification('初始化失败: ' + error.message, 'error');
        }
    }

    async loadInitialData() {
        // 加载素材数据
        await this.materialsManager.loadMaterials();
        
        // 加载文件数据
        await this.filesManager.loadFiles();
        
        // 更新状态栏
        this.uiManager.updateStatusBar();
    }

    bindIPCEvents() {
        // 监听收藏数据加载
        ipcRenderer.on('collections-loaded', (event, collections) => {
            this.collections = collections;
            this.materials = collections;
            this.materialsManager.materials = collections;
            this.materialsManager.renderMaterials();
        });
        
        // 监听AI分析结果
        ipcRenderer.on('ai-analysis-started', () => {
            CoreUtils.showLoading('AI分析中...');
        });
        
        ipcRenderer.on('ai-analysis-completed', (event, result) => {
            CoreUtils.hideLoading();
            
            if (result.success) {
                this.analysisResults = result.results;
                this.renderAnalysisResults();
                CoreUtils.showNotification('AI分析完成', 'success');
            } else {
                CoreUtils.showNotification('AI分析失败: ' + (result.error || result.message), 'error');
            }
        });
        
        // 监听Markdown生成结果
        ipcRenderer.on('markdown-generation-started', () => {
            CoreUtils.showLoading('生成Markdown中...');
        });
        
        ipcRenderer.on('markdown-generation-completed', (event, result) => {
            CoreUtils.hideLoading();
            
            if (result.success) {
                this.insertMarkdown(result.markdown);
                CoreUtils.showNotification('Markdown生成完成', 'success');
            } else {
                CoreUtils.showNotification('Markdown生成失败: ' + result.error, 'error');
            }
        });
        
        // 监听重复检测结果
        ipcRenderer.on('duplicates-detected', (event, duplicates) => {
            this.showDuplicatesModal(duplicates);
        });
    }

    // 兼容性方法 - 委托给相应的管理器
    async loadMaterials() {
        return this.materialsManager.loadMaterials();
    }

    async loadFiles() {
        return this.filesManager.loadFiles();
    }

    saveDocument() {
        return this.editorManager.saveDocument();
    }

    addNewMaterial() {
        return this.materialsManager.addNewMaterial();
    }

    toggleMaterialsSearch() {
        return this.materialsManager.toggleMaterialsSearch();
    }

    switchLeftTab(tabName) {
        return this.uiManager.switchLeftTab(tabName);
    }

    switchRightPanel(panelName) {
        return this.uiManager.switchRightPanel(panelName);
    }

    showNotification(message, type, duration) {
        return CoreUtils.showNotification(message, type, duration);
    }

    showModal(modalId) {
        return CoreUtils.showModal(modalId);
    }

    hideModal(modalId) {
        return CoreUtils.hideModal(modalId);
    }

    // 插入Markdown内容
    insertMarkdown(markdown) {
        this.editorManager.insertText(markdown);
    }

    // 显示重复检测模态框
    showDuplicatesModal(duplicates) {
        const modal = document.getElementById('duplicatesModal');
        if (!modal) return;
        
        const list = modal.querySelector('.duplicates-list');
        if (list) {
            list.innerHTML = duplicates.map(dup => `
                <div class="duplicate-item">
                    <h4>${dup.title}</h4>
                    <p>${dup.content.substring(0, 100)}...</p>
                    <small>创建时间: ${CoreUtils.formatTime(dup.created_at)}</small>
                </div>
            `).join('');
        }
        
        CoreUtils.showModal('duplicatesModal');
    }

    // 渲染分析结果
    renderAnalysisResults() {
        const container = document.getElementById('analysisResults');
        if (!container || !this.analysisResults) return;
        
        container.innerHTML = this.analysisResults.map(result => `
            <div class="analysis-item">
                <h4>${result.title}</h4>
                <p>${result.summary}</p>
                <div class="analysis-tags">
                    ${result.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            </div>
        `).join('');
    }

    // 获取图标（兼容性方法）
    getIcon(iconName) {
        return CoreUtils.getIcon(iconName);
    }

    // 格式化时间（兼容性方法）
    formatTime(timestamp) {
        return CoreUtils.formatTime(timestamp);
    }

    // 转义HTML（兼容性方法）
    escapeHtml(text) {
        return CoreUtils.escapeHtml(text);
    }

    // 获取类型图标（兼容性方法）
    getTypeIcon(type) {
        return CoreUtils.getTypeIcon(type);
    }

    // 生成内容预览（兼容性方法）
    generateContentPreview(content, type) {
        return CoreUtils.generateContentPreview(content, type);
    }

    // 截断内容（兼容性方法）
    truncateContent(content, maxLength) {
        return CoreUtils.truncateContent(content, maxLength);
    }

    // 格式化文件大小（兼容性方法）
    formatFileSize(bytes) {
        return CoreUtils.formatFileSize(bytes);
    }

    // 显示加载状态（兼容性方法）
    showLoading(message) {
        return CoreUtils.showLoading(message);
    }

    // 隐藏加载状态（兼容性方法）
    hideLoading() {
        return CoreUtils.hideLoading();
    }

    // 显示元素加载状态（兼容性方法）
    showElementLoading(elementId) {
        return CoreUtils.showElementLoading(elementId);
    }

    // 隐藏元素加载状态（兼容性方法）
    hideElementLoading(elementId) {
        return CoreUtils.hideElementLoading(elementId);
    }

    // 防抖函数（兼容性方法）
    debounce(func, wait) {
        return CoreUtils.debounce(func, wait);
    }

    // 节流函数（兼容性方法）
    throttle(func, limit) {
        return CoreUtils.throttle(func, limit);
    }

    // 销毁应用
    destroy() {
        if (this.editorManager) {
            this.editorManager.destroy();
        }
        
        if (this.uiManager) {
            this.uiManager.destroy();
        }
    }
}

// 等待DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    // 全局实例
    const organizationApp = new OrganizationApp();

    // 导出供HTML调用
    window.organizationApp = organizationApp;
    window.app = organizationApp; // 保持向后兼容
});
