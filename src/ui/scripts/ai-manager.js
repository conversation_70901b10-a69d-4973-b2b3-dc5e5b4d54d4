class AIManager {
    constructor(app) {
        this.app = app;
        this.aiHistory = [];
        this.selectedText = '';
        this.selectedTextRange = null;
        this.isAIStreaming = false;
        this.currentAIMessage = null;
        this.currentAIContent = '';
        this.streamUpdateThrottle = null;
        
        // 延迟绑定事件，确保DOM已加载
        setTimeout(() => {
            this.bindEvents();
            this.bindAIEvents();
        }, 0);
    }

    bindEvents() {
        // AI 聊天按钮
        document.getElementById('aiSendBtn')?.addEventListener('click', () => this.sendAIMessage());
        document.getElementById('aiClearBtn')?.addEventListener('click', () => this.clearAIHistory());
        
        // AI 输入框
        const aiInput = document.getElementById('aiInput');
        if (aiInput) {
            aiInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    this.sendAIMessage();
                }
            });
            
            // 自动调整输入框高度
            aiInput.addEventListener('input', () => {
                this.autoResizeTextarea(aiInput);
            });
        }
        
        // 选中文本处理器关闭按钮
        document.getElementById('closeSelectedTextProcessor')?.addEventListener('click', () => {
            this.hideSelectedTextProcessor();
        });
        
        // 监听编辑器文本选择
        const editor = document.getElementById('editorContent');
        if (editor) {
            editor.addEventListener('mouseup', () => this.updateTextSelection());
            editor.addEventListener('keyup', () => this.updateTextSelection());
        }
        
        // 快捷操作按钮
        document.addEventListener('click', (e) => {
            if (e.target.matches('.quick-action-btn') || e.target.closest('.quick-action-btn')) {
                const btn = e.target.matches('.quick-action-btn') ? e.target : e.target.closest('.quick-action-btn');
                const action = btn.dataset.action;
                if (action) {
                    this.processSelectedTextWithAction(action);
                }
            }
        });
    }

    bindAIEvents() {
        // 监听AI流式响应
        ipcRenderer.on('ai-stream-data', (event, chunk) => {
            this.handleAIStreamChunk({ chunk });
        });
        
        ipcRenderer.on('ai-stream-end', (event) => {
            this.handleAIStreamEnd();
        });
        
        ipcRenderer.on('ai-stream-error', (event, error) => {
            this.handleAIStreamError(error);
        });
        
        // 监听AI文本处理流式响应
        ipcRenderer.on('ai-text-process-stream-start', (event, data) => {
            this.handleTextProcessStreamStart(data);
        });
        
        ipcRenderer.on('ai-text-process-stream-chunk', (event, data) => {
            this.handleTextProcessStreamChunk(data);
        });
        
        ipcRenderer.on('ai-text-process-stream-end', (event, data) => {
            this.handleTextProcessStreamEnd(data);
        });
        
        ipcRenderer.on('ai-text-process-stream-error', (event, error) => {
            this.handleTextProcessStreamError(error);
        });
        
        // AI面板事件已在bindAIEvents中处理
        
        // 绑定AI消息操作按钮事件（使用事件委托）
        this.bindAIMessageActions();
    }
    
    bindAIMessageActions() {
        const aiChatHistory = document.getElementById('aiChatHistory');
        if (!aiChatHistory) return;
        
        // 使用事件委托处理所有AI消息操作按钮
        aiChatHistory.addEventListener('click', (event) => {
            const button = event.target.closest('.action-btn');
            if (!button) return;
            
            event.preventDefault();
            event.stopPropagation();
            
            const action = button.getAttribute('data-action');
            const content = button.getAttribute('data-content');
            const messageIndex = button.getAttribute('data-message-index');
            
            switch (action) {
                case 'copy':
                    this.copyToClipboard(content);
                    break;
                case 'insert':
                    this.insertToEditor(content);
                    break;
                case 'replace':
                    this.replaceSelectedText(content);
                    break;
                case 'regenerate':
                    this.regenerateMessage(parseInt(messageIndex));
                    break;
                default:
                    console.warn('未知的操作类型:', action);
            }
        });
    }

    initializeAIPanel() {
        // 初始化AI面板
        this.renderAIHistory();
        this.updateTextSelection();
    }

    autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        const maxHeight = 120; // 最大高度
        const newHeight = Math.min(textarea.scrollHeight, maxHeight);
        textarea.style.height = newHeight + 'px';
    }

    updateTextSelection() {
        const editor = document.getElementById('editorContent');
        if (!editor) return;

        const selection = window.getSelection();
        const selectedText = selection.toString().trim();
        
        if (selectedText && selectedText.length > 0) {
            this.selectedText = selectedText;
            this.showSelectedTextProcessor(selectedText);
        } else {
            this.selectedText = '';
            this.hideSelectedTextProcessor();
        }
    }

    showSelectedTextProcessor(text) {
        const processor = document.getElementById('selectedTextProcessor');
        const preview = document.getElementById('selectedTextPreview');
        const lengthSpan = document.getElementById('selectedTextLength');
        
        if (processor && preview && lengthSpan) {
            preview.textContent = text;
            lengthSpan.textContent = `${text.length} 字符`;
            processor.style.display = 'block';
            
            // 切换到AI面板
            if (this.app.uiManager) {
                this.app.uiManager.switchRightPanel('ai');
            }
        }
    }

    hideSelectedTextProcessor() {
        const processor = document.getElementById('selectedTextProcessor');
        if (processor) {
            processor.style.display = 'none';
        }
        this.selectedText = '';
    }

    processSelectedTextWithAction(action) {
        if (!this.selectedText) {
            CoreUtils.showNotification('请先选择要处理的文本', 'warning');
            return;
        }

        // 动作映射
        const actionMap = {
            'rewrite': '重写',
            'improve': '改进',
            'summarize': '总结',
            'expand': '扩写',
            'translate': '翻译',
            'correct': '纠错'
        };

        const actionName = actionMap[action] || action;
        
        // 构建提示词
        const prompts = {
            'rewrite': `请重写以下文本，保持原意但使用不同的表达方式：\n\n${this.selectedText}`,
            'improve': `请改进以下文本，使其更加清晰、准确和优雅：\n\n${this.selectedText}`,
            'summarize': `请总结以下文本的要点：\n\n${this.selectedText}`,
            'expand': `请扩写以下文本，添加更多细节和说明：\n\n${this.selectedText}`,
            'translate': `请将以下文本翻译成中文（如果是中文则翻译成英文）：\n\n${this.selectedText}`,
            'correct': `请纠正以下文本中的错误（语法、拼写、标点等）：\n\n${this.selectedText}`
        };

        const prompt = prompts[action] || `请${actionName}以下文本：\n\n${this.selectedText}`;
        
        // 发送到AI
        this.sendAIMessageWithPrompt(prompt, actionName);
    }

    async sendAIMessageWithPrompt(prompt, actionName) {
        // 添加用户消息到历史
        const userMessage = {
            role: 'user',
            content: `${actionName}选中的文本`,
            originalPrompt: prompt,
            selectedText: this.selectedText,
            timestamp: Date.now(),
            isQuickAction: true
        };
        
        this.aiHistory.push(userMessage);
        
        // 隐藏选中文本处理器
        this.hideSelectedTextProcessor();
        
        // 渲染历史记录
        this.renderAIHistory();
        
        // 手动启动流式处理
        this.handleAIStreamStart();
        
        try {
            // 发送消息到AI服务
            const response = await ipcRenderer.invoke('ai-chat-stream', prompt, this.aiHistory);
            
            if (!response.success) {
                this.handleAIStreamError(response.error);
                return;
            }
            
        } catch (error) {
            console.error('AI处理失败:', error);
            this.handleAIStreamError(error.message);
        }
    }

    async sendAIMessage() {
        const input = document.getElementById('aiInput');
        if (!input) return;
        
        const message = input.value.trim();
        if (!message) return;
        
        // 构建用户消息对象
        const userMessage = {
            role: 'user',
            content: message,
            timestamp: Date.now()
        };
        
        // 检查是否有选中的文本
        let finalMessage = message;
        if (this.selectedText && this.selectedText.trim()) {
            finalMessage = `${message}\n\n选中的文本：\n${this.selectedText}`;
            userMessage.selectedText = this.selectedText;
            userMessage.selectedTextRange = this.selectedTextRange;
        }
        
        // 添加用户消息到历史
        this.aiHistory.push(userMessage);
        
        // 清空输入框和选中文本状态
        input.value = '';
        this.clearSelectedTextState();
        
        // 渲染历史记录
        this.renderAIHistory();
        
        // 手动启动流式处理
        this.handleAIStreamStart();
        
        try {
            // 发送消息到AI服务
            const response = await ipcRenderer.invoke('ai-chat-stream', finalMessage, this.aiHistory);
            
            if (!response.success) {
                this.handleAIStreamError(response.error);
                return;
            }
            
            // 流式响应将通过事件处理
            
        } catch (error) {
            console.error('AI聊天失败:', error);
            this.handleAIStreamError(error.message);
        }
    }

    handleAIStreamStart(data = null) {
        this.isAIStreaming = true;
        this.currentAIMessage = {
            role: 'assistant',
            content: '',
            timestamp: Date.now()
        };
        this.currentAIContent = '';
        
        // 添加到历史记录
        this.aiHistory.push(this.currentAIMessage);
        
        // 渲染历史记录
        this.renderAIHistory();
        
        // 显示加载状态
        this.showAILoading();
    }

    handleAIStreamChunk(data) {
        if (!this.isAIStreaming || !this.currentAIMessage) return;
        
        this.currentAIContent += data.chunk;
        this.currentAIMessage.content = this.currentAIContent;
        
        // 节流更新UI
        if (this.streamUpdateThrottle) {
            clearTimeout(this.streamUpdateThrottle);
        }
        
        this.streamUpdateThrottle = setTimeout(() => {
            this.updateCurrentAIMessage();
        }, 100);
    }

    handleAIStreamEnd(data) {
        this.isAIStreaming = false;
        
        if (this.currentAIMessage) {
            this.currentAIMessage.content = this.currentAIContent;
        }
        
        // 最终更新UI
        this.updateCurrentAIMessage();
        this.hideAILoading();
        
        // 清理状态
        this.currentAIMessage = null;
        this.currentAIContent = '';
        
        // 限制历史记录长度
        if (this.aiHistory.length > 20) {
            this.aiHistory = this.aiHistory.slice(-20);
        }
    }

    handleAIStreamError(error) {
        this.isAIStreaming = false;
        this.hideAILoading();
        
        // 移除未完成的消息
        if (this.currentAIMessage) {
            const index = this.aiHistory.indexOf(this.currentAIMessage);
            if (index > -1) {
                this.aiHistory.splice(index, 1);
            }
        }
        
        // 显示错误消息
        this.aiHistory.push({
            role: 'system',
            content: '错误: ' + error,
            timestamp: Date.now()
        });
        
        this.renderAIHistory();
        CoreUtils.showNotification('AI响应失败: ' + error, 'error');
        
        // 清理状态
        this.currentAIMessage = null;
        this.currentAIContent = '';
    }

    renderAIHistory() {
        const container = document.getElementById('aiChatHistory');
        if (!container) return;
        
        if (this.aiHistory.length === 0) {
            container.innerHTML = `
                <div class="ai-welcome">
                    <h3>AI 助手</h3>
                    <p>你好！我是你的AI写作助手。你可以：</p>
                    <ul>
                        <li>与我对话，获取写作建议</li>
                        <li>选择文本后使用右侧的处理按钮</li>
                        <li>让我帮你改进、总结或扩展内容</li>
                    </ul>
                </div>
            `;
            return;
        }
        
        const html = this.aiHistory.map(message => this.renderAIMessage(message)).join('');
        container.innerHTML = html;
        
        // 滚动到底部
        container.scrollTop = container.scrollHeight;
    }

    renderAIMessage(message) {
        const isUser = message.role === 'user';
        const isSystem = message.role === 'system';
        const isAssistant = message.role === 'assistant';
        const className = isUser ? 'user' : isSystem ? 'system' : 'assistant';
        
        let contentHtml = '';
        let headerHtml = '';
        
        if (isUser) {
            headerHtml = `
                <div class="message-header">
                    <span class="message-role">你</span>
                    <span class="message-time">${CoreUtils.formatTime(message.timestamp)}</span>
                </div>
            `;
            
            if (message.isQuickAction) {
                // 快捷操作消息
                contentHtml = `
                    <div class="message-content">
                        ${this.formatMessageContent(message.content)}
                        <div class="selected-text-quote">
                            "${message.selectedText.substring(0, 100)}${message.selectedText.length > 100 ? '...' : ''}"
                        </div>
                    </div>
                `;
            } else {
                // 普通用户消息
                contentHtml = `<div class="message-content">${this.formatMessageContent(message.content)}</div>`;
            }
        } else if (isAssistant) {
            headerHtml = `
                <div class="message-header">
                    <span class="message-role">AI助手</span>
                    <span class="message-time">${CoreUtils.formatTime(message.timestamp)}</span>
                </div>
            `;
            
            // 生成唯一ID
            const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            // AI助手消息
            contentHtml = `
                <div class="message-content">${this.formatMessageContent(message.content)}</div>
                <div class="message-actions">
                    <button class="action-btn copy-btn" data-action="copy" data-content="${this.escapeHtml(message.content)}" title="复制">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                        </svg>
                        复制
                    </button>
                    <button class="action-btn insert-btn" data-action="insert" data-content="${this.escapeHtml(message.content)}" title="插入到光标位置">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                        </svg>
                        插入
                    </button>
                    <button class="action-btn replace-btn" data-action="replace" data-content="${this.escapeHtml(message.content)}" title="替换选中文本">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z"/>
                        </svg>
                        替换
                    </button>
                    <button class="action-btn regenerate-btn" data-action="regenerate" data-message-index="${this.aiHistory.indexOf(message)}" title="重新生成">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                        </svg>
                        重新生成
                    </button>
                </div>
            `;
        } else {
            // 系统消息
            headerHtml = `
                <div class="message-header">
                    <span class="message-role">系统</span>
                    <span class="message-time">${CoreUtils.formatTime(message.timestamp)}</span>
                </div>
            `;
            contentHtml = `<div class="message-content">${this.formatMessageContent(message.content)}</div>`;
        }
        
        return `
            <div class="ai-message ${className}">
                ${headerHtml}
                ${contentHtml}
            </div>
        `;
    }

    formatMessageContent(content) {
        if (!content) return '';
        
        // 简单的Markdown格式化
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    escapeForJs(str) {
        if (!str) return '';
        return str
            .replace(/\\/g, '\\\\')
            .replace(/`/g, '\\`')
            .replace(/\$/g, '\\$')
            .replace(/\n/g, '\\n')
            .replace(/\r/g, '\\r');
    }

    escapeHtml(str) {
        if (!str) return '';
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    }

    updateCurrentAIMessage() {
        if (!this.currentAIMessage) return;
        
        const container = document.getElementById('aiChatHistory');
        if (!container) return;
        
        // 查找最后一条AI消息
        const messageElements = container.querySelectorAll('.ai-message.assistant');
        const lastMessage = messageElements[messageElements.length - 1];
        
        if (lastMessage) {
            const contentElement = lastMessage.querySelector('.message-content');
            if (contentElement) {
                contentElement.innerHTML = this.formatMessageContent(this.currentAIMessage.content);
            }
            
            // 确保操作按钮存在
            let actionsElement = lastMessage.querySelector('.message-actions');
            if (!actionsElement && this.currentAIMessage.content.trim()) {
                // 如果没有操作按钮且有内容，则添加操作按钮
                actionsElement = document.createElement('div');
                actionsElement.className = 'message-actions';
                actionsElement.innerHTML = `
                    <button class="action-btn copy-btn" data-action="copy" data-content="${this.escapeHtml(this.currentAIMessage.content)}" title="复制">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                        </svg>
                        复制
                    </button>
                    <button class="action-btn insert-btn" data-action="insert" data-content="${this.escapeHtml(this.currentAIMessage.content)}" title="插入到光标位置">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                        </svg>
                        插入
                    </button>
                    <button class="action-btn replace-btn" data-action="replace" data-content="${this.escapeHtml(this.currentAIMessage.content)}" title="替换选中文本">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z"/>
                        </svg>
                        替换
                    </button>
                    <button class="action-btn regenerate-btn" data-action="regenerate" data-message-index="${this.aiHistory.indexOf(this.currentAIMessage)}" title="重新生成">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                        </svg>
                        重新生成
                    </button>
                `;
                lastMessage.appendChild(actionsElement);
            } else if (actionsElement) {
                // 更新现有按钮的data-content属性
                const buttons = actionsElement.querySelectorAll('.action-btn');
                buttons.forEach(btn => {
                    const action = btn.getAttribute('data-action');
                    if (action === 'copy' || action === 'insert' || action === 'replace') {
                        btn.setAttribute('data-content', this.escapeHtml(this.currentAIMessage.content));
                    }
                });
            }
        }
        
        // 滚动到底部
        container.scrollTop = container.scrollHeight;
    }

    showAILoading() {
        const container = document.getElementById('aiChatHistory');
        if (!container) return;
        
        const loadingElement = document.createElement('div');
        loadingElement.className = 'ai-loading';
        loadingElement.innerHTML = `
            <div class="ai-message assistant">
                <div class="message-header">
                    <span class="message-role">AI</span>
                    <span class="message-time">正在输入...</span>
                </div>
                <div class="message-content">
                    <div class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        `;
        
        container.appendChild(loadingElement);
        container.scrollTop = container.scrollHeight;
    }

    hideAILoading() {
        const loadingElement = document.querySelector('.ai-loading');
        if (loadingElement) {
            loadingElement.remove();
        }
    }

    clearAIHistory() {
        const confirmed = confirm('确定要清空AI对话历史吗？');
        if (!confirmed) return;
        
        this.aiHistory = [];
        this.renderAIHistory();
        CoreUtils.showNotification('AI对话历史已清空', 'success');
    }

    copyToClipboard(text) {
        if (!text) {
            CoreUtils.showNotification('没有内容可复制', 'warning');
            return;
        }
        
        // 尝试使用现代 Clipboard API
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text).then(() => {
                CoreUtils.showNotification('已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                this.fallbackCopyToClipboard(text);
            });
        } else {
            // 回退到传统方法
            this.fallbackCopyToClipboard(text);
        }
    }
    
    fallbackCopyToClipboard(text) {
        try {
            // 创建临时textarea元素
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';
            textarea.style.opacity = '0';
            textarea.style.left = '-9999px';
            document.body.appendChild(textarea);
            
            // 选择并复制
            textarea.select();
            textarea.setSelectionRange(0, 99999);
            const successful = document.execCommand('copy');
            
            // 清理
            document.body.removeChild(textarea);
            
            if (successful) {
                CoreUtils.showNotification('已复制到剪贴板', 'success');
            } else {
                CoreUtils.showNotification('复制失败，请手动复制', 'error');
            }
        } catch (err) {
            console.error('复制失败:', err);
            CoreUtils.showNotification('复制失败: ' + err.message, 'error');
        }
    }

    insertToEditor(text) {
        if (!text) {
            CoreUtils.showNotification('没有内容可插入', 'warning');
            return;
        }
        
        // 尝试多种方式找到编辑器
        let editor = document.getElementById('editorContent');
        if (!editor) {
            editor = document.getElementById('markdownEditor');
        }
        if (!editor) {
            editor = document.querySelector('.markdown-editor');
        }
        if (!editor) {
            editor = document.querySelector('textarea');
        }
        
        if (!editor) {
            CoreUtils.showNotification('编辑器未找到', 'error');
            return;
        }

        try {
            if (editor.tagName === 'TEXTAREA' || editor.tagName === 'INPUT') {
                // 处理textarea和input元素
                const startPos = editor.selectionStart;
                const endPos = editor.selectionEnd;
                const beforeText = editor.value.substring(0, startPos);
                const afterText = editor.value.substring(endPos);
                
                editor.value = beforeText + text + afterText;
                editor.selectionStart = editor.selectionEnd = startPos + text.length;
                editor.focus();
                
                // 触发input事件以便其他组件知道内容已更改
                editor.dispatchEvent(new Event('input', { bubbles: true }));
                
            } else {
                // 处理contenteditable元素
                const selection = window.getSelection();
                const range = selection.getRangeAt(0);
                
                // 插入文本
                const textNode = document.createTextNode(text);
                range.insertNode(textNode);
                
                // 移动光标到插入文本的末尾
                range.setStartAfter(textNode);
                range.setEndAfter(textNode);
                selection.removeAllRanges();
                selection.addRange(range);
                
                editor.focus();
            }
            
            CoreUtils.showNotification('已插入到编辑器', 'success');
            
        } catch (error) {
            console.error('插入失败:', error);
            CoreUtils.showNotification('插入失败: ' + error.message, 'error');
        }
    }

    replaceSelectedText(text) {
        if (!text) {
            CoreUtils.showNotification('没有内容可替换', 'warning');
            return;
        }
        
        // 尝试多种方式找到编辑器
        let editor = document.getElementById('editorContent');
        if (!editor) {
            editor = document.getElementById('markdownEditor');
        }
        if (!editor) {
            editor = document.querySelector('.markdown-editor');
        }
        if (!editor) {
            editor = document.querySelector('textarea');
        }
        
        if (!editor) {
            CoreUtils.showNotification('编辑器未找到', 'error');
            return;
        }

        try {
            if (editor.tagName === 'TEXTAREA' || editor.tagName === 'INPUT') {
                // 处理textarea和input元素
                const startPos = editor.selectionStart;
                const endPos = editor.selectionEnd;
                
                if (startPos === endPos) {
                    CoreUtils.showNotification('请先选择要替换的文本', 'warning');
                    return;
                }
                
                const beforeText = editor.value.substring(0, startPos);
                const afterText = editor.value.substring(endPos);
                
                editor.value = beforeText + text + afterText;
                editor.selectionStart = startPos;
                editor.selectionEnd = startPos + text.length;
                editor.focus();
                
                // 触发input事件以便其他组件知道内容已更改
                editor.dispatchEvent(new Event('input', { bubbles: true }));
                
            } else {
                // 处理contenteditable元素
                const selection = window.getSelection();
                
                if (selection.rangeCount === 0) {
                    CoreUtils.showNotification('请先选择要替换的文本', 'warning');
                    return;
                }
                
                const range = selection.getRangeAt(0);
                
                if (range.collapsed) {
                    CoreUtils.showNotification('请先选择要替换的文本', 'warning');
                    return;
                }
                
                // 删除选中的内容
                range.deleteContents();
                
                // 插入新文本
                const textNode = document.createTextNode(text);
                range.insertNode(textNode);
                
                // 选中新插入的文本
                range.setStartBefore(textNode);
                range.setEndAfter(textNode);
                selection.removeAllRanges();
                selection.addRange(range);
                
                editor.focus();
            }
            
            CoreUtils.showNotification('已替换选中文本', 'success');
            
        } catch (error) {
            console.error('替换失败:', error);
            CoreUtils.showNotification('替换失败: ' + error.message, 'error');
        }
    }

    regenerateMessage(messageIndex) {
        if (messageIndex < 0 || messageIndex >= this.aiHistory.length) return;
        
        const message = this.aiHistory[messageIndex];
        if (message.role !== 'assistant') return;
        
        // 找到对应的用户消息
        const userMessage = this.aiHistory[messageIndex - 1];
        if (!userMessage || userMessage.role !== 'user') return;
        
        // 移除当前AI消息
        this.aiHistory.splice(messageIndex, 1);
        
        // 重新发送用户消息
        const prompt = userMessage.originalPrompt || userMessage.content;
        this.sendAIMessageWithPrompt(prompt, '重新生成');
    }

    updateTextProcessButtons() {
        const editor = document.getElementById('markdownEditor');
        if (!editor) return;
        
        const hasSelection = editor.selectionStart !== editor.selectionEnd;
        const buttons = [
            'improveBtn', 'summarizeBtn', 'expandBtn', 
            'translateBtn', 'rewriteBtn', 'explainBtn'
        ];
        
        buttons.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.disabled = !hasSelection;
            }
        });
    }

    async processSelectedText(action) {
        const editor = document.getElementById('markdownEditor');
        if (!editor) return;
        
        const selectedText = editor.value.substring(editor.selectionStart, editor.selectionEnd);
        if (!selectedText.trim()) {
            CoreUtils.showNotification('请先选择要处理的文本', 'warning');
            return;
        }
        
        this.selectedText = selectedText;
        
        // 动作映射
        const actionMap = {
            'improve': '改进',
            'summarize': '总结',
            'expand': '扩展',
            'translate': '翻译',
            'rewrite': '重写',
            'explain': '解释'
        };
        
        const actionName = actionMap[action] || action;
        
        // 创建处理按钮数据
        const buttonData = {
            action: action,
            actionName: actionName,
            originalText: selectedText,
            selectionStart: editor.selectionStart,
            selectionEnd: editor.selectionEnd
        };
        
        // 将按钮数据存储到全局变量中
        window.currentTextProcessingData = buttonData;
        
        try {
            // 调用AI文本处理服务（支持流式输出）
            const response = await ipcRenderer.invoke('ai-process-text-stream', selectedText, action);
            
            if (!response.success) {
                this.handleTextProcessStreamError(response.error);
                return;
            }
            
            // 保存到AI历史记录
            this.aiHistory.push({ 
                role: 'user', 
                content: `请${actionName}以下内容：\n${selectedText}`,
                timestamp: Date.now()
            });
            
            // 限制历史记录长度
            if (this.aiHistory.length > 20) {
                this.aiHistory = this.aiHistory.slice(-20);
            }
            
        } catch (error) {
            console.error('文本处理失败:', error);
            this.handleTextProcessStreamError(error.message);
        }
    }

    handleTextProcessStreamStart(data) {
        this.isAIStreaming = true;
        this.currentAIMessage = {
            role: 'assistant',
            content: '',
            timestamp: Date.now()
        };
        this.currentAIContent = '';
        
        // 添加到历史记录
        this.aiHistory.push(this.currentAIMessage);
        
        // 切换到AI面板
        this.app.uiManager.switchRightPanel('ai');
        
        // 渲染历史记录
        this.renderAIHistory();
        
        // 显示加载状态
        this.showAILoading();
    }

    handleTextProcessStreamChunk(data) {
        if (!this.isAIStreaming || !this.currentAIMessage) return;
        
        this.currentAIContent += data.chunk;
        this.currentAIMessage.content = this.currentAIContent;
        
        // 节流更新UI
        if (this.streamUpdateThrottle) {
            clearTimeout(this.streamUpdateThrottle);
        }
        
        this.streamUpdateThrottle = setTimeout(() => {
            this.updateCurrentAIMessage();
        }, 100);
    }

    handleTextProcessStreamEnd(data) {
        this.isAIStreaming = false;
        
        if (this.currentAIMessage) {
            this.currentAIMessage.content = this.currentAIContent;
        }
        
        // 最终更新UI
        this.updateCurrentAIMessage();
        this.hideAILoading();
        
        // 显示应用按钮
        this.showApplyTextButton();
        
        // 清理状态
        this.currentAIMessage = null;
        this.currentAIContent = '';
    }

    handleTextProcessStreamError(error) {
        this.isAIStreaming = false;
        this.hideAILoading();
        
        // 移除未完成的消息
        if (this.currentAIMessage) {
            const index = this.aiHistory.indexOf(this.currentAIMessage);
            if (index > -1) {
                this.aiHistory.splice(index, 1);
            }
        }
        
        // 显示错误消息
        this.aiHistory.push({
            role: 'system',
            content: '错误: ' + error,
            timestamp: Date.now()
        });
        
        this.renderAIHistory();
        CoreUtils.showNotification('文本处理失败: ' + error, 'error');
        
        // 清理状态
        this.currentAIMessage = null;
        this.currentAIContent = '';
    }

    showApplyTextButton() {
        const container = document.getElementById('aiHistory');
        if (!container) return;
        
        const messageElements = container.querySelectorAll('.ai-message.assistant');
        const lastMessage = messageElements[messageElements.length - 1];
        
        if (lastMessage && window.currentTextProcessingData) {
            const contentElement = lastMessage.querySelector('.message-content');
            if (contentElement) {
                contentElement.innerHTML += `
                    <div class="apply-text-actions">
                        <button class="btn btn-primary" onclick="organizationApp.aiManager.applyProcessedText()">
                            应用到编辑器
                        </button>
                        <button class="btn btn-secondary" onclick="organizationApp.aiManager.insertProcessedText()">
                            插入到光标位置
                        </button>
                    </div>
                `;
            }
        }
    }

    applyProcessedText() {
        if (!window.currentTextProcessingData || !this.currentAIContent) return;
        
        const editor = document.getElementById('markdownEditor');
        if (!editor) return;
        
        const data = window.currentTextProcessingData;
        const processedText = this.currentAIContent;
        
        // 替换选中的文本
        const beforeText = editor.value.substring(0, data.selectionStart);
        const afterText = editor.value.substring(data.selectionEnd);
        
        editor.value = beforeText + processedText + afterText;
        editor.selectionStart = data.selectionStart;
        editor.selectionEnd = data.selectionStart + processedText.length;
        
        // 触发输入事件
        editor.dispatchEvent(new Event('input'));
        editor.focus();
        
        // 清理数据
        window.currentTextProcessingData = null;
        
        CoreUtils.showNotification('文本已应用到编辑器', 'success');
    }

    insertProcessedText() {
        if (!this.currentAIContent) return;
        
        this.app.uiManager.insertAtCursor(this.currentAIContent);
        
        // 清理数据
        window.currentTextProcessingData = null;
        
        CoreUtils.showNotification('文本已插入到光标位置', 'success');
    }

    // 获取选中的文本
    getSelectedText() {
        const editor = document.getElementById('markdownEditor');
        if (!editor) return '';
        
        return editor.value.substring(editor.selectionStart, editor.selectionEnd);
    }

    // 更新选中文本指示器
    updateSelectionIndicator() {
        const editor = document.getElementById('markdownEditor');
        const aiInput = document.getElementById('aiInput');
        const selectedTextHint = document.getElementById('selectedTextHint');
        const selectedTextLength = document.getElementById('selectedTextLength');
        
        if (!editor || !aiInput) return;
        
        const selectedText = editor.value.substring(editor.selectionStart, editor.selectionEnd);
        this.selectedText = selectedText;
        this.selectedTextRange = selectedText ? {
            start: editor.selectionStart,
            end: editor.selectionEnd
        } : null;
        
        if (selectedText && selectedText.trim()) {
            // 有选中文本，更新占位符和提示
            aiInput.placeholder = `输入问题讨论选中的文本（${selectedText.length} 个字符）...`;
            aiInput.style.borderColor = '#667eea';
            
            // 显示选中文本提示区
            if (selectedTextHint) {
                selectedTextHint.style.display = 'block';
                
                // 更新字符数显示
                if (selectedTextLength) {
                    selectedTextLength.textContent = selectedText.length;
                }
                
                // 确保AI面板是激活的
                this.app.uiManager.switchRightPanel('ai');
            }
        } else {
            // 没有选中文本，恢复默认状态
            aiInput.placeholder = '输入您的问题或指令...（可选中文本进行针对性对话）';
            aiInput.style.borderColor = '';
            
            // 隐藏选中文本提示区
            if (selectedTextHint) {
                selectedTextHint.style.display = 'none';
            }
        }
    }

    // 隐藏选中文本处理器
    hideSelectedTextProcessor() {
        const selectedTextProcessor = document.getElementById('selectedTextProcessor');
        if (selectedTextProcessor) {
            selectedTextProcessor.style.display = 'none';
        }
        
        // 清除编辑器选择
        const editor = document.getElementById('markdownEditor');
        if (editor) {
            editor.selectionStart = editor.selectionEnd = editor.value.length;
            editor.focus();
        }
        
        this.updateSelectionIndicator();
    }

    // 监听编辑器选择变化
    handleEditorSelection() {
        this.updateTextProcessButtons();
    }

    // 发送选中文本聊天
    async sendSelectedTextChat() {
        const input = document.getElementById('selectedTextChatInput');
        if (!input || !this.selectedText) return;
        
        const message = input.value.trim();
        if (!message) return;
        
        // 构建完整的消息
        const fullMessage = `请针对以下文本回答我的问题：\n\n文本内容：\n${this.selectedText}\n\n我的问题：\n${message}`;
        
        // 添加到选中文本聊天历史
        this.selectedTextChatHistory.push({
            role: 'user',
            content: message,
            timestamp: Date.now()
        });
        
        // 清空输入框
        input.value = '';
        
        // 渲染聊天历史
        this.renderSelectedTextChatHistory();
        
        try {
            // 发送消息到AI服务
            const response = await ipcRenderer.invoke('ai-chat-stream', fullMessage, []);
            
            if (!response.success) {
                this.handleSelectedTextChatError(response.error);
                return;
            }
            
            // 添加AI响应到历史
            this.selectedTextChatHistory.push({
                role: 'assistant',
                content: response.content || '',
                timestamp: Date.now()
            });
            
            this.renderSelectedTextChatHistory();
            
        } catch (error) {
            console.error('选中文本聊天失败:', error);
            this.handleSelectedTextChatError(error.message);
        }
    }

    // 渲染选中文本聊天历史
    renderSelectedTextChatHistory() {
        const container = document.getElementById('selectedTextChatHistory');
        if (!container) return;
        
        if (this.selectedTextChatHistory.length === 0) {
            container.innerHTML = '<div class="empty-chat">开始针对选中文本进行对话...</div>';
            return;
        }
        
        const html = this.selectedTextChatHistory.map(message => {
            const messageClass = message.role === 'user' ? 'user' : 'ai';
            return `
                <div class="chat-message ${messageClass}">
                    <div class="chat-message-content">${this.formatMessageContent(message.content)}</div>
                    ${message.role === 'assistant' ? this.renderMessageActions(message.content) : ''}
                </div>
            `;
        }).join('');
        
        container.innerHTML = html;
        container.scrollTop = container.scrollHeight;
    }

    // 渲染消息操作按钮
    renderMessageActions(content) {
        return `
            <div class="chat-message-actions">
                <button onclick="organizationApp.aiManager.copyToClipboard('${this.escapeHtml(content)}')">复制</button>
                <button onclick="organizationApp.aiManager.insertToEditor('${this.escapeHtml(content)}')">插入</button>
                <button onclick="organizationApp.aiManager.replaceSelectedText('${this.escapeHtml(content)}')">替换</button>
            </div>
        `;
    }

    // 复制到剪贴板
    async copyToClipboard(content) {
        try {
            await navigator.clipboard.writeText(content);
            CoreUtils.showNotification('已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            CoreUtils.showNotification('复制失败', 'error');
        }
    }

    // 插入到编辑器
    insertToEditor(content) {
        const editor = document.getElementById('markdownEditor');
        if (!editor) return;
        
        const cursorPos = editor.selectionStart;
        const beforeText = editor.value.substring(0, cursorPos);
        const afterText = editor.value.substring(editor.selectionEnd);
        
        let insertText = content;
        if (beforeText && !beforeText.endsWith('\n')) {
            insertText = '\n' + insertText;
        }
        if (afterText && !afterText.startsWith('\n')) {
            insertText = insertText + '\n';
        }
        
        editor.value = beforeText + insertText + afterText;
        editor.selectionStart = editor.selectionEnd = cursorPos + insertText.length;
        
        editor.dispatchEvent(new Event('input'));
        editor.focus();
        
        CoreUtils.showNotification('已插入到编辑器', 'success');
    }

    // 替换选中文本
    replaceSelectedText(content) {
        const editor = document.getElementById('markdownEditor');
        if (!editor) {
            CoreUtils.showNotification('找不到编辑器', 'error');
            return;
        }
        
        // 获取当前选中的文本
        const currentSelection = editor.value.substring(editor.selectionStart, editor.selectionEnd);
        
        if (!currentSelection) {
            CoreUtils.showNotification('请先选择要替换的文本', 'warning');
            return;
        }
        
        const beforeText = editor.value.substring(0, editor.selectionStart);
        const afterText = editor.value.substring(editor.selectionEnd);
        
        editor.value = beforeText + content + afterText;
        editor.selectionStart = beforeText.length;
        editor.selectionEnd = beforeText.length + content.length;
        
        editor.dispatchEvent(new Event('input'));
        editor.focus();
        
        CoreUtils.showNotification('已替换选中文本', 'success');
    }



    // 清除选中文本状态
    clearSelectedTextState() {
        this.selectedText = '';
        this.selectedTextRange = null;
        
        // 更新UI
        const aiInput = document.getElementById('aiInput');
        const selectedTextHint = document.getElementById('selectedTextHint');
        
        if (aiInput) {
            aiInput.placeholder = '输入您的问题或指令...（可选中文本进行针对性对话）';
            aiInput.style.borderColor = '';
        }
        
        if (selectedTextHint) {
            selectedTextHint.style.display = 'none';
        }
    }

    // 使用特定操作处理选中文本
    async processSelectedTextWithAction(action) {
        if (!this.selectedText) {
            CoreUtils.showNotification('请先选择要处理的文本', 'warning');
            return;
        }
        
        const prompts = {
            rewrite: '请重写以下文本，使其更加清晰和流畅：',
            expand: '请扩展以下文本，添加更多细节和解释：',
            summarize: '请总结以下文本的主要内容：',
            improve: '请改进以下文本的表达和结构：',
            translate: '请将以下文本翻译成中文：',
            correct: '请纠正以下文本中的错误：'
        };
        
        const actionNames = {
            rewrite: '重写',
            expand: '扩展',
            summarize: '总结',
            improve: '改进',
            translate: '翻译',
            correct: '纠错'
        };
        
        const prompt = prompts[action];
        const actionName = actionNames[action];
        
        if (!prompt) return;
        
        const message = `${prompt}\n\n${this.selectedText}`;
        
        // 构建用户消息对象
        const userMessage = {
            role: 'user',
            content: `${actionName}文本`,
            selectedText: this.selectedText,
            selectedTextRange: this.selectedTextRange,
            isQuickAction: true,
            actionType: action,
            timestamp: Date.now()
        };
        
        // 添加到主对话历史
        this.aiHistory.push(userMessage);
        
        // 清除选中文本状态
        this.clearSelectedTextState();
        
        // 渲染历史记录
        this.renderAIHistory();
        
        try {
            const response = await ipcRenderer.invoke('ai-chat-stream', message, this.aiHistory);
            
            if (!response.success) {
                this.handleAIStreamError(response.error);
                return;
            }
            
            // 流式响应将通过事件处理
            
        } catch (error) {
            console.error('文本处理失败:', error);
            this.handleAIStreamError(error.message);
        }
    }

    // 格式化消息内容
    formatMessageContent(content) {
        return content.replace(/\n/g, '<br>');
    }

    // 转义HTML
    escapeHtml(text) {
        return text.replace(/'/g, "\\'").replace(/"/g, '\\"').replace(/\n/g, '\\n');
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIManager;
} else {
    window.AIManager = AIManager;
}
