// 简单的 Markdown 编辑器实现
class MarkdownEditor {
    constructor(editorElement, previewElement, options = {}) {
        this.editor = editorElement;
        this.preview = previewElement;
        this.content = '';
        
        // 拖拽相关状态
        this.isDragOver = false;
        this.dropPosition = null;
        this.insertionIndicator = null;
        this.rafId = null;
        this.dragEventsBound = false;
        this._canvas = null;
        
        // 配置选项
        this.enableRealtimeCursor = options.enableRealtimeCursor === true; // 默认禁用
        this.enableInsertionIndicator = options.enableInsertionIndicator !== false; // 默认启用
        this.debounceDelay = options.debounceDelay || 16; // 60fps
        
        this.init();
    }

    init() {
        // 设置编辑器样式
        this.editor.style.fontFamily = 'SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace';
        this.editor.style.fontSize = '14px';
        this.editor.style.lineHeight = '1.6';
        this.editor.style.padding = '16px';
        this.editor.style.border = 'none';
        this.editor.style.outline = 'none';
        this.editor.style.resize = 'none';
        this.editor.style.width = '100%';
        this.editor.style.height = '100%';
        this.editor.style.backgroundColor = 'transparent';
        this.editor.style.position = 'relative';

        // 绑定事件
        this.editor.addEventListener('input', () => this.handleInput());
        this.editor.addEventListener('keydown', (e) => this.handleKeydown(e));
        this.editor.addEventListener('scroll', () => this.syncScroll());

        // 暂时禁用拖拽事件来排除干扰
        // this.bindDragEvents();

        // 设置初始内容
        this.setContent('# 开始编写你的文档\n\n在这里输入 Markdown 内容...');
        
        // 创建插入指示器
        this.createInsertionIndicator();
    }

    // 绑定拖拽事件
    bindDragEvents() {
        // 防止重复绑定
        if (this.dragEventsBound) return;
        this.dragEventsBound = true;
        
        // 创建插入指示器
        this.createInsertionIndicator();
        
        // 绑定拖拽事件
        this.editor.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.handleDragOver(e);
        });
        
        this.editor.addEventListener('dragleave', (e) => {
            this.handleDragLeave(e);
        });
        
        this.editor.addEventListener('drop', (e) => {
            e.preventDefault();
            this.handleDrop(e);
        });
        
        // 添加实时光标跟随
        this.editor.addEventListener('dragenter', (e) => {
            e.preventDefault();
            this.isDragOver = true;
            this.editor.classList.add('drag-over');
        });
    }

    // 处理拖拽悬停 - 重写以支持实时光标跟随
    handleDragOver(e) {
        if (!this.isDragOver) {
            this.isDragOver = true;
            this.editor.classList.add('drag-over');
        }
        
        // 使用requestAnimationFrame优化性能
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
        }
        
        this.rafId = requestAnimationFrame(() => {
            // 计算精确的光标位置
            const position = this.calculatePreciseCursorPosition(e);
            this.dropPosition = position;
            
            // 显示插入指示器
            this.showInsertionIndicator(position);
            
            // 只在实际拖拽操作期间更新光标位置，避免干扰正常点击
            // 检查是否有拖拽数据传输
            if (this.enableRealtimeCursor && e.dataTransfer && e.dataTransfer.types.length > 0) {
                this.editor.setSelectionRange(position, position);
            }
        });
    }

    // 处理拖拽离开
    handleDragLeave(e) {
        // 检查是否真的离开了编辑器区域
        const rect = this.editor.getBoundingClientRect();
        const isOutside = e.clientX < rect.left || e.clientX > rect.right || 
                         e.clientY < rect.top || e.clientY > rect.bottom;
        
        if (isOutside) {
            this.isDragOver = false;
            this.dropPosition = null;
            this.editor.classList.remove('drag-over');
            this.hideInsertionIndicator();
            
            if (this.rafId) {
                cancelAnimationFrame(this.rafId);
                this.rafId = null;
            }
        }
    }

    // 处理放置
    handleDrop(e) {
        console.log('处理拖拽放置事件');
        
        // 获取拖拽数据
        const materialData = e.dataTransfer.getData('application/x-material');
        const materialId = e.dataTransfer.getData('text/plain');
        
        console.log('拖拽数据:', { materialData, materialId });
        console.log('主应用实例:', window.organizationApp);
        console.log('主应用材料数量:', window.organizationApp?.materials?.length);
        
        let contentToInsert = '';
        
        if (materialData) {
            try {
                const material = JSON.parse(materialData);
                contentToInsert = this.formatMaterialContent(material);
                console.log('从素材数据格式化内容:', material.type, contentToInsert);
            } catch (error) {
                console.error('解析拖拽数据失败:', error);
                // 如果解析失败，尝试从主应用获取素材数据
                if (materialId && window.organizationApp && window.organizationApp.materials) {
                    const material = window.organizationApp.materials.find(m => m.id === materialId);
                    if (material) {
                        contentToInsert = this.formatMaterialContent(material);
                        console.log('从主应用获取素材数据:', material.type, contentToInsert);
                    } else {
                        console.error('未找到素材ID:', materialId);
                    }
                }
            }
        } else if (materialId && window.organizationApp && window.organizationApp.materials) {
            // 直接通过ID获取素材
            const material = window.organizationApp.materials.find(m => m.id === materialId);
            if (material) {
                contentToInsert = this.formatMaterialContent(material);
                console.log('通过ID获取素材数据:', material.type, contentToInsert);
            } else {
                console.error('未找到素材ID:', materialId);
            }
        } else {
            console.error('无法获取拖拽数据或主应用实例');
        }
        
        // 重新计算最终的插入位置（确保精确）
        const finalPosition = this.calculatePreciseCursorPosition(e);
        
        if (contentToInsert && finalPosition !== null) {
            this.insertTextAtPosition(contentToInsert, finalPosition);
            
            // 触发自定义事件通知主应用
            this.editor.dispatchEvent(new CustomEvent('materialInserted', {
                detail: { content: contentToInsert, position: finalPosition }
            }));
            
            console.log('素材插入成功:', contentToInsert);
        } else {
            console.error('插入失败:', { contentToInsert, finalPosition });
        }
        
        // 清理状态
        this.handleDragLeave({ clientX: -1, clientY: -1 }); // 强制清理
    }

    // 格式化素材内容
    formatMaterialContent(material) {
        if (!material) return '';
        
        const content = material.content || '';
        const title = material.title || this.generateMaterialTitle(material);
        
        switch (material.type) {
            case 'text':
                return content;
            case 'image':
                // 检查是否是图片URL或base64数据
                if (content.startsWith('data:image/') || content.startsWith('http')) {
                    return `![${title}](${content})`;
                }
                return content; // 如果不是图片数据，直接返回文本内容
            case 'link':
                // 检查是否是有效的URL
                try {
                    new URL(content);
                    return `[${title}](${content})`;
                } catch {
                    return content; // 如果不是有效URL，直接返回文本内容
                }
            case 'code':
                return `\`\`\`\n${content}\n\`\`\``;
            default:
                return content || title || '';
        }
    }

    // 生成素材标题
    generateMaterialTitle(material) {
        if (!material || !material.content) return '未命名素材';
        
        const firstLine = material.content.split('\n')[0].trim();
        return firstLine.length > 50 ? firstLine.substring(0, 50) + '...' : firstLine;
    }

    // 精确计算光标位置 - 全新实现
    calculatePreciseCursorPosition(e) {
        const rect = this.editor.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // 方法1: 使用现代浏览器API
        if (document.caretPositionFromPoint) {
            try {
                const caretPos = document.caretPositionFromPoint(e.clientX, e.clientY);
                if (caretPos && caretPos.offsetNode) {
                    const textNode = caretPos.offsetNode;
                    if (textNode === this.editor || this.editor.contains(textNode)) {
                        // 计算在textarea中的位置
                        const range = document.createRange();
                        range.setStart(textNode, caretPos.offset);
                        range.setEnd(textNode, caretPos.offset);
                        
                        // 对于textarea，需要特殊处理
                        if (textNode === this.editor) {
                            return this.getTextPositionFromCoordinates(x, y);
                        }
                    }
                }
            } catch (error) {
                console.log('caretPositionFromPoint失败，使用备用方法');
            }
        }
        
        // 方法2: 使用caretRangeFromPoint (Webkit)
        if (document.caretRangeFromPoint) {
            try {
                const range = document.caretRangeFromPoint(e.clientX, e.clientY);
                if (range && range.startContainer) {
                    const container = range.startContainer;
                    if (container === this.editor || this.editor.contains(container)) {
                        return this.getTextPositionFromCoordinates(x, y);
                    }
                }
            } catch (error) {
                console.log('caretRangeFromPoint失败，使用备用方法');
            }
        }
        
        // 方法3: 手动精确计算
        return this.getTextPositionFromCoordinates(x, y);
    }

    // 从坐标获取文本位置 - 优化版本
    getTextPositionFromCoordinates(x, y) {
        const style = getComputedStyle(this.editor);
        const paddingLeft = parseInt(style.paddingLeft) || 0;
        const paddingTop = parseInt(style.paddingTop) || 0;
        const fontSize = parseFloat(style.fontSize) || 14;
        const lineHeight = parseFloat(style.lineHeight) || fontSize * 1.4;
        
        // 调整坐标（减去padding）
        const adjustedX = Math.max(0, x - paddingLeft);
        const adjustedY = Math.max(0, y - paddingTop);
        
        // 计算行号
        const lineNumber = Math.floor(adjustedY / lineHeight);
        
        // 获取文本内容
        const lines = this.editor.value.split('\n');
        const totalLines = lines.length;
        
        // 边界检查
        if (lineNumber >= totalLines) {
            return this.editor.value.length; // 最后位置
        }
        
        if (lineNumber < 0) {
            return 0; // 第一个位置
        }
        
        // 获取当前行内容
        const currentLine = lines[lineNumber];
        
        // 使用更精确的字符位置计算
        const charPosition = this.getCharPositionInLine(currentLine, adjustedX);
        
        // 计算在整个文本中的位置
        let position = 0;
        for (let i = 0; i < lineNumber; i++) {
            position += lines[i].length + 1; // +1 for \n
        }
        position += Math.min(charPosition, currentLine.length);
        
        return position;
    }

    // 在行内获取字符位置 - 新的精确方法
    getCharPositionInLine(line, x) {
        if (!line || x <= 0) return 0;
        
        // 使用canvas测量精确字符宽度
        const canvas = this.getCanvas();
        const ctx = canvas.getContext('2d');
        
        // 设置字体样式
        const style = getComputedStyle(this.editor);
        ctx.font = `${style.fontSize} ${style.fontFamily}`;
        
        let currentWidth = 0;
        let charIndex = 0;
        
        // 逐字符测量宽度
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            const charWidth = ctx.measureText(char).width;
            
            // 如果当前位置超过了目标x坐标的一半，就是这个位置
            if (currentWidth + charWidth / 2 > x) {
                break;
            }
            
            currentWidth += charWidth;
            charIndex = i + 1;
        }
        
        return charIndex;
    }

    // 获取或创建canvas（用于精确测量）
    getCanvas() {
        if (!this._canvas) {
            this._canvas = document.createElement('canvas');
            this._canvas.style.position = 'absolute';
            this._canvas.style.visibility = 'hidden';
            this._canvas.style.pointerEvents = 'none';
            document.body.appendChild(this._canvas);
        }
        return this._canvas;
    }

    // 创建插入指示器
    createInsertionIndicator() {
        if (this.insertionIndicator || !this.enableInsertionIndicator) return;
        
        this.insertionIndicator = document.createElement('div');
        this.insertionIndicator.className = 'markdown-insertion-indicator';
        this.insertionIndicator.style.cssText = `
            position: fixed;
            width: 2px;
            height: 20px;
            background-color: #007acc;
            z-index: 1000;
            display: none;
            pointer-events: none;
            border-radius: 1px;
            box-shadow: 0 0 4px rgba(0, 122, 204, 0.5);
        `;
        
        // 添加CSS动画
        if (!document.getElementById('insertion-indicator-style')) {
            const style = document.createElement('style');
            style.id = 'insertion-indicator-style';
            style.textContent = `
                @keyframes blink {
                    0%, 50% { opacity: 1; }
                    51%, 100% { opacity: 0.3; }
                }
                
                .markdown-insertion-indicator {
                    animation: blink 1s infinite;
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(this.insertionIndicator);
    }

    // 显示插入位置指示器 - 优化版本
    showInsertionIndicator(position) {
        if (!this.insertionIndicator) return;
        
        const coordinates = this.getCoordinatesFromPosition(position);
        if (!coordinates) return;
        
        const rect = this.editor.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
        
        this.insertionIndicator.style.left = `${rect.left + coordinates.x + scrollLeft}px`;
        this.insertionIndicator.style.top = `${rect.top + coordinates.y + scrollTop}px`;
        this.insertionIndicator.style.display = 'block';
        
        // 添加动画效果
        this.insertionIndicator.style.animation = 'none';
        this.insertionIndicator.offsetHeight; // 强制重绘
        this.insertionIndicator.style.animation = 'blink 1s infinite';
    }

    // 隐藏插入指示器
    hideInsertionIndicator() {
        if (this.insertionIndicator) {
            this.insertionIndicator.style.display = 'none';
        }
    }

    // 从文本位置获取屏幕坐标 - 简化版本
    getCoordinatesFromPosition(position) {
        const style = getComputedStyle(this.editor);
        const paddingLeft = parseInt(style.paddingLeft) || 0;
        const paddingTop = parseInt(style.paddingTop) || 0;
        const fontSize = parseFloat(style.fontSize) || 14;
        const lineHeight = parseFloat(style.lineHeight) || fontSize * 1.4;
        
        // 分析文本到指定位置
        const textBeforePosition = this.editor.value.substring(0, position);
        const lines = textBeforePosition.split('\n');
        const currentLineIndex = lines.length - 1;
        const currentLine = lines[currentLineIndex];
        
        // 计算Y坐标
        const y = paddingTop + currentLineIndex * lineHeight;
        
        // 计算X坐标
        let x = paddingLeft;
        if (currentLine) {
            const canvas = this.getCanvas();
            const ctx = canvas.getContext('2d');
            ctx.font = `${style.fontSize} ${style.fontFamily}`;
            x += ctx.measureText(currentLine).width;
        }
        
        return { x, y };
    }

    // 在指定位置插入文本
    insertTextAtPosition(text, position) {
        const currentValue = this.editor.value;
        
        // 构建新的文本内容
        const beforeText = currentValue.substring(0, position);
        const afterText = currentValue.substring(position);
        
        // 检查是否需要添加换行
        let insertText = text;
        if (beforeText && !beforeText.endsWith('\n') && !beforeText.endsWith('\n\n')) {
            insertText = '\n' + insertText;
        }
        if (afterText && !afterText.startsWith('\n') && !insertText.endsWith('\n')) {
            insertText = insertText + '\n';
        }
        
        const newValue = beforeText + insertText + afterText;
        
        // 更新内容
        this.editor.value = newValue;
        this.content = newValue;
        
        // 设置光标位置到插入文本的末尾
        const newCursorPosition = position + insertText.length;
        this.editor.setSelectionRange(newCursorPosition, newCursorPosition);
        this.editor.focus();
        
        // 触发更新
        this.handleInput();
    }

    // 销毁编辑器
    destroy() {
        if (this.insertionIndicator && this.insertionIndicator.parentNode) {
            this.insertionIndicator.parentNode.removeChild(this.insertionIndicator);
            this.insertionIndicator = null;
        }
        
        if (this._canvas && this._canvas.parentNode) {
            this._canvas.parentNode.removeChild(this._canvas);
            this._canvas = null;
        }
        
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
            this.rafId = null;
        }
        
        // 清理状态
        this.isDragOver = false;
        this.dropPosition = null;
        this.dragEventsBound = false;
    }

    handleInput() {
        this.content = this.editor.value;
        this.updatePreview();
        this.updateWordCount();
        
        // 触发变化事件
        this.editor.dispatchEvent(new CustomEvent('contentChanged', {
            detail: { content: this.content }
        }));
    }

    handleKeydown(e) {
        // Tab 键插入缩进
        if (e.key === 'Tab') {
            e.preventDefault();
            this.insertAtCursor('  ');
        }
        
        // Ctrl/Cmd + B 加粗
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            this.wrapSelection('**', '**');
        }
        
        // Ctrl/Cmd + I 斜体
        if ((e.ctrlKey || e.metaKey) && e.key === 'i') {
            e.preventDefault();
            this.wrapSelection('*', '*');
        }
        
        // Ctrl/Cmd + K 链接
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.insertLink();
        }
    }

    insertAtCursor(text) {
        const start = this.editor.selectionStart;
        const end = this.editor.selectionEnd;
        const content = this.editor.value;
        
        this.editor.value = content.substring(0, start) + text + content.substring(end);
        this.editor.selectionStart = this.editor.selectionEnd = start + text.length;
        
        this.handleInput();
        this.editor.focus();
    }

    wrapSelection(before, after) {
        const start = this.editor.selectionStart;
        const end = this.editor.selectionEnd;
        const content = this.editor.value;
        const selectedText = content.substring(start, end);
        
        if (selectedText) {
            const wrappedText = before + selectedText + after;
            this.editor.value = content.substring(0, start) + wrappedText + content.substring(end);
            this.editor.selectionStart = start + before.length;
            this.editor.selectionEnd = end + before.length;
        } else {
            this.insertAtCursor(before + after);
            this.editor.selectionStart = this.editor.selectionEnd = start + before.length;
        }
        
        this.handleInput();
        this.editor.focus();
    }

    insertLink() {
        this.showInputModal('插入链接', '请输入链接地址:', '', (url) => {
            if (url) {
                this.showInputModal('链接文本', '请输入链接文本:', url, (text) => {
                    this.insertAtCursor(`[${text || url}](${url})`);
                });
            }
        });
    }

    insertImage() {
        this.showInputModal('插入图片', '请输入图片地址:', '', (url) => {
            if (url) {
                this.showInputModal('图片描述', '请输入图片描述:', '图片', (alt) => {
                    this.insertAtCursor(`![${alt || '图片'}](${url})`);
                });
            }
        });
    }

    showInputModal(title, message, defaultValue = '', callback) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal-overlay" id="inputModalOverlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close" id="inputModalClose">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                        <input type="text" id="inputModalInput" value="${defaultValue}" placeholder="请输入...">
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" id="inputModalCancel">取消</button>
                        <button class="btn btn-primary" id="inputModalConfirm">确定</button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const overlay = document.getElementById('inputModalOverlay');
        const input = document.getElementById('inputModalInput');
        const confirmBtn = document.getElementById('inputModalConfirm');
        const cancelBtn = document.getElementById('inputModalCancel');
        const closeBtn = document.getElementById('inputModalClose');

        // 聚焦输入框并选中文本
        input.focus();
        input.select();

        // 确定按钮事件
        const handleConfirm = () => {
            const value = input.value.trim();
            overlay.remove();
            if (callback) {
                callback(value);
            }
        };

        // 取消按钮事件
        const handleCancel = () => {
            overlay.remove();
        };

        // 绑定事件
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        closeBtn.addEventListener('click', handleCancel);

        // 回车确认，ESC取消
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleConfirm();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                handleCancel();
            }
        });

        // 点击遮罩层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                handleCancel();
            }
        });
    }

    setContent(content) {
        this.editor.value = content;
        this.content = content;
        this.updatePreview();
        this.updateWordCount();
    }

    getContent() {
        return this.content;
    }

    updatePreview() {
        if (!this.preview) return;
        
        try {
            // 简单的 Markdown 转 HTML
            const html = this.markdownToHtml(this.content);
            this.preview.innerHTML = html;
        } catch (error) {
            console.error('预览更新失败:', error);
            this.preview.innerHTML = '<p>预览加载失败</p>';
        }
    }

    markdownToHtml(markdown) {
        // 简单的 Markdown 解析器
        let html = markdown
            // 标题
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
            
            // 粗体和斜体
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            
            // 链接
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
            
            // 图片
            .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" style="max-width: 100%;">')
            
            // 代码块
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            
            // 列表
            .replace(/^\* (.*$)/gm, '<li>$1</li>')
            .replace(/^- (.*$)/gm, '<li>$1</li>')
            
            // 换行
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>');
        
        // 包装段落
        html = '<p>' + html + '</p>';
        
        // 处理列表
        html = html.replace(/(<li>.*<\/li>)/g, '<ul>$1</ul>');
        
        // 清理空段落
        html = html.replace(/<p><\/p>/g, '');
        html = html.replace(/<p><br><\/p>/g, '');
        
        return html;
    }

    updateWordCount() {
        const content = this.content.trim();
        const wordCount = content ? content.split(/\s+/).length : 0;
        const charCount = content.length;
        
        // 更新状态栏
        const wordCountEl = document.getElementById('wordCount');
        const charCountEl = document.getElementById('charCount');
        
        if (wordCountEl) wordCountEl.textContent = `${wordCount} 字`;
        if (charCountEl) charCountEl.textContent = `${charCount} 字符`;
    }

    syncScroll() {
        if (!this.preview) return;
        
        const scrollRatio = this.editor.scrollTop / (this.editor.scrollHeight - this.editor.clientHeight);
        this.preview.scrollTop = scrollRatio * (this.preview.scrollHeight - this.preview.clientHeight);
    }

    // 工具栏方法
    formatBold() {
        this.wrapSelection('**', '**');
    }

    formatItalic() {
        this.wrapSelection('*', '*');
    }

    formatCode() {
        this.wrapSelection('`', '`');
    }

    formatCodeBlock() {
        this.wrapSelection('```\n', '\n```');
    }

    insertHeading(level) {
        const prefix = '#'.repeat(level) + ' ';
        this.insertAtCursor(prefix);
    }

    insertList() {
        this.insertAtCursor('- ');
    }

    insertOrderedList() {
        this.insertAtCursor('1. ');
    }

    insertQuote() {
        this.insertAtCursor('> ');
    }

    insertHorizontalRule() {
        this.insertAtCursor('\n---\n');
    }

    // 查找和替换
    find(searchText) {
        const content = this.editor.value;
        const index = content.indexOf(searchText);
        
        if (index !== -1) {
            this.editor.selectionStart = index;
            this.editor.selectionEnd = index + searchText.length;
            this.editor.focus();
            return true;
        }
        
        return false;
    }

    replace(searchText, replaceText) {
        const content = this.editor.value;
        const newContent = content.replace(searchText, replaceText);
        this.setContent(newContent);
    }

    replaceAll(searchText, replaceText) {
        const content = this.editor.value;
        const newContent = content.replace(new RegExp(searchText, 'g'), replaceText);
        this.setContent(newContent);
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MarkdownEditor;
} else {
    window.MarkdownEditor = MarkdownEditor;
}