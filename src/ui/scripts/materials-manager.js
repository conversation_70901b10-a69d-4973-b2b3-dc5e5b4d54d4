class MaterialsManager {
    constructor(app) {
        this.app = app;
        this.materials = [];
        this.selectedMaterials = new Set();
        this.lastSelectedMaterial = null;
        this.searchQuery = '';
        this.searchTimeout = null;


        // 文件夹管理相关
        this.openFolders = new Set();
        this.currentFolder = 'root';
        this.isRendering = false;
        
        // 延迟绑定事件，确保DOM已加载
        setTimeout(() => this.bindEvents(), 0);
    }

    bindEvents() {
        // 批量操作按钮
        document.getElementById('selectAllBtn')?.addEventListener('click', () => this.selectAllMaterials());
        document.getElementById('batchInsertBtn')?.addEventListener('click', () => this.insertSelectedMaterials());
        document.getElementById('batchDeleteBtn')?.addEventListener('click', () => this.batchDeleteMaterials());



        // 初始化搜索功能
        this.initializeSearch();
    }

    async loadMaterials() {
        try {
            CoreUtils.showElementLoading('materialsList');

            // 检查是否在Electron环境中
            if (!ipcRenderer) {
                console.warn('Not in Electron environment, using mock data');
                this.materials = this.getMockMaterials();
                this.app.materials = this.materials;
                this.app.collections = this.materials;
                // 先清除加载状态，再渲染
                CoreUtils.hideElementLoading('materialsList');
                this.renderMaterials();
                if (this.app && this.app.uiManager) {
                    this.app.uiManager.updateStatusBar();
                }
                return;
            }

            const options = {
                orderBy: 'created_at',
                order: 'DESC'
            };



            if (this.searchQuery) {
                options.search = this.searchQuery;
            }

            const result = await ipcRenderer.invoke('get-collections', options);

            // --- BEGIN FIX ---
            console.log('IPC "get-collections" raw result:', result);
            if (!Array.isArray(result)) {
                console.error('Received non-array response for collections:', result);
                throw new Error('从主进程收到的数据格式不正确。');
            }
            // --- END FIX ---

            this.materials = result;
            this.app.materials = this.materials;
            this.app.collections = this.materials;

            // 先清除加载状态，再渲染
            CoreUtils.hideElementLoading('materialsList');
            this.renderMaterials();
            if (this.app && this.app.uiManager) {
                this.app.uiManager.updateStatusBar();
            }

        } catch (error) {
            console.error('加载素材失败:', error);
            this.materials = [];
            this.app.materials = this.materials;
            this.app.collections = this.materials;
            // 先清除加载状态，再渲染
            CoreUtils.hideElementLoading('materialsList');
            this.renderMaterials();
            CoreUtils.showNotification('加载素材失败: ' + error.message, 'error');
        }
    }

    getMockMaterials() {
        return [
            {
                id: 'mock-1',
                content: '这是第一个测试素材的内容',
                type: 'text',
                category: '测试',
                folder: 'root',
                created_at: new Date().toISOString(),
                keywords: 'test'
            },
            {
                id: 'mock-2',
                content: '这是第二个测试素材的内容',
                type: 'text',
                category: '测试',
                folder: 'root',
                created_at: new Date().toISOString(),
                keywords: 'test'
            },
            {
                id: 'mock-3',
                content: 'https://picsum.photos/300/200?random=1',
                type: 'image',
                category: '测试',
                folder: 'root',
                created_at: new Date().toISOString(),
                keywords: 'test image'
            },
            {
                id: 'mock-4',
                content: 'https://example.com',
                type: 'link',
                category: '测试',
                folder: 'root',
                created_at: new Date().toISOString(),
                keywords: 'test link'
            },
            {
                id: 'mock-5',
                content: '这是文件夹中的素材',
                type: 'text',
                category: '测试',
                folder: '测试文件夹',
                created_at: new Date().toISOString(),
                keywords: 'test'
            },
            {
                id: 'folder-config-1',
                content: '# 测试文件夹\n\n这是一个文件夹配置文件，请勿删除。',
                type: 'text',
                category: '系统',
                folder: '测试文件夹',
                created_at: new Date().toISOString(),
                keywords: '.folder_config'
            }
        ];
    }

    renderMaterials() {
        // 防止重复渲染
        if (this.isRendering) {
            return;
        }

        this.isRendering = true;

        try {
            const container = document.getElementById('materialsList');
            if (!container) {
                console.error('materialsList容器不存在');
                this.isRendering = false;
                return;
            }

            console.log('开始渲染素材，数量:', this.materials ? this.materials.length : 0);

            if (!this.materials || this.materials.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>暂无素材</h3>
                        <p>点击"添加素材"按钮或使用快捷键创建第一个素材</p>
                    </div>
                `;
                console.log('显示空状态');
                this.isRendering = false;
                return;
            }

            // 按文件夹分组素材
            const groupedMaterials = this.groupMaterialsByFolder();
            console.log('分组后的素材:', groupedMaterials);

            // 渲染文件夹和素材
            const html = this.renderMaterialsWithFolders(groupedMaterials);

            container.innerHTML = html;

            this.bindMaterialEvents();
            this.bindFolderEvents();
            this.updateBatchActions();

            console.log('素材渲染完成');
        } finally {
            this.isRendering = false;
        }
    }

    renderMaterialItem(material) {
        // --- BEGIN FIX ---
        // 彻底重写，确保代码健壮性，对所有可能为null的字段进行检查
        if (!material || !material.id) return '';

        const isSelected = this.selectedMaterials.has(material.id);
        
        const type = material.type || 'text';
        const content = material.content || '';
        const category = material.category || '';
        const createdAt = material.created_at ? CoreUtils.formatTime(material.created_at) : '未知时间';
        const title = content.substring(0, 80) + (content.length > 80 ? '...' : '');

        const preview = CoreUtils.generateContentPreview(content, type);
        const typeIcon = CoreUtils.getTypeIcon(type);
        const typeDisplayName = this.getTypeDisplayName(type);
        // --- END FIX ---

        return `
            <div class="material-item ${isSelected ? 'selected' : ''}"
                 data-id="${material.id}"
                 data-type="${type}"
                 draggable="true">
                <div class="material-header">
                    <div class="material-checkbox">
                        <input type="checkbox" ${isSelected ? 'checked' : ''}
                               onchange="organizationApp.materialsManager.toggleMaterialSelection('${material.id}', event)">
                    </div>
                    <div class="material-title" title="${CoreUtils.escapeHtml(content)}">
                        ${CoreUtils.escapeHtml(title) || '未命名素材'}
                    </div>
                    <div class="material-actions">
                        <button class="btn-icon" onclick="organizationApp.materialsManager.insertMaterial('${material.id}')" title="插入">
                            ${CoreUtils.getIcon('add')}
                        </button>
                        <button class="btn-icon" onclick="organizationApp.materialsManager.editMaterial('${material.id}')" title="编辑">
                            ${CoreUtils.getIcon('edit')}
                        </button>
                        <button class="btn-icon" onclick="organizationApp.materialsManager.deleteMaterial('${material.id}')" title="删除">
                            ${CoreUtils.getIcon('delete')}
                        </button>
                    </div>
                </div>
                <div class="material-content">
                    <div class="material-preview">
                        ${preview}
                    </div>
                </div>
                <div class="material-meta">
                    <span class="material-type-info" title="${typeDisplayName}">
                        ${typeIcon} ${typeDisplayName}
                    </span>
                    <span class="material-time">${createdAt}</span>
                </div>
            </div>
        `;
    }

    bindMaterialEvents() {
        const materialItems = document.querySelectorAll('.material-item');
        
        materialItems.forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.closest('.material-checkbox') || e.target.closest('.material-actions')) {
                    return;
                }
                this.selectMaterial(item.dataset.id, e);
            });
            
            item.addEventListener('dblclick', (e) => {
                e.preventDefault();
                this.insertMaterial(item.dataset.id);
            });
            
            item.addEventListener('contextmenu', (e) => {
                this.showMaterialContextMenu(e, item.dataset.id);
            });

            // 拖拽事件
            this.bindMaterialDragEvents(item);
        });
    }

    selectMaterial(id, event) {
        if (event && event.shiftKey && this.lastSelectedMaterial) {
            // Shift + 点击：范围选择
            const materials = this.materials;
            const lastIndex = materials.findIndex(m => m.id === this.lastSelectedMaterial);
            const currentIndex = materials.findIndex(m => m.id === id);

            if (lastIndex !== -1 && currentIndex !== -1) {
                const start = Math.min(lastIndex, currentIndex);
                const end = Math.max(lastIndex, currentIndex);

                for (let i = start; i <= end; i++) {
                    this.selectedMaterials.add(materials[i].id);
                }
            }
        } else {
            // 普通点击：直接多选模式（切换选中状态）
            if (this.selectedMaterials.has(id)) {
                this.selectedMaterials.delete(id);
                if (this.selectedMaterials.size === 0) {
                    this.lastSelectedMaterial = null;
                }
            } else {
                this.selectedMaterials.add(id);
                this.lastSelectedMaterial = id;
            }
        }

        this.renderMaterials();
    }

    toggleMaterialSelection(id, event) {
        if (event) {
            event.stopPropagation();
        }
        
        if (this.selectedMaterials.has(id)) {
            this.selectedMaterials.delete(id);
        } else {
            this.selectedMaterials.add(id);
        }
        
        this.lastSelectedMaterial = id;
        this.updateBatchActions();
        
        const checkbox = document.querySelector(`[data-id="${id}"] input[type="checkbox"]`);
        if (checkbox) {
            checkbox.checked = this.selectedMaterials.has(id);
        }
        
        const item = document.querySelector(`[data-id="${id}"]`);
        if (item) {
            item.classList.toggle('selected', this.selectedMaterials.has(id));
        }
    }

    updateBatchActions() {
        const selectedCount = this.selectedMaterials.size;
        const batchActions = document.getElementById('batchActions');
        const selectAllBtn = document.getElementById('selectAllBtn');
        const batchInsertBtn = document.getElementById('batchInsertBtn');
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        
        if (batchActions) {
            if (selectedCount > 0) {
                batchActions.style.display = 'flex';
            } else {
                batchActions.style.display = 'none';
            }
            
            if (selectAllBtn) {
                selectAllBtn.textContent = selectedCount === this.materials.length ? '取消全选' : '全选';
            }
            
            if (batchInsertBtn) {
                batchInsertBtn.textContent = `插入 (${selectedCount})`;
            }
            
            if (batchDeleteBtn) {
                batchDeleteBtn.textContent = `删除 (${selectedCount})`;
            }
        }
    }

    selectAllMaterials() {
        if (this.selectedMaterials.size === this.materials.length) {
            this.selectedMaterials.clear();
        } else {
            this.selectedMaterials.clear();
            this.materials.forEach(material => {
                this.selectedMaterials.add(material.id);
            });
        }
        
        this.renderMaterials();
    }

    async insertSelectedMaterials() {
        if (this.selectedMaterials.size === 0) {
            CoreUtils.showNotification('请先选择要插入的素材', 'warning');
            return;
        }
        
        const textarea = document.getElementById('markdownEditor');
        if (!textarea) return;
        
        const selectedMaterials = this.materials.filter(material => 
            this.selectedMaterials.has(material.id)
        );
        
        let insertText = '';
        selectedMaterials.forEach((material, index) => {
            switch (material.type) {
                case 'text':
                    insertText += material.content;
                    break;
                case 'image':
                    if (material.content.startsWith('data:image/') || material.content.startsWith('http')) {
                        insertText += `![${material.title || '图片'}](${material.content})`;
                    } else {
                        insertText += material.content;
                    }
                    break;
                case 'link':
                    insertText += `[${material.title || '链接'}](${material.content})`;
                    break;
                case 'code':
                    insertText += `\`\`\`\n${material.content}\n\`\`\``;
                    break;
                default:
                    insertText += material.content;
            }
            
            if (index < selectedMaterials.length - 1) {
                insertText += '\n\n';
            }
        });
        
        const cursorPos = textarea.selectionStart;
        const beforeText = textarea.value.substring(0, cursorPos);
        const afterText = textarea.value.substring(textarea.selectionEnd);
        
        if (beforeText && !beforeText.endsWith('\n')) {
            insertText = '\n' + insertText;
        }
        
        if (afterText && !afterText.startsWith('\n')) {
            insertText = insertText + '\n';
        }
        
        textarea.value = beforeText + insertText + afterText;
        textarea.selectionStart = textarea.selectionEnd = cursorPos + insertText.length;
        
        textarea.dispatchEvent(new Event('input'));
        textarea.focus();
        
        this.selectedMaterials.clear();
        this.renderMaterials();
        
        CoreUtils.showNotification(`已插入 ${selectedMaterials.length} 个素材`, 'success');
    }

    async batchDeleteMaterials() {
        if (this.selectedMaterials.size === 0) {
            CoreUtils.showNotification('请先选择要删除的素材', 'warning');
            return;
        }
        
        const confirmed = confirm(`确定要删除选中的 ${this.selectedMaterials.size} 个素材吗？此操作不可撤销。`);
        if (!confirmed) return;
        
        try {
            for (const id of this.selectedMaterials) {
                await ipcRenderer.invoke('delete-collection', id);
            }
            
            this.selectedMaterials.clear();
            await this.loadMaterials();
            CoreUtils.showNotification('批量删除成功', 'success');
            
        } catch (error) {
            console.error('批量删除失败:', error);
            CoreUtils.showNotification('批量删除失败: ' + error.message, 'error');
        }
    }

    async insertMaterial(id) {
        const material = this.materials.find(m => m.id === id);
        if (!material) return;
        
        let insertText = '';
        switch (material.type) {
            case 'text':
                insertText = material.content;
                break;
            case 'image':
                if (material.content.startsWith('data:image/') || material.content.startsWith('http')) {
                    insertText = `![${material.title || '图片'}](${material.content})`;
                } else {
                    insertText = material.content;
                }
                break;
            case 'link':
                insertText = `[${material.title || '链接'}](${material.content})`;
                break;
            case 'code':
                insertText = `\`\`\`\n${material.content}\n\`\`\``;
                break;
            default:
                insertText = material.content;
        }
        
        this.app.uiManager.insertAtCursor(insertText);
        CoreUtils.showNotification('素材已插入', 'success');
    }

    async editMaterial(id) {
        const material = this.materials.find(m => m.id === id);
        if (!material) {
            CoreUtils.showNotification('找不到要编辑的素材', 'error');
            return;
        }
        
        // 创建编辑对话框
        const modalTitle = '编辑素材';
        const modalBody = `
            <form id="editMaterialForm" style="display: flex; flex-direction: column; gap: 16px;">
                <!-- 内容输入区域 -->
                <div class="form-group">
                    <label for="editMaterialContent" style="display: block; margin-bottom: 6px; font-weight: 500; color: #333; font-size: 14px;">内容:</label>
                    <textarea
                        id="editMaterialContent"
                        rows="6"
                        required
                        style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; line-height: 1.4; resize: vertical; font-family: inherit; box-sizing: border-box;"
                        placeholder="输入素材内容..."
                    >${material.content || ''}</textarea>
                </div>

                <!-- 标签输入区域 - 完全参考quick-capture -->
                <div class="form-group">
                    <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333; font-size: 14px;">标签:</label>
                    <div class="tag-input-container" style="border: 1px solid #ddd; border-radius: 6px; padding: 8px; background: white; box-sizing: border-box;">
                        <div class="tag-list" id="tagList" style="display: flex; flex-wrap: wrap; gap: 6px; margin-bottom: 6px; min-height: 20px;"></div>
                        <input
                            type="text"
                            id="sourceInput"
                            placeholder="输入新标签或点击下方标签"
                            style="border: none; outline: none; width: 100%; padding: 4px; font-size: 14px; box-sizing: border-box;"
                            autocomplete="off"
                        >
                        <div class="tag-suggestions" id="tagSuggestions" style="display: none; position: absolute; z-index: 1000; background: white; border: 1px solid #ddd; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); max-height: 150px; overflow-y: auto;"></div>
                    </div>
                    <div class="recent-tags" id="recentTags" style="margin-top: 8px; display: flex; flex-wrap: wrap; gap: 6px;"></div>
                </div>

                <div class="form-actions" style="display: flex; justify-content: flex-end; gap: 12px; padding-top: 16px; border-top: 1px solid #eee; margin-top: 8px;">
                    <button type="button" class="btn-secondary" onclick="organizationApp.uiManager.closeModal()" style="padding: 8px 20px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer; font-size: 14px;">取消</button>
                    <button type="submit" class="btn-primary" style="padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">保存</button>
                </div>
            </form>
        `;
        
        // 显示模态框
        const fullModalHtml = `
            <div class="modal-content" style="max-width: 1200px; margin: 30px auto; background: white; border-radius: 8px; padding: 24px;">
                <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 15px;">
                    <h3 style="margin: 0; color: #333;">${modalTitle}</h3>
                    <button onclick="organizationApp.uiManager.closeModal()" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #666;">&times;</button>
                </div>
                <div class="modal-body">
                    ${modalBody}
                </div>
            </div>
        `;
        this.app.uiManager.showModal(fullModalHtml);

        // 初始化标签（异步）
        this.initEditTags(material.keywords).then(() => {
            // 绑定标签输入事件
            this.bindEditTagEvents();
        }).catch(error => {
            console.error('初始化标签失败:', error);
            // 即使失败也要绑定事件
            this.bindEditTagEvents();
        });

        // 绑定表单提交事件
        const form = document.getElementById('editMaterialForm');
        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const content = document.getElementById('editMaterialContent').value;

            if (!content.trim()) {
                CoreUtils.showNotification('内容不能为空', 'error');
                return;
            }

            // 自动检测类型
            let type = 'text';
            if (this.isValidUrl(content.trim())) {
                type = this.isImageUrl(content.trim()) ? 'image' : 'link';
            }

            try {
                const updateData = {
                    type,
                    content: content.trim(),
                    keywords: this.tags.length > 0 ? this.tags.join(', ') : null
                };

                console.log('正在更新素材:', id, updateData);

                let updatedMaterial;
                if (ipcRenderer) {
                    updatedMaterial = await ipcRenderer.invoke('update-collection', id, updateData);
                } else {
                    // 浏览器环境模拟更新
                    const index = this.materials.findIndex(m => m.id === id);
                    if (index !== -1) {
                        updatedMaterial = { ...this.materials[index], ...updateData };
                        this.materials[index] = updatedMaterial;
                    }
                }
                console.log('更新结果:', updatedMaterial);

                if (updatedMaterial) {
                    // 更新本地数据
                    const index = this.materials.findIndex(m => m.id === id);
                    if (index !== -1) {
                        this.materials[index] = updatedMaterial;
                        this.app.materials = this.materials;
                        this.app.collections = this.materials;
                    }

                    // 重新渲染素材列表
                    this.renderMaterials();
                    this.app.uiManager.updateStatusBar();

                    // 关闭模态框
                    this.app.uiManager.closeModal();

                    CoreUtils.showNotification('素材已更新', 'success');
                } else {
                    throw new Error('更新操作失败');
                }

            } catch (error) {
                console.error('更新素材失败:', error);
                CoreUtils.showNotification('更新失败: ' + error.message, 'error');
            }
        });
    }

    async deleteMaterial(id) {
        const confirmed = confirm('确定要删除这个素材吗？此操作不可撤销。');
        if (!confirmed) return;

        try {
            console.log('正在删除素材:', id);

            let deleteResult = true;
            if (ipcRenderer) {
                deleteResult = await ipcRenderer.invoke('delete-collection', id);
            } else {
                console.warn('Mock delete in browser environment');
            }
            console.log('删除结果:', deleteResult);

            if (deleteResult) {
                // 从本地数组中移除已删除的素材
                const originalLength = this.materials.length;
                console.log('删除前的素材列表:', this.materials.map(m => ({ id: m.id, content: m.content.substring(0, 30), folder: m.folder })));

                this.materials = this.materials.filter(material => material.id !== id);
                console.log(`删除前素材数量: ${originalLength}, 删除后素材数量: ${this.materials.length}`);
                console.log('删除后的素材列表:', this.materials.map(m => ({ id: m.id, content: m.content.substring(0, 30), folder: m.folder })));

                this.app.materials = this.materials;
                this.app.collections = this.materials;

                // 清除选中状态
                this.selectedMaterials.delete(id);

                // 重新渲染素材列表
                console.log('开始重新渲染素材列表...');
                this.renderMaterials();
                this.app.uiManager.updateStatusBar();

                CoreUtils.showNotification('素材已删除', 'success');
            } else {
                throw new Error('删除操作失败');
            }

        } catch (error) {
            console.error('删除素材失败:', error);
            CoreUtils.showNotification('删除失败: ' + error.message, 'error');
            // 如果删除失败，重新加载素材以确保数据一致性
            await this.loadMaterials();
        }
    }

    openQuickCapture() {
        // 调用主进程的快速收藏功能
        if (ipcRenderer) {
            ipcRenderer.invoke('show-quick-capture');
        } else {
            console.warn('快速收藏功能需要在Electron环境中使用');
            CoreUtils.showNotification('快速收藏功能需要在Electron环境中使用', 'warning');
        }
    }

    toggleMaterialsSearch() {
        const container = document.getElementById('materialsSearchContainer');
        const input = document.getElementById('materialsSearchInput');
        
        if (container && input) {
            if (container.style.display === 'none') {
                container.style.display = 'block';
                input.focus();
            } else {
                container.style.display = 'none';
                this.closeMaterialsSearch();
            }
        }
    }

    handleMaterialsSearch(query) {
        this.searchQuery = query;
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.loadMaterials();
        }, 300);
    }



    closeMaterialsSearch() {
        const container = document.getElementById('materialsSearchContainer');
        const input = document.getElementById('materialsSearchInput');
        
        if (container && input) {
            container.style.display = 'none';
            input.value = '';
            this.searchQuery = '';
            this.loadMaterials();
        }
    }

    showMaterialContextMenu(e, materialId) {
        const items = [
            {
                icon: CoreUtils.getIcon('add'),
                label: '插入',
                action: `organizationApp.materialsManager.insertMaterial('${materialId}')`
            },
            {
                icon: CoreUtils.getIcon('edit'),
                label: '编辑',
                action: `organizationApp.materialsManager.editMaterial('${materialId}')`
            },
            {
                icon: CoreUtils.getIcon('copy'),
                label: '复制',
                action: `organizationApp.materialsManager.copyMaterial('${materialId}')`
            },
            { separator: true },
            {
                icon: CoreUtils.getIcon('delete'),
                label: '删除',
                className: 'danger',
                action: `organizationApp.materialsManager.deleteMaterial('${materialId}')`
            }
        ];
        
        this.app.uiManager.showContextMenu(e, items);
    }

    async copyMaterial(id) {
        const material = this.materials.find(m => m.id === id);
        if (!material) return;
        
        try {
            await navigator.clipboard.writeText(material.content);
            CoreUtils.showNotification('内容已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            CoreUtils.showNotification('复制失败', 'error');
        }
    }

    // 处理拖拽开始
    handleDragStart(e, materialId) {
        const material = this.materials.find(m => m.id === materialId);
        if (!material) return;
        
        // 设置拖拽数据
        e.dataTransfer.setData('text/plain', materialId);
        e.dataTransfer.setData('application/x-material', JSON.stringify({
            id: material.id,
            type: material.type || 'text',
            content: material.content || '',
            title: this.generateMaterialTitle(material),
            category: material.category,
            keywords: material.keywords
        }));
        
        // 设置拖拽效果
        e.dataTransfer.effectAllowed = 'copy';
        
        // 添加拖拽样式
        const dragElement = e.target.closest('.material-item');
        if (dragElement) {
            dragElement.classList.add('dragging');
            
            // 创建拖拽预览
            this.createDragPreview(e, material);
        }
        
        console.log('开始拖拽素材:', material.id);
    }

    // 生成素材标题
    generateMaterialTitle(material) {
        if (!material.content) return '未命名素材';
        
        const firstLine = material.content.split('\n')[0].trim();
        return firstLine.length > 50 ? firstLine.substring(0, 50) + '...' : firstLine;
    }

    // 创建拖拽预览
    createDragPreview(e, material) {
        const preview = document.createElement('div');
        preview.className = 'drag-preview';
        preview.style.cssText = `
            position: absolute;
            top: -1000px;
            left: -1000px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            max-width: 200px;
            z-index: 1000;
            font-size: 12px;
            pointer-events: none;
        `;
        
        const typeIcon = CoreUtils.getTypeIcon(material.type || 'text');
        const title = this.generateMaterialTitle(material);
        
        preview.innerHTML = `
            <div style="display: flex; align-items: center; gap: 6px;">
                <div style="color: #666;">${typeIcon}</div>
                <div style="font-weight: 500; color: #333;">${CoreUtils.escapeHtml(title)}</div>
            </div>
        `;
        
        document.body.appendChild(preview);
        
        // 设置拖拽预览图像
        e.dataTransfer.setDragImage(preview, 10, 10);
        
        // 延迟移除预览元素
        setTimeout(() => {
            if (preview.parentNode) {
                preview.parentNode.removeChild(preview);
            }
        }, 100);
    }

    // ===== 文件夹管理方法 =====



    renderMaterialsWithFolders(groupedMaterials) {
        let html = '';

        // 获取文件夹顺序
        const folderOrder = this.getFolderOrder();

        // 渲染文件夹
        folderOrder.forEach(folderName => {
            if (groupedMaterials.folders[folderName]) {
                html += this.renderMaterialFolder(folderName, groupedMaterials.folders[folderName]);
            }
        });

        // 渲染根目录素材
        if (groupedMaterials.root.length > 0) {
            html += this.renderRootMaterials(groupedMaterials.root);
        }

        return html;
    }

    renderMaterialFolder(folderName, materials) {
        const isOpen = this.openFolders.has(folderName);
        const materialCount = materials.length;

        let html = `
            <div class="folder-item material-folder" data-folder="${folderName}" draggable="true">
                <div class="folder-header" onclick="organizationApp.materialsManager.toggleFolder('${folderName}')">
                    <div class="folder-icon">
                        ${isOpen ? CoreUtils.getIcon('folder-open') : CoreUtils.getIcon('folder')}
                    </div>
                    <div class="folder-name">${CoreUtils.escapeHtml(folderName)}</div>
                    <div class="folder-count">(${materialCount})</div>
                    <div class="folder-actions">
                        <button class="folder-action-btn" data-action="create-material" data-folder="${folderName}" title="新建素材">
                            ${CoreUtils.getIcon('add')}
                        </button>
                        <button class="folder-action-btn" data-action="rename" data-folder="${folderName}" title="重命名文件夹">
                            ${CoreUtils.getIcon('edit')}
                        </button>
                        <button class="folder-action-btn" data-action="delete" data-folder="${folderName}" title="删除文件夹">
                            ${CoreUtils.getIcon('delete')}
                        </button>
                    </div>
                </div>
        `;

        if (isOpen) {
            html += `<div class="folder-content">`;
            materials.forEach(material => {
                html += this.renderMaterialItem(material);
            });
            html += `</div>`;
        }

        html += `</div>`;
        return html;
    }

    renderRootMaterials(materials) {
        let html = '';
        materials.forEach(material => {
            html += this.renderMaterialItem(material);
        });
        return html;
    }

    getFolderOrder() {
        // 获取保存的文件夹顺序
        try {
            const savedOrder = localStorage.getItem('qnotes-material-folder-order');
            if (savedOrder) {
                const order = JSON.parse(savedOrder);
                // 确保所有当前文件夹都在顺序中
                const allFolders = [...new Set(this.materials
                    .map(m => m.folder)
                    .filter(folder => folder && folder !== 'root')
                )];

                allFolders.forEach(folder => {
                    if (!order.includes(folder)) {
                        order.push(folder);
                    }
                });

                return order.filter(folder => allFolders.includes(folder));
            }
        } catch (error) {
            console.warn('Failed to load material folder order:', error);
        }

        // 默认按字母顺序
        return [...new Set(this.materials
            .map(m => m.folder)
            .filter(folder => folder && folder !== 'root')
        )].sort();
    }

    toggleFolder(folderName) {
        if (!this.openFolders) {
            this.openFolders = new Set();
        }

        if (this.openFolders.has(folderName)) {
            this.openFolders.delete(folderName);
        } else {
            this.openFolders.add(folderName);
        }

        // 直接渲染，但确保不会重复
        if (!this.isRendering) {
            this.renderMaterials();
        }
    }

    bindFolderEvents() {
        // 绑定文件夹事件
        const folderItems = document.querySelectorAll('.material-folder');
        folderItems.forEach(item => {
            this.bindMaterialFolderDragEvents(item);
        });

        // 绑定文件夹操作按钮事件
        document.querySelectorAll('.folder-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = btn.dataset.action;
                const folder = btn.dataset.folder;
                this.handleFolderAction(action, folder);
            });
        });
    }

    handleFolderAction(action, folderName) {
        switch (action) {
            case 'create-material':
                this.createMaterialInFolder(folderName);
                break;
            case 'rename':
                this.renameMaterialFolder(folderName);
                break;
            case 'delete':
                this.deleteMaterialFolder(folderName);
                break;
        }
    }

    // ===== 拖拽排序功能 =====

    bindMaterialFolderDragEvents(folderItem) {
        const folderName = folderItem.dataset.folder;

        // 文件夹头部可拖拽
        const folderHeader = folderItem.querySelector('.folder-header');
        if (folderHeader) {
            folderHeader.draggable = true;

            folderHeader.addEventListener('dragstart', (e) => {
                // 检查是否从操作按钮拖拽
                const isFromActions = e.target.closest('.folder-actions');

                if (isFromActions) {
                    e.preventDefault();
                    return;
                }

                console.log('Drag start for material folder:', folderName);

                const dragData = {
                    type: 'material-folder',
                    name: folderName
                };

                try {
                    e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
                    e.dataTransfer.effectAllowed = 'move';
                    console.log('Material folder drag data set:', JSON.stringify(dragData));
                } catch (error) {
                    console.error('Failed to set material folder drag data:', error);
                    e.preventDefault();
                    return;
                }

                folderItem.classList.add('dragging');
                e.dataTransfer.setDragImage(folderItem, 0, 0);
            });
        }

        // 文件夹拖拽目标事件
        folderItem.addEventListener('dragover', (e) => {
            if (e.dataTransfer.types.includes('text/plain')) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';

                // 显示插入位置指示器
                const rect = folderItem.getBoundingClientRect();
                const midY = rect.top + rect.height / 2;

                if (e.clientY < midY) {
                    folderItem.classList.add('drop-before');
                    folderItem.classList.remove('drop-after');
                } else {
                    folderItem.classList.add('drop-after');
                    folderItem.classList.remove('drop-before');
                }
            }
        });

        folderItem.addEventListener('dragleave', (e) => {
            if (!folderItem.contains(e.relatedTarget)) {
                folderItem.classList.remove('drop-before', 'drop-after');
            }
        });

        folderItem.addEventListener('drop', (e) => {
            console.log('Material folder drop event triggered on:', folderName);
            e.preventDefault();
            e.stopPropagation();
            folderItem.classList.remove('drop-before', 'drop-after');

            try {
                const dragDataStr = e.dataTransfer.getData('text/plain');
                console.log('Material folder drop - raw data:', dragDataStr);

                if (!dragDataStr) {
                    console.warn('No drag data found for material folder drop');
                    return;
                }

                const dragData = JSON.parse(dragDataStr);
                console.log('Material folder drop - parsed data:', dragData);

                if (dragData.type === 'material-folder') {
                    console.log('Calling reorderMaterialFolder:', dragData.name, '->', folderName);
                    this.reorderMaterialFolder(dragData.name, folderName, e);
                } else if (dragData.type === 'material') {
                    console.log('Moving material to folder:', dragData.id, '->', folderName);
                    this.moveMaterialToFolder(dragData.id, folderName);
                } else {
                    console.log('Unknown drag type:', dragData.type);
                }
            } catch (error) {
                console.error('Material folder drop data parsing failed:', error);
            }
        });

        folderItem.addEventListener('dragend', (e) => {
            console.log('Material folder drag end for:', folderName);
            folderItem.classList.remove('dragging');
            // 清除所有拖拽目标高亮
            document.querySelectorAll('.drop-target, .drop-before, .drop-after').forEach(el => {
                el.classList.remove('drop-target', 'drop-before', 'drop-after');
            });
        });
    }

    reorderMaterialFolder(draggedFolder, targetFolder, event) {
        console.log('reorderMaterialFolder called with:', { draggedFolder, targetFolder });

        if (draggedFolder === targetFolder) {
            console.log('Same folder, skipping reorder');
            return;
        }

        try {
            // 获取当前文件夹顺序
            let currentOrder = this.getFolderOrder();
            console.log('Current material folder order:', currentOrder);

            const draggedIndex = currentOrder.indexOf(draggedFolder);
            const targetIndex = currentOrder.indexOf(targetFolder);

            console.log('Material folder indices:', { draggedIndex, targetIndex });

            if (draggedIndex === -1 || targetIndex === -1) {
                console.error('Material folder indices not found');
                return;
            }

            // 确定插入位置
            const targetElement = event.target.closest('.material-folder');
            const rect = targetElement.getBoundingClientRect();
            const midY = rect.top + rect.height / 2;
            const insertBefore = event.clientY < midY;

            console.log('Insert position:', { insertBefore, clientY: event.clientY, midY });

            // 重新排列文件夹数组
            const reorderedFolders = [...currentOrder];
            const [movedFolder] = reorderedFolders.splice(draggedIndex, 1);

            let insertIndex = targetIndex;
            if (draggedIndex < targetIndex && !insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex < targetIndex && insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex > targetIndex && insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex > targetIndex && !insertBefore) {
                insertIndex = targetIndex + 1;
            }

            reorderedFolders.splice(insertIndex, 0, movedFolder);

            console.log('New material folder order:', reorderedFolders);

            // 保存文件夹顺序到localStorage
            localStorage.setItem('qnotes-material-folder-order', JSON.stringify(reorderedFolders));
            console.log('Material folder order saved to localStorage');

            // 重新渲染素材列表
            this.renderMaterials();
            CoreUtils.showNotification(`文件夹"${draggedFolder}"已重新排序`, 'success');

        } catch (error) {
            console.error('素材文件夹排序失败:', error);
            CoreUtils.showNotification('文件夹排序失败: ' + error.message, 'error');
        }
    }

    // ===== 素材拖拽排序功能 =====

    bindMaterialDragEvents(materialItem) {
        // 确保元素是可拖拽的
        materialItem.draggable = true;

        materialItem.addEventListener('dragstart', (e) => {
            const materialId = materialItem.dataset.id;
            console.log('Drag start for material:', materialId);

            // 设置拖拽数据
            const dragData = {
                type: 'material',
                id: materialId
            };

            e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
            e.dataTransfer.effectAllowed = 'move';

            materialItem.classList.add('dragging');

            // 简化拖拽预览
            e.dataTransfer.setDragImage(materialItem, 0, 0);
        });

        materialItem.addEventListener('dragend', (e) => {
            console.log('Material drag end for:', materialItem.dataset.id);
            materialItem.classList.remove('dragging');
            // 清除所有拖拽目标高亮
            document.querySelectorAll('.drop-target, .drop-before, .drop-after').forEach(el => {
                el.classList.remove('drop-target', 'drop-before', 'drop-after');
            });
        });

        // 素材之间的排序
        materialItem.addEventListener('dragover', (e) => {
            if (e.dataTransfer.types.includes('text/plain')) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';

                // 显示插入位置指示器
                const rect = materialItem.getBoundingClientRect();
                const midY = rect.top + rect.height / 2;

                if (e.clientY < midY) {
                    materialItem.classList.add('drop-before');
                    materialItem.classList.remove('drop-after');
                } else {
                    materialItem.classList.add('drop-after');
                    materialItem.classList.remove('drop-before');
                }
            }
        });

        materialItem.addEventListener('dragleave', (e) => {
            // 只有当鼠标真正离开元素时才移除样式
            if (!materialItem.contains(e.relatedTarget)) {
                materialItem.classList.remove('drop-before', 'drop-after');
            }
        });

        materialItem.addEventListener('drop', (e) => {
            console.log('Material drop event triggered on:', materialItem.dataset.id);
            e.preventDefault();
            e.stopPropagation();
            materialItem.classList.remove('drop-before', 'drop-after');

            try {
                const dragDataStr = e.dataTransfer.getData('text/plain');
                console.log('Material drop - raw data:', dragDataStr);

                if (!dragDataStr) {
                    console.warn('No drag data found');
                    return;
                }

                const dragData = JSON.parse(dragDataStr);
                console.log('Material drop - parsed data:', dragData);

                if (dragData.type === 'material') {
                    console.log('Calling reorderMaterial:', dragData.id, '->', materialItem.dataset.id);
                    // 素材排序
                    this.reorderMaterial(dragData.id, materialItem.dataset.id, e);
                } else {
                    console.log('Not a material drag operation, ignoring');
                }
            } catch (error) {
                console.error('素材拖拽数据解析失败:', error);
                console.log('Raw drag data:', e.dataTransfer.getData('text/plain'));
            }
        });
    }

    async reorderMaterial(draggedMaterialId, targetMaterialId, event) {
        console.log('reorderMaterial called with:', { draggedMaterialId, targetMaterialId });

        if (draggedMaterialId === targetMaterialId) {
            console.log('Same material, skipping reorder');
            return;
        }

        const draggedMaterial = this.materials.find(m => m.id === draggedMaterialId);
        const targetMaterial = this.materials.find(m => m.id === targetMaterialId);

        console.log('Found materials:', { draggedMaterial: draggedMaterial?.content?.substring(0, 20), targetMaterial: targetMaterial?.content?.substring(0, 20) });

        if (!draggedMaterial || !targetMaterial) {
            console.error('Materials not found');
            return;
        }

        // 检查是否在同一个文件夹中
        if ((draggedMaterial.folder || 'root') !== (targetMaterial.folder || 'root')) {
            console.warn('Materials not in same folder');
            CoreUtils.showNotification('只能在同一文件夹内排序素材', 'warning');
            return;
        }

        try {
            // 获取当前文件夹中的所有素材
            const folderMaterials = this.materials.filter(m => (m.folder || 'root') === (draggedMaterial.folder || 'root'));
            console.log('Folder materials count:', folderMaterials.length);

            // 按当前显示顺序排序（按创建时间倒序）
            folderMaterials.sort((a, b) => {
                const dateA = new Date(a.created_at || 0);
                const dateB = new Date(b.created_at || 0);
                return dateB - dateA;
            });

            // 找到拖拽素材和目标素材的索引
            const draggedIndex = folderMaterials.findIndex(m => m.id === draggedMaterialId);
            const targetIndex = folderMaterials.findIndex(m => m.id === targetMaterialId);

            console.log('Material indices:', { draggedIndex, targetIndex });

            if (draggedIndex === -1 || targetIndex === -1) {
                console.error('Material indices not found');
                return;
            }

            // 确定插入位置（基于鼠标位置）
            const targetElement = event.target.closest('.material-item');
            const rect = targetElement.getBoundingClientRect();
            const midY = rect.top + rect.height / 2;
            const insertBefore = event.clientY < midY;

            console.log('Insert position:', { insertBefore, clientY: event.clientY, midY });

            // 重新排列数组
            const reorderedMaterials = [...folderMaterials];
            const [movedMaterial] = reorderedMaterials.splice(draggedIndex, 1);

            let insertIndex = targetIndex;
            if (draggedIndex < targetIndex && !insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex < targetIndex && insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex > targetIndex && insertBefore) {
                insertIndex = targetIndex;
            } else if (draggedIndex > targetIndex && !insertBefore) {
                insertIndex = targetIndex + 1;
            }

            reorderedMaterials.splice(insertIndex, 0, movedMaterial);

            console.log('Reordering materials, new order:', reorderedMaterials.map(m => m.content?.substring(0, 20)));

            // 使用简单的排序权重
            const baseTime = Date.now();
            const updatePromises = [];

            for (let i = 0; i < reorderedMaterials.length; i++) {
                const material = reorderedMaterials[i];
                // 为每个素材分配新的排序权重
                const sortOrder = reorderedMaterials.length - i;
                const newTimestamp = new Date(baseTime - i * 1000).toISOString();

                console.log(`Updating material ${material.content?.substring(0, 20)} with sort order ${sortOrder}`);

                const updatePromise = ipcRenderer.invoke('update-collection', material.id, {
                    created_at: newTimestamp,
                    sort_order: sortOrder
                }).then(() => {
                    // 更新本地数据
                    const localMaterial = this.materials.find(m => m.id === material.id);
                    if (localMaterial) {
                        localMaterial.created_at = newTimestamp;
                        localMaterial.sort_order = sortOrder;
                    }
                }).catch(error => {
                    console.error(`更新素材 ${material.id} 排序失败:`, error);
                });

                updatePromises.push(updatePromise);
            }

            // 等待所有更新完成
            await Promise.all(updatePromises);
            console.log('All material updates completed');

            // 重新渲染素材列表
            this.renderMaterials();
            CoreUtils.showNotification(`素材已重新排序`, 'success');

        } catch (error) {
            console.error('素材排序失败:', error);
            CoreUtils.showNotification('素材排序失败: ' + error.message, 'error');
        }
    }

    // ===== 辅助方法 =====

    getTypeDisplayName(type) {
        const typeNames = {
            'text': '文本',
            'image': '图片',
            'link': '链接',
            'code': '代码',
            'video': '视频',
            'audio': '音频'
        };
        return typeNames[type] || '未知';
    }

    moveMaterialToFolder(materialId, folderName) {
        console.log('Moving material to folder:', materialId, '->', folderName);

        const material = this.materials.find(m => m.id === materialId);
        if (!material) {
            console.error('Material not found:', materialId);
            return;
        }

        // 更新素材的文件夹
        ipcRenderer.invoke('update-collection', materialId, {
            folder: folderName
        }).then(() => {
            // 更新本地数据
            material.folder = folderName;

            // 重新渲染
            this.renderMaterials();
            CoreUtils.showNotification(`素材已移动到文件夹"${folderName}"`, 'success');
        }).catch(error => {
            console.error('移动素材失败:', error);
            CoreUtils.showNotification('移动素材失败: ' + error.message, 'error');
        });
    }

    // ===== 文件夹操作方法 =====

    async createMaterialFolder() {
        const folderName = await this.app.uiManager.showPrompt('创建文件夹', '请输入文件夹名称:');

        if (!folderName || !folderName.trim()) {
            return;
        }

        const trimmedName = folderName.trim();

        // 检查文件夹是否已存在
        const existingFolders = [...new Set(this.materials
            .map(m => m.folder)
            .filter(folder => folder && folder !== 'root')
        )];

        if (existingFolders.includes(trimmedName)) {
            CoreUtils.showNotification('文件夹已存在', 'warning');
            return;
        }

        // 创建一个空的配置文件来表示文件夹存在
        try {
            let configMaterial;
            if (ipcRenderer) {
                configMaterial = await ipcRenderer.invoke('create-collection', {
                    content: `# ${trimmedName} 文件夹\n\n这是一个文件夹配置文件，请勿删除。`,
                    type: 'text',
                    category: '系统',
                    folder: trimmedName,
                    keywords: '.folder_config'
                });
            } else {
                // 浏览器环境，创建模拟配置文件
                configMaterial = {
                    id: 'folder-config-' + Date.now(),
                    content: `# ${trimmedName} 文件夹\n\n这是一个文件夹配置文件，请勿删除。`,
                    type: 'text',
                    category: '系统',
                    folder: trimmedName,
                    keywords: '.folder_config',
                    created_at: new Date().toISOString()
                };
            }

            // 更新本地数据
            this.materials.push(configMaterial);
            this.app.materials = this.materials;
            this.app.collections = this.materials;

            // 自动展开新创建的文件夹
            this.openFolders.add(trimmedName);

            // 重新渲染
            this.renderMaterials();

            CoreUtils.showNotification(`文件夹"${trimmedName}"创建成功`, 'success');
        } catch (error) {
            console.error('创建文件夹失败:', error);
            CoreUtils.showNotification('创建文件夹失败: ' + error.message, 'error');
        }
    }

    async createMaterialInFolder(folderName) {
        console.log('Create material in folder:', folderName);

        // 显示创建素材的模态框
        this.showCreateMaterialModal(folderName);
    }

    showCreateMaterialModal(folderName = 'root') {
        const modalHtml = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>创建新素材</h3>
                    <button class="modal-close" onclick="organizationApp.uiManager.closeModal()">×</button>
                </div>
                <div class="modal-body">
                    <form id="createMaterialForm">
                        <div class="form-group">
                            <label for="materialType">素材类型:</label>
                            <select id="materialType" required>
                                <option value="text">文本</option>
                                <option value="image">图片</option>
                                <option value="link">链接</option>
                                <option value="code">代码</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="materialFolder">所属文件夹:</label>
                            <select id="materialFolder">
                                <option value="root">根目录</option>
                                ${this.getFolderOptions(folderName)}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="materialContent">内容:</label>
                            <textarea id="materialContent" rows="6" placeholder="请输入素材内容..." required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="materialCategory">分类:</label>
                            <input type="text" id="materialCategory" placeholder="可选，如：工作、学习、生活等">
                        </div>

                        <div class="form-group">
                            <label for="materialKeywords">关键词:</label>
                            <input type="text" id="materialKeywords" placeholder="可选，用逗号分隔多个关键词">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="organizationApp.uiManager.closeModal()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="organizationApp.materialsManager.submitCreateMaterial()">创建</button>
                </div>
            </div>
        `;

        this.app.uiManager.showModal(modalHtml);
    }

    getFolderOptions(selectedFolder = 'root') {
        const folders = [...new Set(this.materials
            .map(m => m.folder)
            .filter(folder => folder && folder !== 'root')
        )].sort();

        return folders.map(folder =>
            `<option value="${folder}" ${folder === selectedFolder ? 'selected' : ''}>${CoreUtils.escapeHtml(folder)}</option>`
        ).join('');
    }

    async submitCreateMaterial() {
        const form = document.getElementById('createMaterialForm');
        const formData = new FormData(form);

        const materialData = {
            type: document.getElementById('materialType').value,
            folder: document.getElementById('materialFolder').value === 'root' ? null : document.getElementById('materialFolder').value,
            content: document.getElementById('materialContent').value.trim(),
            category: document.getElementById('materialCategory').value.trim() || null,
            keywords: document.getElementById('materialKeywords').value.trim() || null
        };

        if (!materialData.content) {
            CoreUtils.showNotification('请输入素材内容', 'warning');
            return;
        }

        try {
            const newMaterial = await ipcRenderer.invoke('create-collection', materialData);

            // 更新本地数据
            this.materials.unshift(newMaterial); // 添加到开头
            this.app.materials = this.materials;
            this.app.collections = this.materials;

            // 如果创建在文件夹中，确保文件夹是展开的
            if (materialData.folder) {
                this.openFolders.add(materialData.folder);
            }

            // 重新渲染
            this.renderMaterials();

            // 关闭模态框
            this.app.uiManager.closeModal();

            CoreUtils.showNotification('素材创建成功', 'success');
        } catch (error) {
            console.error('创建素材失败:', error);
            CoreUtils.showNotification('创建素材失败: ' + error.message, 'error');
        }
    }

    async renameMaterialFolder(folderName) {
        console.log('Rename material folder:', folderName);

        const newName = await this.app.uiManager.showPrompt('重命名文件夹', `请输入新的文件夹名称:`, folderName);

        if (!newName || !newName.trim() || newName.trim() === folderName) {
            return;
        }

        const trimmedNewName = newName.trim();

        // 检查新名称是否已存在
        const existingFolders = [...new Set(this.materials
            .map(m => m.folder)
            .filter(folder => folder && folder !== 'root' && folder !== folderName)
        )];

        if (existingFolders.includes(trimmedNewName)) {
            CoreUtils.showNotification('文件夹名称已存在', 'warning');
            return;
        }

        try {
            // 更新所有该文件夹中的素材
            const folderMaterials = this.materials.filter(m => m.folder === folderName);
            const updatePromises = folderMaterials.map(material =>
                ipcRenderer.invoke('update-collection', material.id, { folder: trimmedNewName })
            );

            await Promise.all(updatePromises);

            // 更新本地数据
            folderMaterials.forEach(material => {
                material.folder = trimmedNewName;
            });

            // 更新文件夹展开状态
            if (this.openFolders.has(folderName)) {
                this.openFolders.delete(folderName);
                this.openFolders.add(trimmedNewName);
            }

            // 更新文件夹顺序
            const folderOrder = this.getFolderOrder();
            const folderIndex = folderOrder.indexOf(folderName);
            if (folderIndex !== -1) {
                folderOrder[folderIndex] = trimmedNewName;
                localStorage.setItem('qnotes-material-folder-order', JSON.stringify(folderOrder));
            }

            // 重新渲染
            this.renderMaterials();

            CoreUtils.showNotification(`文件夹已重命名为"${trimmedNewName}"`, 'success');
        } catch (error) {
            console.error('重命名文件夹失败:', error);
            CoreUtils.showNotification('重命名文件夹失败: ' + error.message, 'error');
        }
    }

    async deleteMaterialFolder(folderName) {
        console.log('Delete material folder:', folderName);

        const folderMaterials = this.materials.filter(m => m.folder === folderName);

        if (folderMaterials.length === 0) {
            CoreUtils.showNotification('文件夹为空，无需删除', 'info');
            return;
        }

        const confirmed = await this.app.uiManager.showConfirm(
            '删除文件夹',
            `确定要删除文件夹"${folderName}"吗？\n\n此操作将删除文件夹中的 ${folderMaterials.length} 个素材，且无法撤销。`,
            '删除',
            'danger'
        );

        if (!confirmed) {
            return;
        }

        try {
            // 删除文件夹中的所有素材
            const deletePromises = folderMaterials.map(material =>
                ipcRenderer.invoke('delete-collection', material.id)
            );

            await Promise.all(deletePromises);

            // 更新本地数据
            this.materials = this.materials.filter(m => m.folder !== folderName);
            this.app.materials = this.materials;
            this.app.collections = this.materials;

            // 清理文件夹展开状态
            this.openFolders.delete(folderName);

            // 更新文件夹顺序
            const folderOrder = this.getFolderOrder();
            const updatedOrder = folderOrder.filter(folder => folder !== folderName);
            localStorage.setItem('qnotes-material-folder-order', JSON.stringify(updatedOrder));

            // 重新渲染
            this.renderMaterials();

            CoreUtils.showNotification(`文件夹"${folderName}"及其中的 ${folderMaterials.length} 个素材已删除`, 'success');
        } catch (error) {
            console.error('删除文件夹失败:', error);
            CoreUtils.showNotification('删除文件夹失败: ' + error.message, 'error');
        }
    }

    // ===== 搜索功能 =====

    initializeSearch() {
        const searchInput = document.getElementById('materialsSearchInput');
        const searchClear = document.getElementById('clearMaterialsSearchBtn');

        if (searchInput) {
            // 实时搜索
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.performSearch(e.target.value);
                }, 300);
            });

            // 回车搜索
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch(e.target.value);
                }
            });
        }

        if (searchClear) {
            searchClear.addEventListener('click', () => {
                this.clearSearch();
            });
        }
    }

    performSearch(query) {
        this.searchQuery = query.trim();

        if (this.searchQuery) {
            // 显示清除按钮
            const searchClear = document.getElementById('clearMaterialsSearchBtn');
            if (searchClear) {
                searchClear.style.display = 'flex';
            }


        } else {
            // 隐藏清除按钮
            const searchClear = document.getElementById('clearMaterialsSearchBtn');
            if (searchClear) {
                searchClear.style.display = 'none';
            }
        }

        // 重新渲染素材列表
        this.renderMaterials();
    }

    clearSearch() {
        this.searchQuery = '';

        const searchInput = document.getElementById('materialsSearchInput');
        const searchClear = document.getElementById('clearMaterialsSearchBtn');

        if (searchInput) {
            searchInput.value = '';
        }

        if (searchClear) {
            searchClear.style.display = 'none';
        }

        // 重新渲染素材列表
        this.renderMaterials();

        // 隐藏搜索统计
        this.hideSearchStats();
    }

    filterMaterialsBySearch(materials) {
        if (!this.searchQuery) {
            return materials;
        }

        const query = this.searchQuery.toLowerCase();
        const filteredMaterials = [];
        const matchedFolders = new Set();

        // 第一遍：找出匹配的素材和它们所在的文件夹
        materials.forEach(material => {
            // 跳过文件夹配置文件的搜索匹配
            if (material.keywords && material.keywords.includes('.folder_config')) {
                return;
            }

            let isMatch = false;

            // 搜索内容
            const content = (material.content || '').toLowerCase();
            if (content.includes(query)) {
                isMatch = true;
            }

            // 搜索关键词
            const keywords = (material.keywords || '').toLowerCase();
            if (keywords.includes(query)) {
                isMatch = true;
            }

            // 搜索分类
            const category = (material.category || '').toLowerCase();
            if (category.includes(query)) {
                isMatch = true;
            }

            // 搜索文件夹名
            const folder = (material.folder || '').toLowerCase();
            if (folder.includes(query)) {
                isMatch = true;
            }

            // 搜索类型
            const type = (material.type || '').toLowerCase();
            const typeDisplayName = this.getTypeDisplayName(type).toLowerCase();
            if (type.includes(query) || typeDisplayName.includes(query)) {
                isMatch = true;
            }

            if (isMatch) {
                filteredMaterials.push(material);
                if (material.folder && material.folder !== 'root') {
                    matchedFolders.add(material.folder);
                }
            }
        });

        // 第二遍：添加匹配文件夹的配置文件
        materials.forEach(material => {
            if (material.keywords && material.keywords.includes('.folder_config')) {
                if (matchedFolders.has(material.folder)) {
                    filteredMaterials.push(material);
                }
            }
        });

        return filteredMaterials;
    }

    saveSearchHistory(query) {
        try {
            let history = JSON.parse(localStorage.getItem('qnotes-material-search-history') || '[]');

            // 移除重复项
            history = history.filter(item => item !== query);

            // 添加到开头
            history.unshift(query);

            // 限制历史记录数量
            if (history.length > 10) {
                history = history.slice(0, 10);
            }

            localStorage.setItem('qnotes-material-search-history', JSON.stringify(history));
        } catch (error) {
            console.warn('保存搜索历史失败:', error);
        }
    }

    getSearchHistory() {
        try {
            return JSON.parse(localStorage.getItem('qnotes-material-search-history') || '[]');
        } catch (error) {
            console.warn('获取搜索历史失败:', error);
            return [];
        }
    }

    showSearchStats() {
        const filteredMaterials = this.filterMaterialsBySearch(this.materials);
        const statsElement = document.getElementById('materialSearchStats');

        if (statsElement) {
            statsElement.innerHTML = `找到 ${filteredMaterials.length} 个匹配的素材`;
            statsElement.style.display = 'block';
        }
    }

    hideSearchStats() {
        const statsElement = document.getElementById('materialSearchStats');
        if (statsElement) {
            statsElement.style.display = 'none';
        }
    }

    // 重写groupMaterialsByFolder方法以支持搜索过滤
    groupMaterialsByFolder() {
        // 先应用搜索过滤
        let materialsToGroup = this.searchQuery ?
            this.filterMaterialsBySearch(this.materials) :
            this.materials;

        const grouped = {
            root: [],
            folders: {}
        };

        materialsToGroup.forEach(material => {
            // 跳过文件夹配置文件，它们不应该在分组中显示
            if (material.keywords && material.keywords.includes('.folder_config')) {
                return;
            }

            const folder = material.folder || 'root';
            if (folder === 'root') {
                grouped.root.push(material);
            } else {
                if (!grouped.folders[folder]) {
                    grouped.folders[folder] = [];
                }
                grouped.folders[folder].push(material);
            }
        });

        // 按创建时间排序
        grouped.root.sort((a, b) => new Date(b.created_at || 0) - new Date(a.created_at || 0));
        Object.keys(grouped.folders).forEach(folder => {
            grouped.folders[folder].sort((a, b) => new Date(b.created_at || 0) - new Date(a.created_at || 0));
        });

        return grouped;
    }

    // 编辑相关的方法
    initEditInterface(material) {
        // 预填充内容
        const contentInput = document.getElementById('contentInput');
        if (contentInput) {
            contentInput.value = material.content || '';
        }

        // 初始化标签
        this.editTags = [];
        if (material.keywords) {
            this.editTags = material.keywords.split(',').map(tag => tag.trim()).filter(tag => tag);
        }

        // 存储当前编辑的素材ID
        this.editingMaterialId = material.id;

        // 绑定事件
        this.bindEditEvents();

        // 渲染标签
        this.renderEditTags();

        // 检测内容类型
        this.detectEditContentType();

        // 聚焦到内容输入框
        if (contentInput) {
            contentInput.focus();
            contentInput.setSelectionRange(contentInput.value.length, contentInput.value.length);
        }
    }

    bindEditEvents() {
        // 关闭按钮
        const closeBtn = document.getElementById('closeBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.app.uiManager.closeModal();
            });
        }

        // 取消按钮
        const clearBtn = document.getElementById('clearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.app.uiManager.closeModal();
            });
        }

        // 保存按钮
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveEditedMaterial();
            });
        }

        // 内容输入框事件
        const contentInput = document.getElementById('contentInput');
        if (contentInput) {
            contentInput.addEventListener('input', () => {
                this.detectEditContentType();
            });

            contentInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    this.saveEditedMaterial();
                }
            });
        }

        // 标签输入框事件
        const sourceInput = document.getElementById('sourceInput');
        if (sourceInput) {
            sourceInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const tagName = e.target.value.trim();
                    if (tagName) {
                        this.addEditTag(tagName);
                    }
                }
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.app.uiManager.closeModal();
            }
        });
    }

    addEditTag(tagName) {
        if (!tagName || this.editTags.includes(tagName)) return;
        this.editTags.push(tagName);
        this.renderEditTags();
        const sourceInput = document.getElementById('sourceInput');
        if (sourceInput) sourceInput.value = '';
    }

    removeEditTag(index) {
        this.editTags.splice(index, 1);
        this.renderEditTags();
    }

    renderEditTags() {
        const tagList = document.getElementById('tagList');
        if (!tagList) return;

        tagList.innerHTML = '';
        this.editTags.forEach((tag, index) => {
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-item';
            tagElement.innerHTML = `
                <span>${CoreUtils.escapeHtml(tag)}</span>
                <button class="tag-remove" onclick="organizationApp.materialsManager.removeEditTag(${index})">×</button>
            `;
            tagList.appendChild(tagElement);
        });
    }

    detectEditContentType() {
        const contentInput = document.getElementById('contentInput');
        const previewArea = document.getElementById('contentPreview');
        const previewContent = document.getElementById('previewContent');

        if (!contentInput || !previewArea || !previewContent) return;

        const content = contentInput.value.trim();

        if (content && this.isValidUrl(content)) {
            previewContent.innerHTML = `<a href="${content}" target="_blank" rel="noopener noreferrer">${content}</a>`;
            previewArea.style.display = 'block';
        } else {
            previewArea.style.display = 'none';
        }
    }

    isValidUrl(string) {
        try {
            const url = new URL(string.trim());
            return url.protocol === 'http:' || url.protocol === 'https:';
        } catch (e) {
            return false;
        }
    }

    async saveEditedMaterial() {
        const contentInput = document.getElementById('contentInput');
        const sourceInput = document.getElementById('sourceInput');

        if (!contentInput) return;

        let content = contentInput.value.trim();

        if (!content) {
            CoreUtils.showNotification('内容不能为空', 'error');
            return;
        }

        // 添加当前正在输入的标签
        const currentTag = sourceInput ? sourceInput.value.trim() : '';
        if (currentTag && !this.editTags.includes(currentTag)) {
            this.addEditTag(currentTag);
        }

        // 自动检测类型
        let type = 'text';
        if (this.isValidUrl(content)) {
            type = this.isImageUrl(content) ? 'image' : 'link';
        }

        try {
            const updateData = {
                type,
                content,
                keywords: this.editTags.length > 0 ? this.editTags.join(', ') : null
            };

            let updatedMaterial;
            if (ipcRenderer) {
                updatedMaterial = await ipcRenderer.invoke('update-collection', this.editingMaterialId, updateData);
            } else {
                // 浏览器环境模拟更新
                const index = this.materials.findIndex(m => m.id === this.editingMaterialId);
                if (index !== -1) {
                    updatedMaterial = { ...this.materials[index], ...updateData };
                    this.materials[index] = updatedMaterial;
                }
            }

            if (updatedMaterial) {
                // 更新本地数据
                const index = this.materials.findIndex(m => m.id === this.editingMaterialId);
                if (index !== -1) {
                    this.materials[index] = updatedMaterial;
                    this.app.materials = this.materials;
                    this.app.collections = this.materials;
                }

                // 重新渲染素材列表
                this.renderMaterials();
                if (this.app.uiManager) {
                    this.app.uiManager.updateStatusBar();
                }

                // 关闭模态框
                this.app.uiManager.closeModal();

                CoreUtils.showNotification('素材已更新', 'success');
            } else {
                throw new Error('更新操作失败');
            }

        } catch (error) {
            console.error('更新素材失败:', error);
            CoreUtils.showNotification('更新失败: ' + error.message, 'error');
        }
    }

    isImageUrl(url) {
        return this.isValidUrl(url) && /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(url);
    }

    // 编辑标签管理方法 - 完全参考quick-capture实现
    async initEditTags(keywords) {
        this.tags = [];
        this.availableTags = [];
        this.selectedSuggestionIndex = -1;

        // 从数据库加载最近标签
        await this.loadRecentTags();

        if (keywords) {
            this.tags = keywords.split(',').map(tag => tag.trim()).filter(tag => tag);
        }
        this.renderTags();
        this.renderRecentTags();
    }

    async loadRecentTags() {
        try {
            if (ipcRenderer) {
                // 从数据库获取最近使用的标签
                const recentTags = await ipcRenderer.invoke('get-recent-tags', { limit: 10 });
                console.log('从数据库加载的最近标签:', recentTags);

                if (Array.isArray(recentTags)) {
                    this.availableTags = recentTags.map(tag => ({
                        name: tag.keyword || tag.name,
                        count: tag.count || 1
                    }));
                } else {
                    console.warn('获取的标签数据格式不正确:', recentTags);
                    this.availableTags = [];
                }
            } else {
                // 浏览器环境使用模拟数据
                console.warn('浏览器环境，使用模拟标签数据');
                this.availableTags = [
                    { name: 'test', count: 5 },
                    { name: '学习', count: 3 },
                    { name: '工作', count: 8 },
                    { name: '技术', count: 6 },
                    { name: '笔记', count: 4 }
                ];
            }
        } catch (error) {
            console.error('加载最近标签失败:', error);
            // 使用默认标签作为后备
            this.availableTags = [
                { name: 'test', count: 5 },
                { name: '学习', count: 3 },
                { name: '工作', count: 8 },
                { name: '技术', count: 6 },
                { name: '笔记', count: 4 }
            ];
        }
    }

    renderTags() {
        const tagList = document.getElementById('tagList');
        if (!tagList) return;

        tagList.innerHTML = '';

        this.tags.forEach((tag, index) => {
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-item';
            tagElement.innerHTML = `
                <span>${CoreUtils.escapeHtml(tag)}</span>
                <button class="tag-remove" onclick="organizationApp.materialsManager.removeTag(${index})">×</button>
            `;
            tagList.appendChild(tagElement);
        });
    }

    renderRecentTags() {
        const recentTagsContainer = document.getElementById('recentTags');
        if (!recentTagsContainer) return;

        recentTagsContainer.innerHTML = '';

        // 显示最近使用的5个标签（排除已选择的）
        const recentTags = this.availableTags
            .filter(tag => !this.tags.includes(tag.name))
            .sort((a, b) => b.count - a.count)
            .slice(0, 5);

        recentTags.forEach(tag => {
            const tagElement = document.createElement('div');
            tagElement.className = 'recent-tag';
            tagElement.innerHTML = `
                <span class="recent-tag-name">${tag.name}</span>
                <span class="recent-tag-count">${tag.count}</span>
            `;
            tagElement.addEventListener('click', () => {
                this.addTag(tag.name);
            });
            recentTagsContainer.appendChild(tagElement);
        });
    }

    addTag(tagName) {
        if (!tagName || this.tags.includes(tagName)) return;

        this.tags.push(tagName);
        this.renderTags();
        this.renderRecentTags(); // 更新最近标签显示
        document.getElementById('sourceInput').value = '';
        this.hideTagSuggestions();
    }

    removeTag(index) {
        this.tags.splice(index, 1);
        this.renderTags();
        this.renderRecentTags(); // 更新最近标签显示
    }

    showTagSuggestions(query) {
        const suggestions = document.getElementById('tagSuggestions');
        if (!suggestions) return;

        const filtered = this.availableTags
            .filter(tag => tag.name.toLowerCase().includes(query.toLowerCase()) && !this.tags.includes(tag.name))
            .slice(0, 5);

        suggestions.innerHTML = '';

        filtered.forEach((tag, index) => {
            const suggestion = document.createElement('div');
            suggestion.className = 'tag-suggestion';
            if (index === this.selectedSuggestionIndex) {
                suggestion.classList.add('selected');
            }
            suggestion.innerHTML = `
                <span class="tag-suggestion-text">${tag.name}</span>
                <span class="tag-suggestion-count">${tag.count}</span>
            `;
            suggestion.addEventListener('click', () => {
                this.addTag(tag.name);
            });
            suggestions.appendChild(suggestion);
        });

        suggestions.style.display = filtered.length > 0 ? 'block' : 'none';
    }

    hideTagSuggestions() {
        const suggestions = document.getElementById('tagSuggestions');
        if (suggestions) {
            suggestions.style.display = 'none';
        }
        this.selectedSuggestionIndex = -1;
    }

    navigateSuggestions(direction) {
        const suggestions = document.querySelectorAll('.tag-suggestion');
        if (suggestions.length === 0) return;

        // 移除当前选中状态
        if (this.selectedSuggestionIndex >= 0) {
            suggestions[this.selectedSuggestionIndex].classList.remove('selected');
        }

        // 更新选中索引
        this.selectedSuggestionIndex += direction;
        if (this.selectedSuggestionIndex < 0) {
            this.selectedSuggestionIndex = suggestions.length - 1;
        } else if (this.selectedSuggestionIndex >= suggestions.length) {
            this.selectedSuggestionIndex = 0;
        }

        // 添加新的选中状态
        suggestions[this.selectedSuggestionIndex].classList.add('selected');
    }

    selectSuggestion() {
        const suggestions = document.querySelectorAll('.tag-suggestion');
        if (this.selectedSuggestionIndex >= 0 && suggestions[this.selectedSuggestionIndex]) {
            const tagName = suggestions[this.selectedSuggestionIndex].querySelector('.tag-suggestion-text').textContent;
            this.addTag(tagName);
        }
    }

    bindEditTagEvents() {
        const tagInput = document.getElementById('sourceInput');
        if (!tagInput) return;

        tagInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query) {
                this.showTagSuggestions(query);
            } else {
                this.hideTagSuggestions();
            }
        });

        tagInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (this.selectedSuggestionIndex >= 0) {
                    this.selectSuggestion();
                } else {
                    const tagName = tagInput.value.trim();
                    if (tagName) {
                        this.addTag(tagName);
                    }
                }
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.navigateSuggestions(1);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.navigateSuggestions(-1);
            } else if (e.key === 'Escape') {
                this.hideTagSuggestions();
            } else if (e.key === 'Backspace' && tagInput.value === '' && this.tags.length > 0) {
                this.removeTag(this.tags.length - 1);
            }
        });

        tagInput.addEventListener('blur', () => {
            setTimeout(() => {
                this.hideTagSuggestions();
            }, 200);
        });
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MaterialsManager;
} else {
    window.MaterialsManager = MaterialsManager;
}
