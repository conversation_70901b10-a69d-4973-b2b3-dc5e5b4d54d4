const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class QuickCapture {
    constructor() {
        this.isProcessing = false;
        this.detectedType = 'text';
        this.tags = [];
        this.availableTags = []; // 从历史记录中获取的标签
        this.selectedSuggestionIndex = -1;
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupIPC();
        this.loadClipboardContent();
        this.loadAvailableTags();
        // 不设置焦点到输入框，让用户可以直接按Enter保存
    }

    bindEvents() {
        // 关闭按钮
        document.getElementById('closeBtn').addEventListener('click', () => {
            this.closeWindow();
        });

        // 清除按钮
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearForm();
        });

        // 保存按钮
        document.getElementById('saveBtn').addEventListener('click', () => {
            this.saveCapture();
        });

        // 成功页面按钮
        document.getElementById('continueBtn').addEventListener('click', () => {
            this.continueCapture();
        });

        document.getElementById('organizeBtn').addEventListener('click', () => {
            this.switchToOrganization();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeWindow();
            } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                this.saveCapture();
            } else if (e.key === 'Enter' && !e.ctrlKey && !e.metaKey && !e.shiftKey) {
                // 直接按Enter保存（如果没有焦点在输入框内）
                const activeElement = document.activeElement;
                if (activeElement.tagName !== 'TEXTAREA' && 
                    (activeElement.tagName !== 'INPUT' || activeElement.id !== 'sourceInput')) {
                    e.preventDefault();
                    this.saveCapture();
                }
            }
        });

        // 输入框事件
        const contentInput = document.getElementById('contentInput');
        contentInput.addEventListener('input', () => {
            this.detectContentType();
            this.updateSaveButton();
        });

        contentInput.addEventListener('paste', (e) => {
            // 延迟处理粘贴内容
            setTimeout(() => {
                this.detectContentType();
                this.updateSaveButton();
            }, 10);
        });

        // 标签输入事件
        this.setupTagEvents();

        // 窗口失去焦点时自动关闭（但不跳转）
        window.addEventListener('blur', () => {
            setTimeout(() => {
                if (!document.querySelector('.image-lightbox')) { // 如果没有lightbox打开
                    this.closeWindow();
                }
            }, 100);
        });
    }

    setupTagEvents() {
        const tagInput = document.getElementById('sourceInput');
        const suggestions = document.getElementById('tagSuggestions');

        tagInput.addEventListener('input', (e) => {
            const value = e.target.value.trim();
            if (value.length > 0) {
                this.showTagSuggestions(value);
            } else {
                this.hideTagSuggestions();
            }
        });

        tagInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (this.selectedSuggestionIndex >= 0) {
                    this.selectSuggestion();
                } else {
                    const tagName = tagInput.value.trim();
                    if (tagName) {
                        this.addTag(tagName);
                    }
                }
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.navigateSuggestions(1);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.navigateSuggestions(-1);
            } else if (e.key === 'Escape') {
                this.hideTagSuggestions();
            } else if (e.key === 'Backspace' && tagInput.value === '' && this.tags.length > 0) {
                this.removeTag(this.tags.length - 1);
            }
        });

        tagInput.addEventListener('blur', () => {
            setTimeout(() => {
                this.hideTagSuggestions();
            }, 150);
        });
    }

    setupIPC() {
        // 监听剪贴板内容
        ipcRenderer.on('clipboard-content', (event, data) => {
            this.handleClipboardContent(data);
        });
    }

    async loadClipboardContent() {
        try {
            console.log('正在加载剪贴板内容...');
            const clipboardData = await ipcRenderer.invoke('get-clipboard-content');
            console.log('剪贴板数据:', clipboardData);
            this.handleClipboardContent(clipboardData);
        } catch (error) {
            console.error('获取剪贴板内容失败:', error);
        }
    }

    handleClipboardContent(data) {
        console.log('处理剪贴板数据:', data);
        if (!data) {
            console.log('剪贴板数据为空');
            return;
        }

        const contentInput = document.getElementById('contentInput');
        const previewArea = document.getElementById('contentPreview');

        // 处理同时有文本和图片的情况
        if (data.hasImage && data.image && data.text && data.text.trim()) {
            console.log('检测到图片和文本，优先显示图片');
            this.detectedType = 'image';
            this.showImagePreview(data.image);
            contentInput.value = data.text; // 将文本作为图片描述
            contentInput.placeholder = '为图片添加描述（可选）...';
            this.adjustInputForImage(contentInput);
        } else if (data.hasImage && data.image) {
            console.log('检测到图片');
            this.detectedType = 'image';
            this.showImagePreview(data.image);
            contentInput.placeholder = '为图片添加描述（可选）...';
            this.adjustInputForImage(contentInput);
        } else if (data.text && data.text.trim()) {
            console.log('设置文本内容:', data.text);
            contentInput.value = data.text;
            this.detectContentType(data.text);
            // 不显示文本预览
            previewArea.style.display = 'none';
        }

        this.updateSaveButton();
    }

    detectContentType(content) {
        const text = content || document.getElementById('contentInput').value;
        
        if (!text) {
            this.detectedType = 'text';
            return;
        }

        // 检测是否是URL
        if (this.isValidUrl(text)) {
            this.detectedType = 'link';
        } else if (this.isCodeSnippet(text)) {
            this.detectedType = 'code';
        } else {
            this.detectedType = 'text';
        }
    }

    isValidUrl(string) {
        try {
            const url = new URL(string.trim());
            return url.protocol === 'http:' || url.protocol === 'https:';
        } catch (e) {
            return false;
        }
    }

    isCodeSnippet(text) {
        // 简单的代码检测
        const codePatterns = [
            /function\s+\w+\s*\(/,
            /class\s+\w+/,
            /import\s+.+from/,
            /const\s+\w+\s*=/,
            /def\s+\w+\s*\(/,
            /<\w+.*>/,
            /\{[^}]*\}/,
            /\[[^\]]*\]/
        ];

        return codePatterns.some(pattern => pattern.test(text));
    }

    // 文本不需要预览，直接在输入框中显示

    adjustInputForImage(contentInput) {
        // 当显示图片时，压缩文本输入框高度
        contentInput.style.minHeight = '50px';
        contentInput.rows = 2;
    }

    showImagePreview(imageDataUrl) {
        const previewArea = document.getElementById('contentPreview');
        const previewContent = document.getElementById('previewContent');
        const previewLabel = document.querySelector('.preview-label');

        previewLabel.textContent = '图片预览';
        previewContent.innerHTML = `
            <div class="image-preview-container">
                <img src="${imageDataUrl}" 
                     alt="剪贴板图片" 
                     class="preview-image"
                     onclick="quickCapture.showImageLightbox('${imageDataUrl}')">
            </div>
        `;
        previewArea.style.display = 'block';
        
        // 存储图片数据供lightbox使用
        this.currentImageData = imageDataUrl;
    }

    showImageLightbox(imageDataUrl) {
        // 创建lightbox容器
        const lightbox = document.createElement('div');
        lightbox.className = 'image-lightbox';
        lightbox.innerHTML = `
            <div class="lightbox-backdrop" onclick="this.parentElement.remove()">
                <div class="lightbox-content" onclick="event.stopPropagation()">
                    <button class="lightbox-close" onclick="this.closest('.image-lightbox').remove()">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 6.586L13.314 1.272a1 1 0 111.414 1.414L9.414 8l5.314 5.314a1 1 0 01-1.414 1.414L8 9.414l-5.314 5.314a1 1 0 01-1.414-1.414L6.586 8 1.272 2.686a1 1 0 111.414-1.414L8 6.586z"/>
                        </svg>
                    </button>
                    <img src="${imageDataUrl}" alt="图片预览" class="lightbox-image">
                </div>
            </div>
        `;
        
        document.body.appendChild(lightbox);
        
        // 添加键盘事件
        const handleKeydown = (e) => {
            if (e.key === 'Escape') {
                lightbox.remove();
                document.removeEventListener('keydown', handleKeydown);
            }
        };
        document.addEventListener('keydown', handleKeydown);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    updateSaveButton() {
        const contentInput = document.getElementById('contentInput');
        const saveBtn = document.getElementById('saveBtn');
        const previewArea = document.getElementById('contentPreview');
        
        // 如果有预览内容（图片或文本），或者输入框有内容，则启用保存按钮
        const hasContent = contentInput.value.trim().length > 0 || 
                          previewArea.style.display === 'block';
        
        saveBtn.disabled = !hasContent;
    }

    // 标签管理方法
    async loadAvailableTags() {
        try {
            // 从数据库加载历史标签
            const tags = await ipcRenderer.invoke('get-available-tags');
            this.availableTags = tags || [];
            this.renderRecentTags();
        } catch (error) {
            console.error('加载标签失败:', error);
            this.availableTags = [];
            this.renderRecentTags();
        }
    }

    renderRecentTags() {
        const recentTagsContainer = document.getElementById('recentTags');
        recentTagsContainer.innerHTML = '';
        
        // 显示最近使用的5个标签（排除已选择的）
        const recentTags = this.availableTags
            .filter(tag => !this.tags.includes(tag.name))
            .sort((a, b) => b.count - a.count)
            .slice(0, 5);
        
        recentTags.forEach(tag => {
            const tagElement = document.createElement('div');
            tagElement.className = 'recent-tag';
            tagElement.innerHTML = `
                <span class="recent-tag-name">${tag.name}</span>
                <span class="recent-tag-count">${tag.count}</span>
            `;
            tagElement.addEventListener('click', () => {
                this.addTag(tag.name);
            });
            recentTagsContainer.appendChild(tagElement);
        });
    }

    addTag(tagName) {
        if (!tagName || this.tags.includes(tagName)) return;
        
        this.tags.push(tagName);
        this.renderTags();
        this.renderRecentTags(); // 更新最近标签显示
        document.getElementById('sourceInput').value = '';
        this.hideTagSuggestions();
    }

    removeTag(index) {
        this.tags.splice(index, 1);
        this.renderTags();
        this.renderRecentTags(); // 更新最近标签显示
    }

    renderTags() {
        const tagList = document.getElementById('tagList');
        tagList.innerHTML = '';
        
        this.tags.forEach((tag, index) => {
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-item';
            tagElement.innerHTML = `
                <span>${tag}</span>
                <button class="tag-remove" onclick="quickCapture.removeTag(${index})">×</button>
            `;
            tagList.appendChild(tagElement);
        });
    }


    showTagSuggestions(query) {
        const suggestions = document.getElementById('tagSuggestions');
        const filtered = this.availableTags
            .filter(tag => tag.name.toLowerCase().includes(query.toLowerCase()) && !this.tags.includes(tag.name))
            .slice(0, 5);

        if (filtered.length === 0) {
            this.hideTagSuggestions();
            return;
        }

        suggestions.innerHTML = '';
        filtered.forEach((tag, index) => {
            const suggestion = document.createElement('div');
            suggestion.className = 'tag-suggestion';
            suggestion.innerHTML = `
                <span class="tag-suggestion-text">${tag.name}</span>
                <span class="tag-suggestion-count">${tag.count}</span>
            `;
            suggestion.addEventListener('click', () => {
                this.addTag(tag.name);
            });
            suggestions.appendChild(suggestion);
        });

        suggestions.style.display = 'block';
        this.selectedSuggestionIndex = -1;
    }

    hideTagSuggestions() {
        document.getElementById('tagSuggestions').style.display = 'none';
        this.selectedSuggestionIndex = -1;
    }

    navigateSuggestions(direction) {
        const suggestions = document.querySelectorAll('.tag-suggestion');
        if (suggestions.length === 0) return;

        if (this.selectedSuggestionIndex >= 0) {
            suggestions[this.selectedSuggestionIndex].classList.remove('selected');
        }

        this.selectedSuggestionIndex += direction;
        
        if (this.selectedSuggestionIndex < 0) {
            this.selectedSuggestionIndex = suggestions.length - 1;
        } else if (this.selectedSuggestionIndex >= suggestions.length) {
            this.selectedSuggestionIndex = 0;
        }

        suggestions[this.selectedSuggestionIndex].classList.add('selected');
    }

    selectSuggestion() {
        const suggestions = document.querySelectorAll('.tag-suggestion');
        if (this.selectedSuggestionIndex >= 0 && suggestions[this.selectedSuggestionIndex]) {
            const tagName = suggestions[this.selectedSuggestionIndex].querySelector('.tag-suggestion-text').textContent;
            this.addTag(tagName);
        }
    }

    clearForm() {
        // 清除输入框内容
        const contentInput = document.getElementById('contentInput');
        const sourceInput = document.getElementById('sourceInput');
        const previewArea = document.getElementById('contentPreview');
        
        contentInput.value = '';
        sourceInput.value = '';
        contentInput.placeholder = '输入要收藏的内容，或直接按 Enter 保存剪贴板内容...';
        
        // 重置输入框高度
        contentInput.style.minHeight = '140px';
        contentInput.rows = 4;
        
        // 隐藏预览区域
        previewArea.style.display = 'none';
        
        // 清除标签
        this.tags = [];
        this.renderTags();
        this.renderRecentTags(); // 更新最近标签显示
        this.hideTagSuggestions();
        
        // 重置状态
        this.detectedType = 'text';
        this.currentImageData = null;
        
        // 更新保存按钮状态
        this.updateSaveButton();
        
        console.log('表单已清除');
    }

    async saveCapture() {
        if (this.isProcessing) return;

        const contentInput = document.getElementById('contentInput');
        const sourceInput = document.getElementById('sourceInput');
        const previewArea = document.getElementById('contentPreview');

        let content = contentInput.value.trim();
        
        // 如果没有输入内容但有预览（比如图片），使用预览内容
        if (!content && previewArea.style.display === 'block') {
            if (this.detectedType === 'image') {
                content = '[图片]'; // 图片的占位内容
            }
        }

        if (!content) {
            this.showError('请输入内容或确保剪贴板中有可保存的内容');
            return;
        }

        // 添加当前正在输入的标签
        const currentTag = sourceInput.value.trim();
        if (currentTag && !this.tags.includes(currentTag)) {
            this.addTag(currentTag);
        }

        this.isProcessing = true;
        this.showLoading();

        try {
            const captureData = {
                content: content,
                type: this.detectedType,
                source: this.tags.length > 0 ? this.tags.join(', ') : 'quick-capture',
                tags: this.tags,
                timestamp: new Date().toISOString()
            };

            // 如果是图片类型，尝试获取图片数据
            if (this.detectedType === 'image') {
                const clipboardData = await ipcRenderer.invoke('get-clipboard-content');
                if (clipboardData.hasImage && clipboardData.image) {
                    captureData.imageData = clipboardData.image;
                }
            }

            const result = await ipcRenderer.invoke('quick-capture', captureData);

            if (result) {
                this.showSuccess();
            }

        } catch (error) {
            console.error('保存失败:', error);
            this.showError(error.message || '保存失败，请重试');
        } finally {
            this.isProcessing = false;
            this.hideLoading();
        }
    }

    continueCapture() {
        // 关闭窗口
        this.hideSuccess();
        this.closeWindow();
    }

    async switchToOrganization() {
        try {
            await ipcRenderer.invoke('switch-to-organization');
            this.closeWindow();
        } catch (error) {
            console.error('切换模式失败:', error);
            this.showError('切换模式失败，请重试');
        }
    }

    closeWindow() {
        const container = document.querySelector('.capture-window');
        container.style.animation = 'windowSwooshUp 0.25s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards';
        
        setTimeout(() => {
            window.close();
        }, 250);
    }

    showLoading() {
        const container = document.querySelector('.capture-window');
        container.classList.add('loading');
        
        const saveBtn = document.getElementById('saveBtn');
        saveBtn.textContent = '保存中';
        saveBtn.disabled = true;
    }

    hideLoading() {
        const container = document.querySelector('.capture-window');
        container.classList.remove('loading');
        
        const saveBtn = document.getElementById('saveBtn');
        saveBtn.textContent = '保存';
        saveBtn.disabled = false;
    }

    showSuccess() {
        const successOverlay = document.getElementById('successOverlay');
        successOverlay.style.display = 'flex';
        
        // 设置Esc键关闭成功弹窗
        this.setupSuccessKeyboardEvents();
        
        // 1.5秒后自动关闭成功弹窗和整个窗口
        this.successAutoCloseTimer = setTimeout(() => {
            if (successOverlay.style.display === 'flex') {
                this.hideSuccess();
                this.closeWindow();
            }
        }, 1500);
    }

    setupSuccessKeyboardEvents() {
        const handleSuccessKeydown = (e) => {
            if (e.key === 'Escape') {
                e.stopPropagation(); // 阻止事件冒泡
                this.hideSuccess();
                this.closeWindow();
                document.removeEventListener('keydown', handleSuccessKeydown);
            }
        };
        document.addEventListener('keydown', handleSuccessKeydown);
        
        // 存储事件处理器以便清理
        this.successKeydownHandler = handleSuccessKeydown;
    }

    hideSuccess() {
        const successOverlay = document.getElementById('successOverlay');
        successOverlay.style.display = 'none';
        
        // 清理定时器和事件监听器
        if (this.successAutoCloseTimer) {
            clearTimeout(this.successAutoCloseTimer);
            this.successAutoCloseTimer = null;
        }
        
        if (this.successKeydownHandler) {
            document.removeEventListener('keydown', this.successKeydownHandler);
            this.successKeydownHandler = null;
        }
    }

    showError(message) {
        // 创建临时错误提示
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 59, 48, 0.95);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            z-index: 2000;
            animation: errorSlideIn 0.3s ease;
        `;
        errorDiv.textContent = message;
        
        document.body.appendChild(errorDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.style.animation = 'errorSlideOut 0.3s ease forwards';
                setTimeout(() => {
                    errorDiv.remove();
                }, 300);
            }
        }, 3000);
    }
}

// 添加窗口动画
const style = document.createElement('style');
style.textContent = `
    @keyframes windowSwooshUp {
        0% {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
        50% {
            opacity: 0.6;
            transform: scale(0.94) translateY(-40px);
        }
        100% {
            opacity: 0;
            transform: scale(0.85) translateY(-100px);
        }
    }
    
    @keyframes windowDisappear {
        from {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
        to {
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
        }
    }
    
    @keyframes errorSlideIn {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
    }
    
    @keyframes errorSlideOut {
        from {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
    }
`;
document.head.appendChild(style);

// 初始化快速捕获
const quickCapture = new QuickCapture();

// 导出供HTML调用
window.quickCapture = quickCapture;