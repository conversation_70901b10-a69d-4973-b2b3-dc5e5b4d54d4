class UIManager {
    constructor(app) {
        this.app = app;
        this.currentLeftTab = 'materials';
        this.currentRightPanel = 'preview';
        this.isLeftSidebarVisible = true;
        this.isRightSidebarVisible = true;
        this.resizeObserver = null;
        this.isResizing = false;
        
        // 延迟初始化，确保DOM已加载
        setTimeout(() => {
            this.initializeUI();
            this.bindEvents();
            this.setupResizers();
        }, 0);
    }

    initializeUI() {
        // 初始化 Markdown 编辑器
        this.initializeMarkdownEditor();
        
        // 设置初始状态
        this.updateWordCount();
        this.updateStatusBar();
        
        // 隐藏加载覆盖层
        CoreUtils.hideLoading();
    }

    initializeMarkdownEditor() {
        const editorContent = document.getElementById('editorContent');
        if (!editorContent) return;

        let editor = document.getElementById('markdownEditor');
        if (!editor) {
            editor = document.createElement('textarea');
            editor.id = 'markdownEditor';
            editor.placeholder = '开始编写你的文档，或者从左侧拖拽素材到这里...';
            editorContent.innerHTML = '';
            editorContent.appendChild(editor);
        }

        const preview = document.getElementById('previewContent'); // FIX: Correct preview element ID
        
        if (editor && preview) {
            this.markdownEditor = new MarkdownEditor(editor, preview, {
                enableDragDrop: true, // 确保拖拽功能启用
                enableRealtimeCursor: false, // 禁用实时光标以避免干扰正常点击
                enableInsertionIndicator: true
            });
            
            // 确保拖拽功能已绑定
            if (this.markdownEditor.bindDragEvents) {
                this.markdownEditor.bindDragEvents();
            }
            
            // 监听素材插入事件
            editor.addEventListener('materialInserted', (e) => {
                console.log('素材已插入:', e.detail);
                this.updatePreview();
                this.updateWordCount();
            });
            
            editor.addEventListener('input', () => {
                this.app.editorContent = editor.value;
                this.updateWordCount();
                this.updatePreview();
            });
            
            editor.addEventListener('keydown', (e) => this.handleEditorKeydown(e));
            
            // 将编辑器实例暴露给应用
            this.app.markdownEditor = this.markdownEditor;
        }
    }

    handleEditorKeydown(e) {
        // Tab键插入缩进
        if (e.key === 'Tab') {
            e.preventDefault();
            this.insertAtCursor('    ');
            return;
        }
        
        // 快捷键处理
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            this.wrapSelection('**', '**');
            return;
        }
        
        if ((e.ctrlKey || e.metaKey) && e.key === 'i') {
            e.preventDefault();
            this.wrapSelection('*', '*');
            return;
        }
        
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.insertLink();
            return;
        }
        
        // 自动列表
        if (e.key === 'Enter') {
            const editor = e.target;
            const cursorPos = editor.selectionStart;
            const textBeforeCursor = editor.value.substring(0, cursorPos);
            const currentLine = textBeforeCursor.split('\n').pop();
            
            // 检查是否是列表项
            const listMatch = currentLine.match(/^(\s*)([-*+]|\d+\.)\s/);
            if (listMatch) {
                e.preventDefault();
                const indent = listMatch[1];
                const marker = listMatch[2];
                
                // 如果当前行只有标记，删除标记
                if (currentLine.trim() === marker) {
                    const lineStart = cursorPos - currentLine.length;
                    editor.setSelectionRange(lineStart, cursorPos);
                    editor.value = editor.value.substring(0, lineStart) + editor.value.substring(cursorPos);
                    editor.setSelectionRange(lineStart, lineStart);
                } else {
                    // 插入新的列表项
                    const newMarker = marker.match(/\d+/) ? (parseInt(marker) + 1) + '.' : marker;
                    this.insertAtCursor(`\n${indent}${newMarker} `);
                }
            }
        }
    }

    insertAtCursor(text) {
        const editor = document.getElementById('markdownEditor');
        if (!editor) return;
        
        const start = editor.selectionStart;
        const end = editor.selectionEnd;
        const value = editor.value;
        
        editor.value = value.substring(0, start) + text + value.substring(end);
        editor.selectionStart = editor.selectionEnd = start + text.length;
        
        // 触发输入事件
        editor.dispatchEvent(new Event('input'));
        editor.focus();
    }

    wrapSelection(before, after) {
        const editor = document.getElementById('markdownEditor');
        if (!editor) return;
        
        const start = editor.selectionStart;
        const end = editor.selectionEnd;
        const selectedText = editor.value.substring(start, end);
        
        if (selectedText) {
            const newText = before + selectedText + after;
            editor.value = editor.value.substring(0, start) + newText + editor.value.substring(end);
            editor.selectionStart = start + before.length;
            editor.selectionEnd = start + before.length + selectedText.length;
        } else {
            this.insertAtCursor(before + after);
            editor.selectionStart = editor.selectionEnd = start + before.length;
        }
        
        editor.focus();
        editor.dispatchEvent(new Event('input'));
    }

    insertLink() {
        this.showInputModal('插入链接', '请输入链接地址:', '', (url) => {
            if (url) {
                this.wrapSelection('[', `](${url})`);
            }
        });
    }

    showInputModal(title, message, defaultValue = '', callback) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal-overlay" id="inputModalOverlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close" id="inputModalClose">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                        <input type="text" id="inputModalInput" value="${defaultValue}" placeholder="请输入...">
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" id="inputModalCancel">取消</button>
                        <button class="btn btn-primary" id="inputModalConfirm">确定</button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const overlay = document.getElementById('inputModalOverlay');
        const input = document.getElementById('inputModalInput');
        const confirmBtn = document.getElementById('inputModalConfirm');
        const cancelBtn = document.getElementById('inputModalCancel');
        const closeBtn = document.getElementById('inputModalClose');

        // 聚焦输入框并选中文本
        input.focus();
        input.select();

        // 确定按钮事件
        const handleConfirm = () => {
            const value = input.value.trim();
            overlay.remove();
            if (value && callback) {
                callback(value);
            }
        };

        // 取消按钮事件
        const handleCancel = () => {
            overlay.remove();
        };

        // 绑定事件
        confirmBtn.addEventListener('click', handleConfirm);
        cancelBtn.addEventListener('click', handleCancel);
        closeBtn.addEventListener('click', handleCancel);

        // 回车确认，ESC取消
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleConfirm();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                handleCancel();
            }
        });

        // 点击遮罩层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                handleCancel();
            }
        });
    }

    // 添加通用的模态框方法
    showModal(htmlContent) {
        // 移除现有的模态框
        this.closeModal();

        const modalHtml = `
            <div class="modal-overlay" id="generalModalOverlay">
                ${htmlContent}
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 添加ESC键关闭功能
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    }

    closeModal() {
        const modal = document.getElementById('generalModalOverlay');
        if (modal) {
            modal.remove();
        }
    }

    showPrompt(title, message, defaultValue = '') {
        return new Promise((resolve) => {
            const modalHtml = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close" onclick="organizationApp.uiManager.closeModal(); organizationApp.uiManager._promptResolve(null)">×</button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                        <input type="text" id="promptInput" value="${defaultValue}" placeholder="请输入...">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="organizationApp.uiManager.closeModal(); organizationApp.uiManager._promptResolve(null)">取消</button>
                        <button type="button" class="btn btn-primary" onclick="organizationApp.uiManager.submitPrompt()">确定</button>
                    </div>
                </div>
            `;

            this.showModal(modalHtml);

            // 保存resolve函数
            this._promptResolve = resolve;

            // 聚焦输入框
            setTimeout(() => {
                const input = document.getElementById('promptInput');
                if (input) {
                    input.focus();
                    input.select();

                    // 回车确认
                    input.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            this.submitPrompt();
                        }
                    });
                }
            }, 100);
        });
    }

    submitPrompt() {
        const input = document.getElementById('promptInput');
        const value = input ? input.value.trim() : '';

        if (this._promptResolve) {
            this._promptResolve(value || null);
            this._promptResolve = null;
        }

        this.closeModal();
    }

    showConfirm(title, message, confirmText = '确定', type = 'primary') {
        return new Promise((resolve) => {
            const modalHtml = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close" onclick="organizationApp.uiManager.closeModal(); organizationApp.uiManager._confirmResolve(false)">×</button>
                    </div>
                    <div class="modal-body">
                        <p style="white-space: pre-line;">${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="organizationApp.uiManager.closeModal(); organizationApp.uiManager._confirmResolve(false)">取消</button>
                        <button type="button" class="btn btn-${type}" onclick="organizationApp.uiManager.submitConfirm(true)">${confirmText}</button>
                    </div>
                </div>
            `;

            this.showModal(modalHtml);

            // 保存resolve函数
            this._confirmResolve = resolve;
        });
    }

    submitConfirm(result) {
        if (this._confirmResolve) {
            this._confirmResolve(result);
            this._confirmResolve = null;
        }

        this.closeModal();
    }

    bindEvents() {
        // 左侧栏 Tab 切换
        document.getElementById('materialsTab')?.addEventListener('click', () => this.switchLeftTab('materials'));
        document.getElementById('filesTab')?.addEventListener('click', () => this.switchLeftTab('files'));
        
        // 右侧栏切换
        document.getElementById('previewToggleBtn')?.addEventListener('click', () => this.switchRightPanel('preview'));
        document.getElementById('aiToggleBtn')?.addEventListener('click', () => this.switchRightPanel('ai'));
        
        // 侧边栏切换按钮
        document.getElementById('toggleLeftSidebar')?.addEventListener('click', () => this.toggleLeftSidebar());
        document.getElementById('toggleRightSidebar')?.addEventListener('click', () => this.toggleRightSidebar());
        
        // 模态框关闭
        document.addEventListener('click', (e) => {
            if (e.target.id === 'modal') CoreUtils.hideModal();
        });
        
        // 键盘快捷键
        this.bindKeyboardShortcuts();
        
        // 窗口大小变化
        window.addEventListener('resize', () => this.handleWindowResize());
    }

    bindKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // 保存快捷键
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.app.saveDocument();
            }
            
            // 搜索快捷键
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                this.app.toggleMaterialsSearch();
            }
            
            // 新建快捷键
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                this.app.addNewMaterial();
            }
            
            // 打开快捷键
            if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
                e.preventDefault();
                // 打开文件对话框
            }
            
            // 预览快捷键
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                this.switchRightPanel('preview');
            }
            
            // ESC 关闭模态框
            if (e.key === 'Escape') {
                CoreUtils.hideModal();
            }
        });
    }

    switchLeftTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(tabName + 'Tab')?.classList.add('active');
        
        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName + 'TabContent')?.classList.add('active');
        
        this.currentLeftTab = tabName;
        
        // 加载对应数据
        if (tabName === 'materials') {
            this.app.materialsManager.loadMaterials();
        } else if (tabName === 'files') {
            this.app.filesManager.loadFiles();
        }
    }

    switchRightPanel(panelName) {
        // 更新按钮状态
        document.querySelectorAll('.panel-toggle').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(panelName + 'ToggleBtn')?.classList.add('active');
        
        // 更新面板显示
        document.querySelectorAll('.right-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(panelName + 'Panel')?.classList.add('active');
        
        this.currentRightPanel = panelName;
        
        // 根据面板类型执行相应操作
        if (panelName === 'preview') {
            this.updatePreview();
        } else if (panelName === 'ai') {
            this.app.aiManager.initializeAIPanel();
        }
    }

    toggleLeftSidebar() {
        const leftSidebar = document.querySelector('.left-sidebar');
        const toggleBtn = document.getElementById('toggleLeftSidebar');
        
        if (leftSidebar && toggleBtn) {
            this.isLeftSidebarVisible = !this.isLeftSidebarVisible;
            leftSidebar.classList.toggle('hidden', !this.isLeftSidebarVisible);
            toggleBtn.title = this.isLeftSidebarVisible ? '隐藏左侧栏' : '显示左侧栏';
            
            // --- BEGIN FIX ---
            // 切换图标以提供清晰的视觉反馈
            if (this.isLeftSidebarVisible) {
                toggleBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3 9h14V7H3v2zm0 4h14v-2H3v2zm0 4h14v-2H3v2zm16 0h2v-2h-2v2zm0-10v2h2V7h-2zm0 6h2v-2h-2v2z"/></svg>`;
            } else {
                toggleBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M4 18h16v-2H4v2zm0-5h16v-2H4v2zm0-5h16V6H4v2z"/></svg>`;
            }
            // --- END FIX ---
        }
    }

    toggleRightSidebar() {
        const rightSidebar = document.querySelector('.right-sidebar');
        const toggleBtn = document.getElementById('toggleRightSidebar');
        
        if (rightSidebar && toggleBtn) {
            this.isRightSidebarVisible = !this.isRightSidebarVisible;
            rightSidebar.classList.toggle('hidden', !this.isRightSidebarVisible);
            toggleBtn.title = this.isRightSidebarVisible ? '隐藏右侧栏' : '显示右侧栏';

            // --- BEGIN FIX ---
            // 切换图标以提供清晰的视觉反馈
            if (this.isRightSidebarVisible) {
                toggleBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3 9h14V7H3v2zm0 4h14v-2H3v2zm0 4h14v-2H3v2zm16 0h2v-2h-2v2zm0-10v2h2V7h-2zm0 6h2v-2h-2v2z"/></svg>`;
            } else {
                toggleBtn.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M4 18h16v-2H4v2zm0-5h16v-2H4v2zm0-5h16V6H4v2z"/></svg>`;
            }
            // --- END FIX ---
        }
    }

    setupResizers() {
        const leftResizer = document.querySelector('.resizer.left-resizer');
        const rightResizer = document.querySelector('.resizer.right-resizer');

        if (leftResizer) {
            leftResizer.addEventListener('mousedown', (e) => this.startResize(e, 'left'));
        }

        if (rightResizer) {
            rightResizer.addEventListener('mousedown', (e) => this.startResize(e, 'right'));
        }
    }

    startResize(e, direction) {
        e.preventDefault();
        this.isResizing = true;
        
        const startX = e.clientX;
        const leftSidebar = document.querySelector('.left-sidebar');
        const rightSidebar = document.querySelector('.right-sidebar');
        const startLeftWidth = leftSidebar ? leftSidebar.offsetWidth : 0;
        const startRightWidth = rightSidebar ? rightSidebar.offsetWidth : 0;
        
        const handleMouseMove = (e) => {
            if (!this.isResizing) return;
            
            const deltaX = e.clientX - startX;
            
            if (direction === 'left' && leftSidebar) {
                const newWidth = Math.max(200, Math.min(600, startLeftWidth + deltaX));
                leftSidebar.style.width = newWidth + 'px';
            } else if (direction === 'right' && rightSidebar) {
                const newWidth = Math.max(200, Math.min(600, startRightWidth - deltaX));
                rightSidebar.style.width = newWidth + 'px';
            }
        };
        
        const handleMouseUp = () => {
            this.isResizing = false;
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            document.body.style.cursor = '';
            document.body.style.userSelect = '';
        };
        
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';
    }

    updatePreview() {
        const editor = document.getElementById('markdownEditor');
        const preview = document.getElementById('markdownPreview');
        
        if (editor && preview && this.currentRightPanel === 'preview') {
            const markdown = editor.value;
            preview.innerHTML = this.markdownToHtml(markdown);
        }
    }

    markdownToHtml(markdown) {
        if (!markdown) return '';

        // 改进的 Markdown 转换
        let html = markdown;

        // 代码块（必须在其他处理之前）
        html = html.replace(/```([^`]*)\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>');
        html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

        // 行内代码
        html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

        // 标题
        html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
        html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
        html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

        // 引用
        html = html.replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>');

        // 粗体和斜体
        html = html.replace(/\*\*\*([^*]+)\*\*\*/g, '<strong><em>$1</em></strong>');
        html = html.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
        html = html.replace(/\*([^*]+)\*/g, '<em>$1</em>');

        // 链接和图片
        html = html.replace(/!\[([^\]]*)\]\(([^)]*)\)/g, '<img alt="$1" src="$2" style="max-width: 100%; height: auto;" />');
        html = html.replace(/\[([^\]]*)\]\(([^)]*)\)/g, '<a href="$2" target="_blank">$1</a>');

        // 列表
        html = html.replace(/^\* (.+)$/gim, '<li>$1</li>');
        html = html.replace(/^- (.+)$/gim, '<li>$1</li>');
        html = html.replace(/^(\d+)\. (.+)$/gim, '<li>$2</li>');

        // 包装列表项
        html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

        // 水平线
        html = html.replace(/^---$/gim, '<hr>');
        html = html.replace(/^\*\*\*$/gim, '<hr>');

        // 段落（处理换行）
        html = html.replace(/\n\n/g, '</p><p>');
        html = html.replace(/\n/g, '<br>');

        // 包装段落
        if (html && !html.startsWith('<')) {
            html = '<p>' + html + '</p>';
        }

        // 清理空段落
        html = html.replace(/<p><\/p>/g, '');
        html = html.replace(/<p>\s*<\/p>/g, '');

        return html;
    }

    updateWordCount() {
        const editor = document.getElementById('markdownEditor');
        const wordCountEl = document.getElementById('wordCount');
        const charCountEl = document.getElementById('charCount');
        
        if (!editor) return;
        
        const content = editor.value;
        const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
        const charCount = content.length;
        
        if (wordCountEl) wordCountEl.textContent = `${wordCount} 字`;
        if (charCountEl) charCountEl.textContent = `${charCount} 字符`;
    }

    updateStatusBar() {
        const statusBar = document.getElementById('statusBar');
        if (!statusBar) return;
        
        const materialsCount = this.app.materials ? this.app.materials.length : 0;
        const filesCount = this.app.files ? this.app.files.length : 0;
        
        statusBar.innerHTML = `
            <span>素材: ${materialsCount}</span>
            <span>文件: ${filesCount}</span>
            <span id="wordCount">0 字</span>
            <span id="charCount">0 字符</span>
        `;
    }

    handleWindowResize() {
        // 处理窗口大小变化
        if (window.innerWidth < 768) {
            // 移动端适配
            if (this.isLeftSidebarVisible && this.isRightSidebarVisible) {
                this.toggleRightSidebar();
            }
        }
    }

    showContextMenu(e, items) {
        e.preventDefault();
        
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.position = 'fixed';
        menu.style.left = e.clientX + 'px';
        menu.style.top = e.clientY + 'px';
        menu.style.zIndex = '10000';
        
        const menuItems = items.map(item => {
            if (item.separator) {
                return '<div class="menu-separator"></div>';
            }
            return `<div class="menu-item ${item.className || ''}" onclick="${item.action}">${item.icon || ''}${item.label}</div>`;
        }).join('');
        
        menu.innerHTML = menuItems;
        document.body.appendChild(menu);
        
        const removeMenu = () => {
            if (menu.parentNode) {
                menu.parentNode.removeChild(menu);
            }
            document.removeEventListener('click', removeMenu);
        };
        
        setTimeout(() => {
            document.addEventListener('click', removeMenu);
        }, 100);
    }

    destroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        
        if (this.markdownEditor) {
            this.markdownEditor.destroy();
        }
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIManager;
} else {
    window.UIManager = UIManager;
} 