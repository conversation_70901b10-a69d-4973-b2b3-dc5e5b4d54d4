/* 现代苹果风格快速收藏界面 */

/* 重置和基础样式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
    background: transparent;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 主窗口容器 */
.capture-window {
    width: 100%;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;
    box-shadow: 
        0 32px 64px rgba(0, 0, 0, 0.15),
        0 16px 32px rgba(0, 0, 0, 0.1);
    animation: windowAppearFromTop 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes windowAppearFromTop {
    0% {
        opacity: 0;
        transform: scale(0.9) translateY(-40px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes windowAppear {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 窗口标题栏 */
.window-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px 12px 0 0;
}

.window-title {
    font-size: 16px;
    font-weight: 600;
    color: #1d1d1f;
    letter-spacing: -0.01em;
}

.close-button {
    width: 24px;
    height: 24px;
    border: none;
    background: rgba(142, 142, 147, 0.12);
    border-radius: 6px;
    color: #8e8e93;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease;
}

.close-button:hover {
    background: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 20px 20px 0;
    overflow: hidden;
}

/* 内容预览区域 */
.content-preview {
    margin-bottom: 12px;
    padding: 10px;
    background: rgba(0, 122, 255, 0.05);
    border: 1px solid rgba(0, 122, 255, 0.15);
    border-radius: 8px;
    animation: previewSlideIn 0.3s ease;
    max-height: 120px;
    overflow: hidden;
}

@keyframes previewSlideIn {
    from {
        opacity: 0;
        height: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
    to {
        opacity: 1;
        height: auto;
        padding-top: 12px;
        padding-bottom: 12px;
    }
}

.preview-label {
    font-size: 12px;
    font-weight: 500;
    color: #007aff;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.preview-content {
    font-size: 14px;
    color: #1d1d1f;
    line-height: 1.4;
}

.preview-content img {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    margin: 4px 0;
}

.preview-content a {
    color: #007aff;
    text-decoration: none;
}

.preview-content a:hover {
    text-decoration: underline;
}

.preview-content pre {
    background: rgba(0, 0, 0, 0.05);
    padding: 8px;
    border-radius: 4px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 13px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.preview-content code {
    font-family: inherit;
}

/* 图片预览优化 */
.image-preview-container {
    position: relative;
    max-height: 80px;
    overflow-y: auto;
    border-radius: 6px;
}

.preview-image {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.preview-image:hover {
    opacity: 0.9;
    cursor: pointer;
}

/* Lightbox样式 */
.image-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10000;
    animation: lightboxFadeIn 0.3s ease;
}

@keyframes lightboxFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.lightbox-backdrop {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-close {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 36px;
    height: 36px;
    background: rgba(0, 0, 0, 0.8);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 1;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.lightbox-close:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.05);
}

.lightbox-close svg {
    pointer-events: none;
}

.lightbox-image {
    max-width: 90vw;
    max-height: 90vh;
    width: auto;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    object-fit: contain;
}

/* 输入区域 */
.input-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.content-input {
    width: 100%;
    min-height: 140px;
    padding: 16px;
    border: 2px solid rgba(0, 0, 0, 0.06);
    border-radius: 10px;
    font-size: 15px;
    font-family: inherit;
    line-height: 1.5;
    color: #1d1d1f;
    background: rgba(255, 255, 255, 0.8);
    resize: vertical;
    transition: all 0.2s ease;
    outline: none;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.content-input:focus {
    border-color: #007aff;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    background: rgba(255, 255, 255, 0.95);
}

.content-input::placeholder {
    color: #8e8e93;
}

/* 标签输入系统 */
.tag-section {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding-bottom: 32px;
}

.tag-label {
    font-size: 13px;
    font-weight: 600;
    color: #1d1d1f;
    padding-left: 2px;
}

.recent-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
    margin-bottom: 20px;
    min-height: 20px;
}

.recent-tag {
    display: inline-flex;
    align-items: center;
    background: #f2f2f7;
    color: #1d1d1f;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    border: 1px solid #e5e5ea;
    gap: 4px;
}

.recent-tag:hover {
    background: #007aff;
    color: white;
    border-color: #007aff;
    transform: translateY(-1px);
}

.recent-tag.selected {
    background: #007aff;
    color: white;
    border-color: #007aff;
}

.recent-tag-name {
    font-weight: 500;
}

.recent-tag-count {
    font-size: 10px;
    background: #e5e5ea;
    color: #8e8e93;
    padding: 1px 4px;
    border-radius: 6px;
    min-width: 14px;
    text-align: center;
    font-weight: 600;
}

.recent-tag:hover .recent-tag-count {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

.tag-input-container {
    position: relative;
    border: 2px solid rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.2s ease;
    min-height: 44px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 8px 12px;
    gap: 6px;
}

.tag-input-container:focus-within {
    border-color: #007aff;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    background: rgba(255, 255, 255, 0.95);
}

.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
}

.tag-item {
    display: inline-flex;
    align-items: center;
    background: rgba(0, 122, 255, 0.1);
    color: #007aff;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    gap: 4px;
    animation: tagAppear 0.2s ease;
}

@keyframes tagAppear {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.tag-remove {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: background 0.15s ease;
}

.tag-remove:hover {
    background: rgba(0, 122, 255, 0.2);
}

.tag-input {
    flex: 1;
    min-width: 120px;
    border: none;
    outline: none;
    background: transparent;
    font-size: 14px;
    font-family: inherit;
    color: #1d1d1f;
    padding: 4px 0;
}

.tag-input::placeholder {
    color: #8e8e93;
}

.tag-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    margin-top: 4px;
}

.tag-suggestion {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.15s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tag-suggestion:hover,
.tag-suggestion.selected {
    background: rgba(0, 122, 255, 0.1);
}

.tag-suggestion-text {
    flex: 1;
}

.tag-suggestion-count {
    font-size: 12px;
    color: #8e8e93;
}

/* 操作栏 */
.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0 0 12px 12px;
}

.shortcuts-hint {
    font-size: 12px;
    color: #8e8e93;
    font-weight: 400;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

/* 按钮样式 */
.btn-primary {
    padding: 8px 16px;
    background: #007aff;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.15s ease;
    min-width: 80px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-primary:hover {
    background: #0056d3;
    transform: translateY(-1px);
}

.btn-primary:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

.btn-primary:disabled {
    background: #8e8e93;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    padding: 8px 16px;
    background: rgba(142, 142, 147, 0.12);
    color: #1d1d1f;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    min-width: 60px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-secondary:hover {
    background: rgba(142, 142, 147, 0.2);
}

.btn-secondary:active {
    background: rgba(142, 142, 147, 0.3);
}

/* 成功覆盖层 */
.success-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.success-content {
    text-align: center;
    animation: successAppear 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes successAppear {
    0% {
        opacity: 0;
        transform: scale(0.7) translateY(30px);
    }
    70% {
        opacity: 1;
        transform: scale(1.05) translateY(-5px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.success-checkmark {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
}

.success-checkmark svg {
    animation: checkmarkDraw 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes checkmarkDraw {
    0% {
        transform: scale(0) rotate(-45deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(0deg);
        opacity: 1;
    }
    75% {
        transform: scale(0.95) rotate(5deg);
        opacity: 1;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

.success-text {
    font-size: 18px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 24px;
    letter-spacing: -0.01em;
}

.success-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-bottom: 16px;
}

.success-close {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 24px;
    height: 24px;
    background: rgba(142, 142, 147, 0.12);
    border: none;
    border-radius: 50%;
    color: #8e8e93;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease;
}

.success-close:hover {
    background: rgba(142, 142, 147, 0.2);
    color: #1d1d1f;
}

.success-hint {
    font-size: 12px;
    color: #8e8e93;
    text-align: center;
}

/* 加载状态 */
.capture-window.loading .btn-primary {
    background: #8e8e93;
    pointer-events: none;
    position: relative;
}

.capture-window.loading .btn-primary::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .capture-window {
        background: rgba(28, 28, 30, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .window-header {
        background: rgba(28, 28, 30, 0.8);
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }
    
    .window-title {
        color: #f2f2f7;
    }
    
    .content-input,
    .source-input {
        background: rgba(44, 44, 46, 0.8);
        border-color: rgba(255, 255, 255, 0.1);
        color: #f2f2f7;
    }
    
    .content-input:focus,
    .source-input:focus {
        background: rgba(44, 44, 46, 0.95);
    }
    
    .action-bar {
        background: rgba(28, 28, 30, 0.8);
        border-top-color: rgba(255, 255, 255, 0.1);
    }
    
    .btn-secondary {
        background: rgba(99, 99, 102, 0.2);
        color: #f2f2f7;
    }
    
    .success-overlay {
        background: rgba(28, 28, 30, 0.98);
    }
    
    .success-text {
        color: #f2f2f7;
    }
}

/* 响应式调整 */
@media (max-width: 480px) {
    .capture-window {
        border-radius: 0;
        height: 100vh;
    }
    
    .window-header,
    .content-area,
    .action-bar {
        padding-left: 16px;
        padding-right: 16px;
    }
}