/* 设置窗口样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f8f9fa;
    overflow: hidden;
}

.settings-container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

.settings-sidebar {
    width: 200px;
    background-color: #fff;
    border-right: 1px solid #e0e6ed;
    flex-shrink: 0;
}

.settings-nav {
    padding: 16px 0;
}

.nav-item {
    padding: 12px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s ease;
    border-right: 3px solid transparent;
}

.nav-item:hover {
    background-color: #f7fafc;
}

.nav-item.active {
    background-color: #ebf8ff;
    border-right-color: #3182ce;
    color: #2b6cb0;
}

.nav-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
}

.settings-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background-color: #fff;
}

.settings-section {
    display: none;
    max-width: 600px;
}

.settings-section.active {
    display: block;
}

.settings-section h2 {
    font-size: 24px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e2e8f0;
}

.setting-group {
    margin-bottom: 24px;
}

.setting-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 8px;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 4px 0;
    user-select: none;
}

.setting-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    background-color: #fff;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
    background-color: #3182ce;
    border-color: #3182ce;
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 5px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.setting-label:hover .checkmark {
    border-color: #a0aec0;
}

.setting-label input[type="checkbox"]:checked:hover + .checkmark {
    border-color: #2c5aa0;
}

.setting-group input[type="text"],
.setting-group input[type="password"],
.setting-group input[type="number"],
.setting-group input[type="email"],
.setting-group input[type="url"],
.setting-group select,
.setting-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
    background: white;
    transition: all 0.2s ease;
    outline: none;
}

.setting-group input:focus,
.setting-group select:focus,
.setting-group textarea:focus {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.setting-group input[type="range"] {
    width: calc(100% - 60px);
    margin-right: 12px;
}

.setting-group input[type="range"] + span {
    display: inline-block;
    width: 48px;
    text-align: center;
    font-size: 14px;
    color: #4a5568;
    font-weight: 500;
}

.setting-group select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 36px;
    appearance: none;
}

.setting-group input::placeholder,
.setting-group textarea::placeholder {
    color: #a0aec0;
}

.action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn-primary {
    padding: 10px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    padding: 10px 16px;
    background: #f7fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.btn-secondary:active {
    transform: translateY(0);
}

.btn-danger {
    padding: 10px 16px;
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-danger:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-footer {
    padding: 20px 24px;
    background: #f8f9fa;
    border-top: 1px solid #e0e6ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.footer-actions {
    display: flex;
    gap: 12px;
}

/* 特殊样式 */
.setting-group.inline {
    display: flex;
    align-items: center;
    gap: 12px;
}

.setting-group.inline label {
    margin-bottom: 0;
    white-space: nowrap;
}

.setting-group.inline input,
.setting-group.inline select {
    width: auto;
    min-width: 120px;
}

/* 描述文本 */
.setting-description {
    font-size: 13px;
    color: #718096;
    margin-top: 4px;
    line-height: 1.4;
}

/* 分组标题 */
.setting-group-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e2e8f0;
}

/* 警告样式 */
.setting-warning {
    background: #fef5e7;
    border: 1px solid #f6e05e;
    border-radius: 6px;
    padding: 12px;
    margin-top: 8px;
    font-size: 13px;
    color: #744210;
}

.setting-warning::before {
    content: '⚠️ ';
    margin-right: 4px;
}

/* 成功样式 */
.setting-success {
    background: #f0fff4;
    border: 1px solid #68d391;
    border-radius: 6px;
    padding: 12px;
    margin-top: 8px;
    font-size: 13px;
    color: #22543d;
}

.setting-success::before {
    content: '✓ ';
    margin-right: 4px;
}

/* 错误样式 */
.setting-error {
    background: #fed7d7;
    border: 1px solid #fc8181;
    border-radius: 6px;
    padding: 12px;
    margin-top: 8px;
    font-size: 13px;
    color: #742a2a;
}

.setting-error::before {
    content: '✗ ';
    margin-right: 4px;
}

/* 加载状态 */
.settings-container.loading {
    pointer-events: none;
    opacity: 0.7;
}

.settings-container.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 4px solid #e2e8f0;
    border-top-color: #3182ce;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a202c;
        color: #e2e8f0;
    }
    
    .settings-sidebar,
    .settings-content {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .nav-item:hover {
        background-color: #4a5568;
    }
    
    .nav-item.active {
        background-color: #3182ce;
        color: white;
    }
    
    .settings-section h2 {
        color: #e2e8f0;
        border-color: #4a5568;
    }
    
    .setting-group label {
        color: #cbd5e0;
    }
    
    .checkmark {
        background-color: #4a5568;
        border-color: #718096;
    }
    
    .setting-group input,
    .setting-group select,
    .setting-group textarea {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .setting-group input:focus,
    .setting-group select:focus,
    .setting-group textarea:focus {
        border-color: #63b3ed;
        box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
    }
    
    .setting-group input::placeholder,
    .setting-group textarea::placeholder {
        color: #a0aec0;
    }
    
    .btn-secondary {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .btn-secondary:hover {
        background-color: #718096;
        border-color: #a0aec0;
    }
    
    .settings-footer {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .setting-description {
        color: #a0aec0;
    }
    
    .setting-group-title {
        color: #e2e8f0;
        border-color: #4a5568;
    }
    
    .setting-warning {
        background-color: #744210;
        border-color: #f6e05e;
        color: #fef5e7;
    }
    
    .setting-success {
        background-color: #22543d;
        border-color: #68d391;
        color: #f0fff4;
    }
    
    .setting-error {
        background-color: #742a2a;
        border-color: #fc8181;
        color: #fed7d7;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .settings-container {
        flex-direction: column;
    }
    
    .settings-sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid #e0e6ed;
    }
    
    .settings-nav {
        display: flex;
        padding: 8px 0;
        overflow-x: auto;
    }
    
    .nav-item {
        padding: 8px 16px;
        white-space: nowrap;
        border-right: none;
        border-bottom: 3px solid transparent;
    }
    
    .nav-item.active {
        border-bottom-color: #3182ce;
        border-right-color: transparent;
    }
    
    .settings-content {
        padding: 16px;
    }
    
    .settings-section h2 {
        font-size: 20px;
        margin-bottom: 16px;
    }
    
    .setting-group {
        margin-bottom: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons button {
        width: 100%;
    }
    
    .settings-footer {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }
    
    .footer-actions {
        width: 100%;
        justify-content: center;
    }
}

/* 滚动条样式 */
.settings-content::-webkit-scrollbar {
    width: 8px;
}

.settings-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.settings-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.settings-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 键盘焦点样式 */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
    outline: 2px solid #3182ce;
    outline-offset: 2px;
}

/* 禁用状态 */
button:disabled,
input:disabled,
textarea:disabled,
select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 动画效果 */
.settings-section {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 保存指示器 */
.settings-container.saving .settings-footer::after {
    content: '保存中...';
    position: absolute;
    right: 24px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #3182ce;
}

/* 已更改指示器 */
.setting-group.changed {
    background: rgba(49, 130, 206, 0.05);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
    border: 1px solid rgba(49, 130, 206, 0.2);
}

.setting-group.changed::before {
    content: '已修改';
    position: absolute;
    top: -8px;
    left: 8px;
    background: #3182ce;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}