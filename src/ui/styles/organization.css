/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    position: relative;
}

/* 三栏布局 */
.left-sidebar {
    width: 320px;
    min-width: 250px;
    max-width: 500px;
    background-color: #fff;
    border-right: 1px solid #e0e6ed;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: width 0.3s ease, margin-left 0.3s ease;
}

.left-sidebar.hidden {
    width: 0;
    min-width: 0;
    border-right: none;
    overflow: hidden;
    padding-left: 0;
    padding-right: 0;
}

.editor-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 400px;
    background-color: #fff;
    position: relative;
}

.right-sidebar {
    width: 380px;
    min-width: 300px;
    max-width: 600px;
    background-color: #f8f9fa;
    border-left: 1px solid #e0e6ed;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: width 0.3s ease, margin-right 0.3s ease;
}

.right-sidebar.hidden {
    width: 0;
    min-width: 0;
    border-left: none;
    overflow: hidden;
    padding-left: 0;
    padding-right: 0;
}

/* 调整器 */
.resizer {
    width: 4px;
    background-color: #e0e6ed;
    cursor: col-resize;
    position: relative;
    transition: background-color 0.2s;
}

.resizer:hover {
    background-color: #3182ce;
}

.resizer.resizing {
    background-color: #3182ce;
}

.left-resizer {
    margin-right: 0;
}

.right-resizer {
    margin-left: 0;
}

/* 左侧栏样式 */
.sidebar-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e6ed;
}

.sidebar-tabs {
    display: flex;
}

.tab-button {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #4a5568;
    border-bottom: 3px solid transparent;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-button:hover {
    background-color: #e2e8f0;
    color: #2d3748;
}

.tab-button.active {
    color: #3182ce;
    border-bottom-color: #3182ce;
    background-color: #ebf8ff;
}

.tab-content {
    flex: 1;
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.tab-content.active {
    display: flex;
}

.tab-controls {
    padding: 12px 16px;
    border-bottom: 1px solid #e0e6ed;
    background-color: #fff;
}

.tab-actions {
    display: flex;
    gap: 4px;
    justify-content: flex-end;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s;
    color: #4a5568;
}

.btn-icon:hover {
    background-color: #e2e8f0;
    color: #2d3748;
}

.btn-icon.active {
    background-color: #3182ce;
    color: white;
}

/* 搜索和过滤 */
.search-container {
    padding: 12px 16px;
    border-bottom: 1px solid #e0e6ed;
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #fff;
}

.search-container input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.search-container input:focus {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

/* 持久搜索容器样式 */
.persistent-search-container {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    background: #ffffff;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.search-input-wrapper:focus-within {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    background: #ffffff;
}

.search-icon {
    margin-left: 12px;
    color: #6c757d;
    flex-shrink: 0;
    transition: color 0.2s ease;
}

.search-input-wrapper:focus-within .search-icon {
    color: #007bff;
}

.persistent-search-container input {
    flex: 1;
    padding: 10px 12px;
    border: none;
    background: transparent;
    font-size: 14px;
    color: #495057;
    outline: none;
}

.persistent-search-container input::placeholder {
    color: #6c757d;
}

.clear-search-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    margin-right: 6px;
    border: none;
    background: none;
    border-radius: 4px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.clear-search-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.filter-container {
    padding: 12px 16px;
    border-bottom: 1px solid #e0e6ed;
    display: flex;
    gap: 8px;
    background-color: #fff;
}

.filter-container select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    outline: none;
    transition: border-color 0.2s;
}

.filter-container select:focus {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

/* 批量操作 */
.batch-actions {
    padding: 12px 16px;
    border-bottom: 1px solid #e0e6ed;
    display: flex;
    gap: 8px;
    background-color: #fff8dc;
}

.btn-secondary {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    background-color: #fff;
    color: #4a5568;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-secondary:hover {
    background-color: #f7fafc;
    border-color: #a0aec0;
}

.btn-primary {
    padding: 6px 12px;
    border: 1px solid #3182ce;
    background-color: #3182ce;
    color: white;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary:hover {
    background-color: #2c5aa0;
    border-color: #2c5aa0;
}

.btn-danger {
    padding: 6px 12px;
    border: 1px solid #e53e3e;
    background-color: #e53e3e;
    color: white;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-danger:hover {
    background-color: #c53030;
    border-color: #c53030;
}

/* 素材列表 */
.materials-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    background-color: #fafbfc;
}

.material-item {
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 素材类型角标 */
.material-type-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    z-index: 2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.material-type-badge.material-type-text {
    background-color: #4299e1; /* 蓝色 - 文本 */
}

.material-type-badge.material-type-image {
    background-color: #48bb78; /* 绿色 - 图片 */
}

.material-type-badge.material-type-link {
    background-color: #ed8936; /* 橙色 - 链接 */
}

.material-type-badge.material-type-code {
    background-color: #9f7aea; /* 紫色 - 代码 */
}

.material-type-badge.material-type-video {
    background-color: #f56565; /* 红色 - 视频 */
}

.material-type-badge.material-type-audio {
    background-color: #38b2ac; /* 青色 - 音频 */
}

/* 素材文件夹样式 */
.material-folder {
    margin-bottom: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background-color: #fff;
    overflow: hidden;
}

.material-folder .folder-header {
    padding: 12px 16px;
    background-color: #f7fafc;
    border-bottom: 1px solid #e2e8f0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s ease;
}

.material-folder .folder-header:hover {
    background-color: #edf2f7;
}

.material-folder .folder-icon {
    color: #4299e1;
    flex-shrink: 0;
}

.material-folder .folder-name {
    flex-grow: 1;
    font-weight: 500;
    color: #2d3748;
}

.material-folder .folder-count {
    color: #718096;
    font-size: 12px;
}

.material-folder .folder-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.material-folder:hover .folder-actions {
    opacity: 1;
}

.material-folder .folder-content {
    padding: 8px;
    background-color: #fafbfc;
}

.material-folder .folder-content .material-item {
    margin-left: 16px;
    margin-bottom: 8px;
}

.material-folder .folder-content .material-item:last-child {
    margin-bottom: 0;
}

/* 素材文件夹拖拽样式 */
.material-folder[draggable="true"] .folder-header {
    cursor: grab;
}

.material-folder.dragging {
    opacity: 0.5;
    transform: scale(0.95);
}

.material-folder.dragging .folder-header {
    cursor: grabbing;
}

/* 搜索功能样式 */
.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-wrapper input {
    flex: 1;
    padding-right: 32px;
}

.search-clear-btn {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.search-clear-btn:hover {
    background-color: #edf2f7;
    color: #4a5568;
}

.search-stats {
    font-size: 12px;
    color: #718096;
    padding: 4px 8px;
    background-color: #f7fafc;
    border-radius: 4px;
    margin-top: 4px;
}

/* 创建素材模态框样式 */
.modal-content .form-group {
    margin-bottom: 16px;
}

.modal-content .form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #2d3748;
}

.modal-content .form-group input,
.modal-content .form-group select,
.modal-content .form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.modal-content .form-group input:focus,
.modal-content .form-group select:focus,
.modal-content .form-group textarea:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.modal-content .form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* 通用模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideIn 0.2s ease;
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #718096;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: #edf2f7;
    color: #4a5568;
}

.modal-body {
    padding: 20px 24px;
}

.modal-body p {
    margin: 0 0 16px 0;
    color: #4a5568;
    line-height: 1.5;
}

.modal-body input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.modal-body input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background-color: #4299e1;
    color: white;
}

.btn-primary:hover {
    background-color: #3182ce;
}

.btn-secondary {
    background-color: #edf2f7;
    color: #4a5568;
}

.btn-secondary:hover {
    background-color: #e2e8f0;
}

.btn-danger {
    background-color: #f56565;
    color: white;
}

.btn-danger:hover {
    background-color: #e53e3e;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.material-item:hover {
    border-color: #cbd5e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.material-item.selected {
    border-color: #3182ce;
    background-color: #ebf8ff;
}

.material-item.dragging {
    opacity: 0.5;
    transform: scale(0.95);
}

.material-header {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: space-between;
}

.material-checkbox {
    flex-shrink: 0;
}

.material-type {
    flex-shrink: 0;
    color: #6c757d;
}

.material-title {
    flex-grow: 1;
    font-weight: 500;
    color: #2d3748;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.material-actions {
    flex-shrink: 0;
    display: flex;
    gap: 4px;
}

.material-content {
    font-size: 13px;
    color: #4a5568;
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.material-preview img {
    max-width: 100%;
    height: auto;
    display: block;
    margin-top: 4px;
}

.material-meta {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: #718096;
}

.files-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    background-color: #fafbfc;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    user-select: none;
}

.file-item:hover {
    background-color: #f7fafc;
}

.file-item.selected {
    background-color: #ebf8ff;
    color: #3182ce;
}

.folder-item {
    margin-bottom: 4px;
}

.folder-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-weight: 500;
}

.folder-header:hover {
    background-color: #f7fafc;
}

.folder-content {
    margin-left: 16px;
    border-left: 1px solid #e2e8f0;
    padding-left: 8px;
}

/* 工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    border-bottom: 1px solid #e0e6ed;
    background-color: #fff;
    min-height: 48px;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.separator {
    width: 1px;
    height: 24px;
    background-color: #e0e6ed;
    margin: 0 4px;
}



/* 编辑器 */
.editor-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.editor-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #fff;
}

.editor-placeholder {
    color: #a0aec0;
    font-style: italic;
    text-align: center;
    margin-top: 50px;
}

/* 状态栏 */
.status-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    border-top: 1px solid #e0e6ed;
    background-color: #f8f9fa;
    font-size: 12px;
    color: #4a5568;
    min-height: 32px;
}

.status-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 右侧栏 */
.right-panel {
    display: none;
    flex-direction: column;
    height: 100%;
}

.right-panel.active {
    display: flex;
}

.panel-header {
    padding: 16px;
    border-bottom: 1px solid #e0e6ed;
    background-color: #fff;
}

.panel-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 预览面板 */
.preview-placeholder {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
    margin-top: 50px;
    padding: 20px;
}

#previewContent {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #fff;
}

/* AI面板 */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e2e8f0;
    background-color: #fff;
}

.panel-header h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.panel-header-actions {
    display: flex;
    gap: 8px;
}

.ai-chat-history {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #fff;
}

.ai-welcome {
    text-align: center;
    padding: 40px 20px;
    color: #4a5568;
}

.ai-welcome-icon {
    margin-bottom: 16px;
    color: #3182ce;
}

.ai-welcome h3 {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.ai-welcome p {
    font-size: 14px;
    color: #718096;
    margin-bottom: 24px;
}

.ai-capabilities {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 16px;
}

.ai-capability {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background-color: #f7fafc;
    border-radius: 8px;
    font-size: 13px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.ai-capability:hover {
    background-color: #ebf8ff;
    border-color: #3182ce;
    transform: translateY(-1px);
}

.capability-icon {
    font-size: 16px;
}

/* 选中文本处理器 */
.selected-text-processor {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 0;
}

.processor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
    background-color: #fff;
}

.processor-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
}

.text-length {
    font-size: 12px;
    color: #718096;
    font-weight: 400;
}

.processor-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: #718096;
    transition: all 0.2s ease;
}

.processor-close:hover {
    background-color: #f7fafc;
    color: #e53e3e;
}

.processor-content {
    padding: 16px;
}

.selected-text-preview {
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    font-size: 13px;
    line-height: 1.5;
    color: #2d3748;
    max-height: 120px;
    overflow-y: auto;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background-color: #fff;
    color: #4a5568;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-action-btn:hover {
    background-color: #f7fafc;
    border-color: #3182ce;
    color: #3182ce;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-action-btn:active {
    transform: translateY(0);
}

.quick-action-btn svg {
    flex-shrink: 0;
}

/* 文本处理器 */
.selected-text-processor {
    border-top: 1px solid #e0e6ed;
    background-color: #f8f9fa;
    padding: 12px;
}

.processor-header {
    font-size: 12px;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 8px;
}

.processor-preview {
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
    max-height: 60px;
    overflow-y: auto;
    margin-bottom: 8px;
}

.text-processing-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
}

.text-processing-buttons button {
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    background-color: #fff;
    color: #4a5568;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.text-processing-buttons button:hover {
    background-color: #f7fafc;
    border-color: #3182ce;
    color: #3182ce;
}

/* AI输入区 */
.ai-input-area {
    border-top: 1px solid #e2e8f0;
    padding: 16px;
    background-color: #fff;
}

.ai-input-wrapper {
    position: relative;
    display: flex;
    gap: 8px;
    align-items: flex-end;
}

.ai-input-wrapper textarea {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.5;
    resize: none;
    outline: none;
    transition: all 0.2s ease;
    font-family: inherit;
    background-color: #f8f9fa;
}

.ai-input-wrapper textarea:focus {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
    background-color: #fff;
}

.ai-input-wrapper textarea::placeholder {
    color: #a0aec0;
}

.ai-input-actions {
    display: flex;
    align-items: center;
}

.ai-input-actions .btn-primary {
    padding: 10px 12px;
    border-radius: 10px;
    background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.ai-input-actions .btn-primary:hover {
    background: linear-gradient(135deg, #2c5aa0 0%, #2a4a8a 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(49, 130, 206, 0.3);
}

.ai-input-actions .btn-primary:active {
    transform: translateY(0);
}

.ai-input-actions .btn-primary:disabled {
    background: #e2e8f0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* AI消息 */
.ai-message {
    margin-bottom: 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.ai-message.user {
    align-items: flex-end;
}

.ai-message.assistant {
    align-items: flex-start;
}

.ai-message.system {
    align-items: center;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    font-size: 12px;
    color: #718096;
}

.message-role {
    font-weight: 500;
}

.message-time {
    font-size: 11px;
    color: #a0aec0;
}

.message-content {
    max-width: 85%;
    padding: 12px 16px;
    border-radius: 16px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
    position: relative;
}

.ai-message.user .message-content {
    background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
    color: white;
    border-bottom-right-radius: 4px;
}

.ai-message.assistant .message-content {
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    color: #2d3748;
    border-bottom-left-radius: 4px;
}

.ai-message.system .message-content {
    background-color: #fef5e7;
    border: 1px solid #f6ad55;
    color: #744210;
    border-radius: 8px;
    text-align: center;
    font-size: 13px;
}

.message-content code {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 4px;
    font-family: 'Menlo', 'Monaco', 'Consolas', monospace;
    font-size: 13px;
}

.ai-message.user .message-content code {
    background-color: rgba(255, 255, 255, 0.2);
}

.message-content strong {
    font-weight: 600;
}

.message-content em {
    font-style: italic;
}

.selected-text-quote {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: rgba(255, 255, 255, 0.1);
    border-left: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    font-size: 13px;
    font-style: italic;
    opacity: 0.9;
}

.message-actions {
    display: flex;
    gap: 6px;
    margin-top: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.ai-message:hover .message-actions {
    opacity: 1;
}

.action-btn {
    padding: 6px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background-color: #fff;
    color: #718096;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn:hover {
    background-color: #f7fafc;
    border-color: #3182ce;
    color: #3182ce;
}

.action-btn svg {
    width: 12px;
    height: 12px;
}

/* AI加载状态 */
.ai-loading {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.typing-indicator {
    display: flex;
    gap: 4px;
    padding: 12px 16px;
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    border-bottom-left-radius: 4px;
}

.typing-indicator span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #3182ce;
    animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-8px);
        opacity: 1;
    }
}

.ai-message-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.ai-action-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    background-color: #fff;
    color: #4a5568;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.ai-action-btn:hover {
    background-color: #f7fafc;
    border-color: #3182ce;
    color: #3182ce;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e6ed;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: 60vh;
}

/* 通知 */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.notification {
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(400px);
    opacity: 0;
}

.notification.success {
    border-left: 4px solid #38a169;
}

.notification.error {
    border-left: 4px solid #e53e3e;
}

.notification.warning {
    border-left: 4px solid #ed8936;
}

.notification.info {
    border-left: 4px solid #3182ce;
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #a0aec0;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: #4a5568;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1200;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3182ce;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #4a5568;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .left-sidebar {
        width: 280px;
    }
    
    .right-sidebar {
        width: 320px;
    }
    
    .ai-capabilities {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .left-sidebar {
        position: absolute;
        z-index: 100;
        height: 100%;
        width: 300px;
        left: -300px;
        transition: left 0.3s ease;
    }
    
    .left-sidebar.show {
        left: 0;
    }
    
    .right-sidebar {
        position: absolute;
        z-index: 100;
        height: 100%;
        width: 320px;
        right: -320px;
        transition: right 0.3s ease;
    }
    
    .right-sidebar.show {
        right: 0;
    }
    
    .toolbar {
        padding: 8px 12px;
    }
}

/* 拖拽样式 */
.drag-indicator {
    position: fixed;
    width: 3px;
    height: 24px;
    background: linear-gradient(180deg, #3182ce, #2c5aa0);
    border-radius: 2px;
    pointer-events: none;
    z-index: 1000;
    opacity: 0.9;
    box-shadow: 0 2px 8px rgba(49, 130, 206, 0.4);
}

.drag-preview {
    position: absolute;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 200px;
    z-index: 1000;
    font-size: 12px;
    pointer-events: none;
}

/* Markdown编辑器拖拽样式 */
.markdown-insertion-indicator {
    position: fixed;
    width: 2px;
    height: 20px;
    background-color: #007acc;
    z-index: 1000;
    display: none;
    pointer-events: none;
    border-radius: 1px;
    box-shadow: 0 0 4px rgba(0, 122, 204, 0.5);
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 编辑器拖拽目标样式 */
#markdownEditor.drag-over {
    background-color: #f0f8ff;
    border: 2px dashed #3182ce;
}

/* 素材项拖拽样式增强 */
.material-item[draggable="true"] {
    cursor: grab;
    transition: all 0.2s ease;
}

.material-item[draggable="true"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.material-item.dragging {
    opacity: 0.5;
    transform: scale(0.95);
    cursor: grabbing;
}

.material-drag-indicator {
    position: absolute;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.material-item:hover .material-drag-indicator {
    opacity: 1;
}

.drag-dots {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.drag-dot {
    width: 3px;
    height: 3px;
    background-color: #666;
    border-radius: 50%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 选中文本样式 */
::selection {
    background-color: rgba(49, 130, 206, 0.2);
}

/* 焦点样式 */
:focus {
    outline: none;
}

/* 过渡动画 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #a0aec0;
}

.empty-state h3 {
    font-size: 16px;
    margin-bottom: 8px;
    color: #4a5568;
}

.empty-state p {
    font-size: 14px;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 20px;
    color: #a0aec0;
    font-style: italic;
}

/* AI选中文本预览样式 */
.selected-text-preview {
    margin-top: 12px;
    padding: 12px;
    background-color: #f8f9fa;
    border-left: 4px solid #3182ce;
    border-radius: 6px;
    font-size: 13px;
}

.selected-text-label {
    font-weight: 500;
    color: #3182ce;
    margin-bottom: 6px;
    font-size: 12px;
}

.selected-text-content {
    color: #4a5568;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 120px;
    overflow-y: auto;
    padding: 6px 8px;
    background-color: #ffffff;
    border-radius: 4px;
    border: 1px solid #e2e8f0;
}

/* 快捷操作消息样式 */
.quick-action-message {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 16px;
    margin: 8px 0;
}

.quick-action-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.quick-action-icon {
    font-size: 16px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.quick-action-title {
    font-weight: 600;
    font-size: 14px;
}

.quick-action-message .selected-text-preview {
    background-color: rgba(255, 255, 255, 0.1);
    border-left: 4px solid rgba(255, 255, 255, 0.3);
    margin-top: 0;
}

.quick-action-message .selected-text-label {
    color: rgba(255, 255, 255, 0.9);
}

.quick-action-message .selected-text-content {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.95);
}

/* 消息操作按钮样式 */
.message-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid #e2e8f0;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 10px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background-color: #ffffff;
    color: #6b7280;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
    transform: translateY(-1px);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn svg {
    opacity: 0.7;
}

.action-btn:hover svg {
    opacity: 1;
}

.copy-btn:hover {
    background-color: #eff6ff;
    border-color: #3b82f6;
    color: #1d4ed8;
}

.insert-btn:hover {
    background-color: #f0fdf4;
    border-color: #22c55e;
    color: #16a34a;
}

.replace-btn:hover {
    background-color: #fef3c7;
    border-color: #f59e0b;
    color: #d97706;
}

/* 选中文本提示区样式 */
.selected-text-hint {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    margin-bottom: 16px;
    padding: 16px;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hint-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.hint-content > div {
    display: flex;
    align-items: center;
    gap: 12px;
}

.hint-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    flex-shrink: 0;
}

.hint-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.hint-title {
    font-weight: 600;
    font-size: 14px;
    color: white;
}

.hint-subtitle {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.quick-action-btn {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    backdrop-filter: blur(10px);
}

.quick-action-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-action-btn:active {
    transform: translateY(0);
}

/* 选中文本聊天区域 */
.selected-text-chat {
    background-color: #ffffff;
}

.chat-header {
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 500;
    color: #495057;
    font-size: 13px;
}

.chat-history {
    max-height: 300px;
    overflow-y: auto;
    padding: 16px;
}

.chat-input-area {
    border-top: 1px solid #e2e8f0;
    padding: 12px 16px;
    background-color: #ffffff;
}

.chat-input-wrapper {
    display: flex;
    gap: 8px;
    align-items: flex-end;
}

.chat-input-wrapper textarea {
    flex: 1;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    line-height: 1.4;
    resize: vertical;
    min-height: 36px;
    max-height: 120px;
    font-family: inherit;
}

.chat-input-wrapper textarea:focus {
    outline: none;
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.chat-input-actions {
    display: flex;
    gap: 4px;
}

.chat-input-actions button {
    padding: 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.chat-input-actions .btn-primary {
    background-color: #3182ce;
    color: white;
}

.chat-input-actions .btn-primary:hover {
    background-color: #2c5aa0;
}

/* 聊天消息样式 */
.chat-message {
    margin-bottom: 16px;
}

.chat-message.user {
    text-align: right;
}

.chat-message.ai {
    text-align: left;
}

.chat-message-content {
    display: inline-block;
    padding: 8px 12px;
    border-radius: 12px;
    font-size: 13px;
    line-height: 1.4;
    max-width: 80%;
    word-wrap: break-word;
}

.chat-message.user .chat-message-content {
    background-color: #3182ce;
    color: white;
}

.chat-message.ai .chat-message-content {
    background-color: #f1f3f4;
    color: #333;
}

.chat-message-actions {
    margin-top: 8px;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.chat-message.ai .chat-message-actions {
    justify-content: flex-start;
}

.chat-message-actions button {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background-color: #ffffff;
    color: #374151;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
}

.chat-message-actions button:hover {
    background-color: #f3f4f6;
}

/* 输入模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    width: 320px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e6ed;
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #718096;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f7fafc;
    color: #2d3748;
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin: 0 0 12px 0;
    color: #2d3748;
    font-size: 14px;
}

.modal-body input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    background: #fff;
    color: #2d3748;
    transition: border-color 0.2s ease;
}

.modal-body input:focus {
    outline: none;
    border-color: #3182ce;
    box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
}

.modal-footer {
    display: flex;
    gap: 8px;
    padding: 16px 20px;
    border-top: 1px solid #e0e6ed;
    justify-content: flex-end;
}

.modal-footer .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-footer .btn-secondary {
    background: #f7fafc;
    color: #718096;
    border: 1px solid #d1d5db;
}

.modal-footer .btn-secondary:hover {
    background: #e2e8f0;
    color: #2d3748;
}

.modal-footer .btn-primary {
    background: #3182ce;
    color: white;
}

.modal-footer .btn-primary:hover {
    background: #2c5aa0;
}

.modal-footer .btn-danger {
    background: #dc3545;
    color: white;
}

.modal-footer .btn-danger:hover {
    background: #c82333;
}

.modal-footer .btn-warning {
    background: #ffc107;
    color: #212529;
}

.modal-footer .btn-warning:hover {
    background: #e0a800;
}

/* 文件夹删除对话框样式 */
.file-list-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin: 12px 0;
    max-height: 120px;
    overflow-y: auto;
}

.file-preview-item {
    display: flex;
    align-items: center;
    padding: 4px 0;
    font-size: 13px;
    color: #495057;
}

.file-preview-more {
    padding: 4px 0;
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

/* 搜索结果样式 */
.search-results-header {
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e6ed;
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.search-results-count {
    display: flex;
    align-items: center;
    gap: 4px;
}

.search-no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
}

.no-results-icon {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.no-results-text {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #495057;
}

.no-results-hint {
    font-size: 12px;
    color: #6c757d;
}

/* 搜索高亮样式 */
mark {
    background: #fff3cd;
    color: #856404;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 500;
}

/* 现代化文件列表样式 */
.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin: 2px 0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    position: relative;
}

.file-item:hover {
    background: #f8f9fa;
    border-color: #e9ecef;
}

.file-item.selected {
    background: #e3f2fd;
    border-color: #2196f3;
}

.file-item.file-item-nested {
    margin-left: 24px;
    border-left: 2px solid #e9ecef;
}

.file-content {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

.file-icon-wrapper {
    display: flex;
    align-items: center;
    margin-right: 8px;
    color: #6c757d;
}

.file-icon {
    transition: color 0.2s ease;
}

.file-item:hover .file-icon {
    color: #495057;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
}

.file-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 2px;
}

.file-date,
.file-size {
    font-size: 12px;
    color: #6c757d;
}

.file-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.file-item:hover .file-actions {
    opacity: 1;
}

.file-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: 6px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.file-action-btn:hover {
    background: #f8f9fa;
    color: #495057;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.file-action-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 特定按钮样式 */
.file-action-btn.edit-btn:hover {
    background: #e3f2fd;
    color: #1976d2;
}

.file-action-btn.duplicate-btn:hover {
    background: #f3e5f5;
    color: #7b1fa2;
}

.file-action-btn.delete-btn:hover {
    background: #ffebee;
    color: #d32f2f;
}

/* 按钮工具提示效果 */
.file-action-btn::after {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.file-action-btn:hover::after {
    opacity: 1;
}

/* 文件夹样式 */
.folder-item {
    margin: 4px 0;
}

.folder-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.folder-header:hover {
    background: #f8f9fa;
    border-color: #e9ecef;
}

.folder-toggle {
    display: flex;
    align-items: center;
    margin-right: 4px;
    color: #6c757d;
    transition: transform 0.2s ease;
}

.folder-item.open .folder-toggle {
    transform: rotate(0deg);
}

.folder-icon-wrapper {
    display: flex;
    align-items: center;
    margin-right: 8px;
    color: #ffc107;
}

.folder-info {
    flex: 1;
    min-width: 0;
}

.folder-name {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    display: block;
    line-height: 1.4;
}

.folder-count {
    font-size: 12px;
    color: #6c757d;
    margin-top: 1px;
    display: block;
}

.folder-actions {
    display: flex;
    align-items: center;
    gap: 6px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.folder-header:hover .folder-actions {
    opacity: 1;
}

.folder-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: 6px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.folder-action-btn:hover {
    background: #f8f9fa;
    color: #495057;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.folder-action-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 特定文件夹按钮样式 */
.folder-action-btn.add-file-btn:hover {
    background: #e8f5e8;
    color: #2e7d32;
}

.folder-action-btn.rename-btn:hover {
    background: #e3f2fd;
    color: #1976d2;
}

.folder-action-btn.delete-btn:hover {
    background: #ffebee;
    color: #d32f2f;
}

/* 文件夹按钮工具提示 */
.folder-action-btn::after {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.folder-action-btn:hover::after {
    opacity: 1;
}

.folder-content {
    overflow: hidden;
    transition: all 0.3s ease;
}

.folder-content.collapsed {
    max-height: 0;
    opacity: 0;
}

.folder-content.expanded {
    max-height: 1000px;
    opacity: 1;
}

/* 拖拽样式 */
.file-item[draggable="true"] {
    cursor: grab;
}

.file-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    cursor: grabbing;
}

.folder-item.drop-target {
    background: #e3f2fd;
    border-color: #2196f3;
}

.folder-item.drop-target .folder-header {
    background: #e3f2fd;
    border-color: #2196f3;
}

/* 文件夹和文件拖拽排序样式 */
.folder-item.dragging,
.file-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    z-index: 1000;
}

.folder-item.drop-before {
    border-top: 3px solid #2196f3;
    margin-top: 3px;
}

.folder-item.drop-after {
    border-bottom: 3px solid #2196f3;
    margin-bottom: 3px;
}

.file-item.drop-before {
    border-top: 2px solid #4caf50;
    margin-top: 2px;
}

.file-item.drop-after {
    border-bottom: 2px solid #4caf50;
    margin-bottom: 2px;
}

/* 拖拽时的文件夹和文件样式 */
.folder-item[draggable="true"]:hover {
    cursor: grab;
}

.folder-item[draggable="true"]:active {
    cursor: grabbing;
}

.file-item[draggable="true"]:hover {
    cursor: grab;
}

.file-item[draggable="true"]:active {
    cursor: grabbing;
}

/* 操作反馈和动画效果 */
.file-item.creating,
.folder-item.creating {
    animation: slideInFromTop 0.3s ease-out;
    border: 2px solid #4caf50;
    background: #f1f8e9;
}

.file-item.deleting,
.folder-item.deleting {
    animation: slideOutToRight 0.3s ease-in forwards;
    opacity: 0.5;
}

.file-item.updating,
.folder-item.updating {
    animation: pulse 0.6s ease-in-out infinite alternate;
}

/* 局部加载状态 */
.local-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    z-index: 10;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 成功/错误状态指示器 */
.operation-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.operation-status.show {
    transform: translateX(0);
}

.operation-status.success {
    background: #4caf50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.operation-status.error {
    background: #f44336;
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.operation-status.warning {
    background: #ff9800;
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

/* 动画关键帧 */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutToRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

@keyframes pulse {
    from {
        background: #f8f9fa;
    }
    to {
        background: #e3f2fd;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 按钮点击反馈 */
.file-action-btn.clicked,
.folder-action-btn.clicked {
    animation: buttonClick 0.2s ease;
}

@keyframes buttonClick {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

/* 统一的交互体验优化 */
.file-item,
.folder-item {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 改进的焦点状态 */
.file-action-btn:focus,
.folder-action-btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* 改进的禁用状态 */
.file-action-btn:disabled,
.folder-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* 更好的间距一致性 */
.files-tree {
    padding: 8px;
}

.file-item + .file-item,
.folder-item + .folder-item,
.file-item + .folder-item,
.folder-item + .file-item {
    margin-top: 4px;
}

/* 改进的空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

/* 改进的加载状态 */
.files-tree.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 响应式改进 */
@media (max-width: 768px) {
    .file-actions,
    .folder-actions {
        opacity: 1; /* 在移动设备上始终显示操作按钮 */
    }

    .file-action-btn,
    .folder-action-btn {
        width: 36px;
        height: 36px;
    }

    .file-action-btn::after,
    .folder-action-btn::after {
        display: none; /* 在移动设备上隐藏工具提示 */
    }
}

/* 改进的滚动条样式 */
.files-tree::-webkit-scrollbar {
    width: 6px;
}

.files-tree::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.files-tree::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.files-tree::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}