/* 拖拽弹窗样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: transparent;
    overflow: hidden;
}

.popup-container {
    width: 300px;
    height: 150px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    padding: 16px;
    display: flex;
    flex-direction: column;
    animation: popupSlideIn 0.3s ease-out;
    position: relative;
}

@keyframes popupSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.content-preview {
    flex: 1;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
}

.content-type {
    font-size: 24px;
    line-height: 1;
    flex-shrink: 0;
}

.content-text {
    flex: 1;
    font-size: 13px;
    color: #4a5568;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-break: break-word;
}

.popup-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.btn-primary {
    flex: 1;
    padding: 8px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    flex: 1;
    padding: 8px 16px;
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.btn-secondary:active {
    background: #e2e8f0;
}

.quick-actions {
    display: flex;
    justify-content: center;
}

.btn-link {
    background: none;
    border: none;
    color: #667eea;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-link:hover {
    background: rgba(102, 126, 234, 0.1);
    text-decoration: underline;
}

/* 内容类型图标样式 - 现在使用直接插入的SVG */
.content-type svg {
    display: block;
    width: 16px;
    height: 16px;
}

/* 响应式调整 */
@media (max-width: 350px) {
    .popup-container {
        width: 280px;
        height: 140px;
        padding: 12px;
    }
    
    .content-text {
        font-size: 12px;
        -webkit-line-clamp: 2;
    }
    
    .btn-primary,
    .btn-secondary {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    .popup-container {
        background: rgba(45, 55, 72, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
        color: #e2e8f0;
    }
    
    .content-text {
        color: #a0aec0;
    }
    
    .btn-secondary {
        background: rgba(74, 85, 104, 0.8);
        color: #e2e8f0;
        border-color: rgba(113, 128, 150, 0.5);
    }
    
    .btn-secondary:hover {
        background: rgba(113, 128, 150, 0.8);
        border-color: rgba(160, 174, 192, 0.5);
    }
    
    .btn-link {
        color: #90cdf4;
    }
    
    .btn-link:hover {
        background: rgba(144, 205, 244, 0.1);
    }
}

/* 动画效果 */
.popup-container.closing {
    animation: popupSlideOut 0.2s ease-in forwards;
}

@keyframes popupSlideOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.8) translateY(-10px);
    }
}

/* 保存成功动画 */
.popup-container.success {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    }
}

/* 错误状态 */
.popup-container.error {
    border-color: rgba(244, 67, 54, 0.3);
    box-shadow: 0 8px 32px rgba(244, 67, 54, 0.2);
}

.popup-container.error .btn-primary {
    background: linear-gradient(135deg, #f44336 0%, #e91e63 100%);
}

/* 加载状态 */
.popup-container.loading .btn-primary {
    position: relative;
    color: transparent;
}

.popup-container.loading .btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: white;
    border-radius: 50%;
    animation: buttonSpin 0.8s linear infinite;
}

@keyframes buttonSpin {
    to {
        transform: rotate(360deg);
    }
}