<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置</title>
    <link rel="stylesheet" href="styles/settings.css">
</head>
<body>
    <div class="settings-container">
        <div class="settings-sidebar">
            <div class="settings-nav">
                <div class="nav-item active" data-section="general">
                    <span class="nav-icon">⚙️</span>
                    <span class="nav-text">通用</span>
                </div>
                <div class="nav-item" data-section="collection">
                    <span class="nav-icon">📚</span>
                    <span class="nav-text">收集模式</span>
                </div>
                <div class="nav-item" data-section="organization">
                    <span class="nav-icon">📝</span>
                    <span class="nav-text">整理模式</span>
                </div>
                <div class="nav-item" data-section="ai">
                    <span class="nav-icon">🤖</span>
                    <span class="nav-text">AI 设置</span>
                </div>
                <div class="nav-item" data-section="shortcuts">
                    <span class="nav-icon">⌨️</span>
                    <span class="nav-text">快捷键</span>
                </div>
                <div class="nav-item" data-section="data">
                    <span class="nav-icon">💾</span>
                    <span class="nav-text">数据</span>
                </div>
            </div>
        </div>
        
        <div class="settings-content">
            <!-- 通用设置 -->
            <div class="settings-section active" id="general">
                <h2>通用设置</h2>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="launchAtStartup">
                        <span class="checkmark"></span>
                        开机自动启动
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="showTrayIcon">
                        <span class="checkmark"></span>
                        显示系统托盘图标
                    </label>
                </div>
                
                <div class="setting-group">
                    <label for="theme">主题</label>
                    <select id="theme">
                        <option value="light">浅色</option>
                        <option value="dark">深色</option>
                        <option value="auto">跟随系统</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label for="language">语言</label>
                    <select id="language">
                        <option value="zh-CN">简体中文</option>
                        <option value="en">English</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label for="fontSize">字体大小</label>
                    <input type="range" id="fontSize" min="12" max="24" value="14">
                    <span id="fontSizeValue">14px</span>
                </div>
            </div>
            
            <!-- 收集模式设置 -->
            <div class="settings-section" id="collection">
                <h2>收集模式设置</h2>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="autoSaveCollection">
                        <span class="checkmark"></span>
                        自动保存收藏
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="showPreview">
                        <span class="checkmark"></span>
                        显示内容预览
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="dragDropEnabled">
                        <span class="checkmark"></span>
                        启用拖拽收藏
                    </label>
                </div>
                
                <div class="setting-group">
                    <label for="popupDuration">弹窗持续时间 (秒)</label>
                    <input type="number" id="popupDuration" min="1" max="30" value="5">
                </div>
                
                <div class="setting-group">
                    <label for="popupWidth">弹窗宽度 (px)</label>
                    <input type="number" id="popupWidth" min="200" max="600" value="300">
                </div>
                
                <div class="setting-group">
                    <label for="popupHeight">弹窗高度 (px)</label>
                    <input type="number" id="popupHeight" min="100" max="400" value="150">
                </div>
            </div>
            
            <!-- 整理模式设置 -->
            <div class="settings-section" id="organization">
                <h2>整理模式设置</h2>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="autoSaveOrganization">
                        <span class="checkmark"></span>
                        自动保存文档
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="showLineNumbers">
                        <span class="checkmark"></span>
                        显示行号
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="wordWrap">
                        <span class="checkmark"></span>
                        自动换行
                    </label>
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="aiAutoAnalysis">
                        <span class="checkmark"></span>
                        AI 自动分析
                    </label>
                </div>
                
                <div class="setting-group">
                    <label for="sidebarWidth">侧边栏宽度 (px)</label>
                    <input type="number" id="sidebarWidth" min="200" max="600" value="300">
                </div>
            </div>
            
            <!-- AI 设置 -->
            <div class="settings-section" id="ai">
                <h2>AI 设置</h2>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="aiEnabled">
                        <span class="checkmark"></span>
                        启用 AI 功能
                    </label>
                </div>
                
                <div class="setting-group">
                    <label for="apiUrl">API 地址</label>
                    <input type="text" id="apiUrl" placeholder="https://api.example.com/v1/chat/completions">
                </div>
                
                <div class="setting-group">
                    <label for="apiKey">API 密钥</label>
                    <input type="password" id="apiKey" placeholder="sk-...">
                </div>
                
                <div class="setting-group">
                    <label for="model">模型</label>
                    <input type="text" id="model" placeholder="claude-sonnet-4-20250514">
                </div>
                
                <div class="setting-group">
                    <label for="temperature">温度</label>
                    <input type="range" id="temperature" min="0" max="1" step="0.1" value="0.7">
                    <span id="temperatureValue">0.7</span>
                </div>
                
                <div class="setting-group">
                    <label for="maxTokens">最大令牌数</label>
                    <input type="number" id="maxTokens" min="100" max="8000" value="4000">
                </div>
                
                <div class="setting-group">
                    <label for="maxRetries">最大重试次数</label>
                    <input type="number" id="maxRetries" min="1" max="10" value="3">
                </div>
            </div>
            
            <!-- 快捷键设置 -->
            <div class="settings-section" id="shortcuts">
                <h2>快捷键设置</h2>
                
                <div class="setting-group">
                    <label for="switchToCollection">切换到收集模式</label>
                    <input type="text" id="switchToCollection" readonly>
                </div>
                
                <div class="setting-group">
                    <label for="switchToOrganization">切换到整理模式</label>
                    <input type="text" id="switchToOrganization" readonly>
                </div>
                
                <div class="setting-group">
                    <label for="newCollection">新建收藏</label>
                    <input type="text" id="newCollection" readonly>
                </div>
                
                <div class="setting-group">
                    <label for="aiAnalysis">AI 分析</label>
                    <input type="text" id="aiAnalysis" readonly>
                </div>
                
                <div class="setting-group">
                    <label for="search">搜索</label>
                    <input type="text" id="search" readonly>
                </div>
                
                <div class="setting-group">
                    <label for="export">导出</label>
                    <input type="text" id="export" readonly>
                </div>
            </div>
            
            <!-- 数据设置 -->
            <div class="settings-section" id="data">
                <h2>数据设置</h2>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="autoBackup">
                        <span class="checkmark"></span>
                        自动备份
                    </label>
                </div>
                
                <div class="setting-group">
                    <label for="backupInterval">备份间隔 (小时)</label>
                    <input type="number" id="backupInterval" min="1" max="168" value="24">
                </div>
                
                <div class="setting-group">
                    <label for="maxBackups">最大备份数</label>
                    <input type="number" id="maxBackups" min="1" max="30" value="7">
                </div>
                
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="autoCleanup">
                        <span class="checkmark"></span>
                        自动清理
                    </label>
                </div>
                
                <div class="setting-group">
                    <label for="cleanupDays">清理天数</label>
                    <input type="number" id="cleanupDays" min="1" max="365" value="30">
                </div>
                
                <div class="setting-group">
                    <div class="action-buttons">
                        <button id="exportDataBtn" class="btn-secondary">导出数据</button>
                        <button id="importDataBtn" class="btn-secondary">导入数据</button>
                        <button id="resetDataBtn" class="btn-danger">重置数据</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="settings-footer">
            <button id="resetBtn" class="btn-secondary">重置所有设置</button>
            <div class="footer-actions">
                <button id="cancelBtn" class="btn-secondary">取消</button>
                <button id="saveBtn" class="btn-primary">保存</button>
            </div>
        </div>
    </div>
    
    <script src="scripts/settings.js"></script>
</body>
</html>