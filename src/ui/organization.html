<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QNotes - 整理模式</title>
    <link rel="stylesheet" href="styles/organization.css">
    <link rel="stylesheet" href="styles/github-markdown.css">
    <link rel="stylesheet" href="styles/quick-capture.css">
</head>
<body>
    <div class="app-container">
        <!-- 左侧栏 -->
        <div class="left-sidebar" id="leftSidebar">
            <div class="sidebar-header">
                <div class="sidebar-tabs">
                    <button class="tab-button active" data-tab="materials" id="materialsTab">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"/>
                        </svg>
                        素材
                    </button>
                    <button class="tab-button" data-tab="files" id="filesTab">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/>
                        </svg>
                        文件
                    </button>
                </div>
            </div>
            
            <!-- 素材管理 Tab -->
            <div class="tab-content active" id="materialsTabContent">
                <div class="tab-controls">
                    <div class="tab-actions">
                        <button id="createMaterialFolderBtn" class="btn-icon" title="创建文件夹" onclick="organizationApp.materialsManager.createMaterialFolder()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 6h-2l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-1 8h-3v3h-2v-3h-3v-2h3V9h2v3h3v2z"/>
                            </svg>
                        </button>
                        <button id="addMaterialBtn" class="btn-icon" title="快速收藏" onclick="organizationApp.materialsManager.openQuickCapture()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- 持久搜索栏 -->
                <div class="persistent-search-container">
                    <div class="search-input-wrapper">
                        <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="m21 21-4.35-4.35"/>
                        </svg>
                        <input type="text" id="materialsSearchInput" placeholder="搜索素材内容、标签、分类..." autocomplete="off">
                        <button id="clearMaterialsSearchBtn" class="clear-search-btn" style="display: none;" title="清除搜索">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>
                </div>
                

                
                <!-- 批量操作栏 -->
                <div class="batch-actions" id="batchActions" style="display: none;">
                    <button id="selectAllBtn" class="btn-secondary" onclick="organizationApp.selectAllMaterials()">全选</button>
                    <button id="batchInsertBtn" class="btn-primary" onclick="organizationApp.insertSelectedMaterials()">插入素材</button>
                    <button id="batchDeleteBtn" class="btn-danger" onclick="organizationApp.batchDeleteMaterials()">删除素材</button>
                </div>
                
                <!-- 素材列表 -->
                <div class="materials-list" id="materialsList">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <!-- 文件管理 Tab -->
            <div class="tab-content" id="filesTabContent">
                <!-- 持久搜索栏 -->
                <div class="persistent-search-container">
                    <div class="search-input-wrapper">
                        <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="m21 21-4.35-4.35"/>
                        </svg>
                        <input type="text" id="filesSearchInput" placeholder="搜索文件和文件夹..." autocomplete="off">
                        <button id="clearSearchBtn" class="clear-search-btn" style="display: none;" title="清除搜索">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="tab-controls">
                    <div class="tab-actions">
                        <button id="newFolderBtn" class="btn-icon" title="新建文件夹">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
                                <line x1="12" y1="11" x2="12" y2="17"/>
                                <line x1="9" y1="14" x2="15" y2="14"/>
                            </svg>
                        </button>
                        <button id="newFileBtn" class="btn-icon" title="新建文件">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="12" y1="18" x2="12" y2="12"/>
                                <line x1="9" y1="15" x2="15" y2="15"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- 文件树 -->
                <div class="files-tree" id="filesTree">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
        
        <!-- 左侧栏调整器 -->
        <div class="resizer left-resizer" id="leftResizer"></div>
        
        <!-- 中间编辑区 -->
        <div class="editor-section" id="editorSection">
            <!-- 简化的工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <button id="toggleLeftSidebar" class="btn-icon" title="显示/隐藏左侧栏">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 9h14V7H3v2zm0 4h14v-2H3v2zm0 4h14v-2H3v2zm16 0h2v-2h-2v2zm0-10v2h2V7h-2zm0 6h2v-2h-2v2z"/>
                        </svg>
                    </button>
                    <div class="separator"></div>
                    <button id="saveDocBtn" class="btn-icon" title="保存文档">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>
                        </svg>
                    </button>
                </div>
                
                <div class="toolbar-right">
                    <button id="previewToggleBtn" class="btn-icon active" title="预览">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                        </svg>
                    </button>
                    <button id="aiToggleBtn" class="btn-icon" title="AI助手">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.12.23-2.19.64-3.17L6.5 10.5C6.19 11.29 6 12.13 6 13c0 3.31 2.69 6 6 6s6-2.69 6-6-2.69-6-6-6c-.87 0-1.71.19-2.5.5L7.83 5.83C9.1 4.67 10.5 4 12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/>
                            <circle cx="9" cy="9" r="2"/>
                            <circle cx="15" cy="15" r="2"/>
                            <path d="M9 15l6-6"/>
                        </svg>
                    </button>
                    <button id="exportBtn" class="btn-icon" title="导出">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z"/>
                        </svg>
                    </button>
                    <button id="settingsBtn" class="btn-icon" title="设置">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                        </svg>
                    </button>
                    <div class="separator"></div>
                    <button id="toggleRightSidebar" class="btn-icon" title="显示/隐藏右侧栏">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 9h14V7H3v2zm0 4h14v-2H3v2zm0 4h14v-2H3v2zm16 0h2v-2h-2v2zm0-10v2h2V7h-2zm0 6h2v-2h-2v2z"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- 编辑器 -->
            <div class="editor-wrapper">
                <div class="editor-content" id="editorContent" ondragover="event.preventDefault();" ondrop="event.preventDefault();">
                    <div class="editor-placeholder">
                        开始编写你的文档，或者从左侧拖拽素材到这里...
                    </div>
                </div>
            </div>
            
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span id="statusText">准备就绪</span>
                </div>
                <div class="status-right">
                    <span id="wordCount">0 字</span>
                    <span id="charCount">0 字符</span>
                </div>
            </div>
        </div>
        
        <!-- 右侧栏调整器 -->
        <div class="resizer right-resizer" id="rightResizer"></div>
        
        <!-- 右侧栏 -->
        <div class="right-sidebar" id="rightSidebar">
            <!-- 预览面板 -->
            <div class="right-panel active" id="previewPanel">
                <div class="panel-header">
                    <h3>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                        </svg>
                        预览
                    </h3>
                </div>
                <div class="panel-content">
                    <div id="markdownPreview" class="markdown-body">
                        <div class="preview-placeholder">
                            在左侧编辑器中输入内容以查看预览
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- AI 对话面板 -->
            <div class="right-panel" id="aiPanel">
                <div class="panel-header">
                    <h3>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.12.23-2.19.64-3.17L6.5 10.5C6.19 11.29 6 12.13 6 13c0 3.31 2.69 6 6 6s6-2.69 6-6-2.69-6-6-6c-.87 0-1.71.19-2.5.5L7.83 5.83C9.1 4.67 10.5 4 12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/>
                            <circle cx="9" cy="9" r="2"/>
                            <circle cx="15" cy="15" r="2"/>
                            <path d="M9 15l6-6"/>
                        </svg>
                        AI 助手
                    </h3>
                    <div class="panel-header-actions">
                        <button id="aiClearBtn" class="btn-icon" title="清空对话">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="panel-content">
                    <!-- 选中文本处理区 -->
                    <div class="selected-text-processor" id="selectedTextProcessor" style="display: none;">
                        <div class="processor-header">
                            <div class="processor-title">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                                </svg>
                                <span>选中文本处理</span>
                                <span class="text-length" id="selectedTextLength">0 字符</span>
                            </div>
                            <button class="processor-close" id="closeSelectedTextProcessor">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="processor-content">
                            <div class="selected-text-preview" id="selectedTextPreview"></div>
                            <div class="quick-actions">
                                <button class="quick-action-btn" data-action="rewrite" title="重写文本">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                                    </svg>
                                    重写
                                </button>
                                <button class="quick-action-btn" data-action="improve" title="改进文本">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    改进
                                </button>
                                <button class="quick-action-btn" data-action="summarize" title="总结文本">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                                    </svg>
                                    总结
                                </button>
                                <button class="quick-action-btn" data-action="expand" title="扩写文本">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                    </svg>
                                    扩写
                                </button>
                                <button class="quick-action-btn" data-action="translate" title="翻译文本">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
                                    </svg>
                                    翻译
                                </button>
                                <button class="quick-action-btn" data-action="correct" title="纠错">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                    纠错
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI 对话历史 -->
                    <div class="ai-chat-history" id="aiChatHistory">
                        <div class="ai-welcome">
                            <div class="ai-welcome-icon">
                                <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.12.23-2.19.64-3.17L6.5 10.5C6.19 11.29 6 12.13 6 13c0 3.31 2.69 6 6 6s6-2.69 6-6-2.69-6-6-6c-.87 0-1.71.19-2.5.5L7.83 5.83C9.1 4.67 10.5 4 12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/>
                                    <circle cx="9" cy="9" r="2"/>
                                    <circle cx="15" cy="15" r="2"/>
                                    <path d="M9 15l6-6"/>
                                </svg>
                            </div>
                            <h3>AI 写作助手</h3>
                            <p>选中文本后可进行快速处理，或直接对话交流</p>
                            <div class="ai-capabilities">
                                <div class="ai-capability">
                                    <span class="capability-icon">💬</span>
                                    <span>智能对话</span>
                                </div>
                                <div class="ai-capability">
                                    <span class="capability-icon">✍️</span>
                                    <span>文本优化</span>
                                </div>
                                <div class="ai-capability">
                                    <span class="capability-icon">📝</span>
                                    <span>内容扩展</span>
                                </div>
                                <div class="ai-capability">
                                    <span class="capability-icon">🌐</span>
                                    <span>多语言翻译</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI 输入区 -->
                    <div class="ai-input-area">
                        <div class="ai-input-wrapper">
                            <textarea id="aiInput" placeholder="输入您的问题或指令..." rows="3"></textarea>
                            <div class="ai-input-actions">
                                <button id="aiSendBtn" class="btn-primary" title="发送 (Ctrl+Enter)">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <button id="modalClose" class="btn-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body" id="modalBody"></div>
        </div>
    </div>
    
    <!-- 通知容器 -->
    <div class="notifications" id="notifications"></div>
    
    <!-- 加载覆盖层 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text" id="loadingText">加载中...</div>
        </div>
    </div>
    
    <!-- 脚本 -->
    <script src="scripts/core.js"></script>
    <script src="scripts/markdown-editor.js"></script>
    <script src="scripts/files-manager.js"></script>
    <script src="scripts/materials-manager.js"></script>
    <script src="scripts/ai-manager.js"></script>
    <script src="scripts/editor-manager.js"></script>
    <script src="scripts/ui-manager.js"></script>
    <script src="scripts/app.js"></script>
</body>
</html>