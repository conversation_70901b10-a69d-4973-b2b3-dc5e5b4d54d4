const axios = require('axios');
const { EventEmitter } = require('events');

class AIService extends EventEmitter {
  constructor(appController = null) {
    super();
    this.appController = appController;
    
    // 默认配置
    this.config = {
      enabled: false,
      baseURL: 'https://api.tu-zi.com/v1',
      apiKey: '',
      model: 'claude-sonnet-4-20250514',
      temperature: 0.7,
      maxTokens: 4000,
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000
    };
    
    // 如果有appController，从设置中加载配置
    if (this.appController && this.appController.settings) {
      const aiSettings = this.appController.settings.getAISettings();
      if (aiSettings) {
        this.config = { ...this.config, ...aiSettings };
      }
    }
    
    this.isConnected = false;
    this.requestCount = 0;
    this.errorCount = 0;
    this.lastError = null;
  }

  // 加载配置
  loadConfig() {
    try {
      if (this.appController && this.appController.settings) {
        const aiSettings = this.appController.settings.getAISettings();
        if (aiSettings) {
          this.config = { ...this.config, ...aiSettings };
        }
      }
    } catch (error) {
      console.error('加载AI配置失败:', error);
    }
  }

  // 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    try {
      this.appController.settings.setAISettings(this.config);
      this.emit('config-updated', this.config);
    } catch (error) {
      console.error('保存AI配置失败:', error);
    }
  }

  // 获取配置（隐藏敏感信息）
  getConfig() {
    const config = { ...this.config };
    if (config.apiKey) {
      config.apiKey = config.apiKey.substring(0, 7) + '***' + config.apiKey.substring(config.apiKey.length - 4);
    }
    return config;
  }

  // 获取完整配置（仅内部使用）
  getFullConfig() {
    return { ...this.config };
  }

  // 测试连接
  async testConnection() {
    try {
      const result = await this.callAI('Hello, this is a connection test.', {
        maxTokens: 100,
        temperature: 0.1
      });
      
      return {
        success: true,
        message: 'AI连接测试成功',
        response: result
      };
    } catch (error) {
      return {
        success: false,
        message: `AI连接测试失败: ${error.message}`,
        error: error.message
      };
    }
  }

  async callAI(prompt, options = {}) {
    // 检查API Key是否已配置
    if (!this.config.apiKey) {
      throw new Error('API Key未配置，请先在设置中配置AI服务');
    }

    const {
      temperature = 0.7,
      maxTokens = 4000,
      systemPrompt = '你是一个智能内容分析助手，专门帮助用户整理和分析收藏的内容。'
    } = options;

    const requestData = {
      model: this.config.model,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature,
      max_tokens: maxTokens,
      stream: false
    };

    let lastError;
    for (let retry = 0; retry < this.config.maxRetries; retry++) {
      try {
        const response = await axios.post(`${this.config.baseURL}/chat/completions`, requestData, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.config.apiKey}`
          },
          timeout: this.config.timeout
        });

        if (response.data && response.data.choices && response.data.choices.length > 0) {
          const content = response.data.choices[0].message.content;
          this.emit('response', { prompt, response: content, usage: response.data.usage });
          return content;
        } else {
          throw new Error('AI响应格式不正确');
        }
      } catch (error) {
        lastError = error;
        console.error(`AI调用失败 (尝试 ${retry + 1}/${this.config.maxRetries}):`, error.message);
        
        if (retry < this.config.maxRetries - 1) {
          await this.delay(this.config.retryDelay * Math.pow(2, retry));
        }
      }
    }
    
    throw new Error(`AI调用失败: ${lastError.message}`);
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async analyzeCollections(items) {
    if (!items || items.length === 0) {
      return { categories: {}, insights: '', duplicates: [] };
    }

    const prompt = `请分析以下收藏内容，进行智能分类和整理：

${items.map((item, i) => `${i + 1}. [${item.type}] ${item.content.substring(0, 300)}...`).join('\n\n')}

请返回JSON格式的分析结果，包含以下字段：
{
  "categories": {
    "分类名": {
      "items": [项目索引数组],
      "keywords": ["关键词1", "关键词2"],
      "summary": "分类摘要",
      "priority": "重要程度(1-5的数字)"
    }
  },
  "insights": "整体分析和建议",
  "duplicates": [[重复内容的索引组]]
}

要求：
1. 创建3-8个有意义的分类
2. 每个分类包含2-15个项目
3. 关键词要准确反映内容主题
4. 摘要要简洁明了
5. 优先级要合理评估
6. 识别重复或相似的内容
7. 必须返回有效的JSON格式`;

    try {
      const response = await this.callAI(prompt, {
        temperature: 0.3,
        maxTokens: 3000,
        systemPrompt: '你是一个专业的内容分析师，擅长对各类信息进行分类整理。请严格按照JSON格式返回分析结果。'
      });

      // 尝试解析JSON响应
      let analysis;
      try {
        // 提取JSON部分
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysis = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('未找到JSON格式的响应');
        }
      } catch (parseError) {
        console.error('JSON解析失败:', parseError);
        // 返回默认结构
        analysis = {
          categories: {
            "未分类": {
              items: items.map((_, i) => i),
              keywords: ["混合内容"],
              summary: "需要进一步分析的内容",
              priority: 3
            }
          },
          insights: "分析过程中遇到问题，请重新尝试",
          duplicates: []
        };
      }

      // 验证和修正分析结果
      analysis = this.validateAnalysis(analysis, items.length);
      
      this.emit('analysis', { items, analysis });
      return analysis;
      
    } catch (error) {
      console.error('内容分析失败:', error);
      throw new Error(`内容分析失败: ${error.message}`);
    }
  }

  validateAnalysis(analysis, itemCount) {
    // 确保基本结构存在
    if (!analysis.categories) analysis.categories = {};
    if (!analysis.insights) analysis.insights = '';
    if (!analysis.duplicates) analysis.duplicates = [];

    // 验证每个分类
    Object.keys(analysis.categories).forEach(categoryName => {
      const category = analysis.categories[categoryName];
      
      // 确保必要字段存在
      if (!category.items || !Array.isArray(category.items)) {
        category.items = [];
      }
      if (!category.keywords || !Array.isArray(category.keywords)) {
        category.keywords = [];
      }
      if (!category.summary) {
        category.summary = '';
      }
      if (!category.priority || typeof category.priority !== 'number') {
        category.priority = 3;
      }
      
      // 验证索引范围
      category.items = category.items.filter(index => 
        typeof index === 'number' && index >= 0 && index < itemCount
      );
      
      // 确保优先级在合理范围内
      category.priority = Math.max(1, Math.min(5, category.priority));
    });

    // 验证重复项
    if (Array.isArray(analysis.duplicates)) {
      analysis.duplicates = analysis.duplicates.filter(group => 
        Array.isArray(group) && 
        group.length > 1 &&
        group.every(index => typeof index === 'number' && index >= 0 && index < itemCount)
      );
    }

    return analysis;
  }

  async generateMarkdown(categories, items) {
    if (!categories || Object.keys(categories).length === 0) {
      return '# 空的收藏内容\n\n暂无内容可供整理。';
    }

    const prompt = `基于以下分类结果和原始内容，生成一个结构化的Markdown文档：

## 分类信息：
${JSON.stringify(categories, null, 2)}

## 原始内容：
${items.map((item, i) => `${i}. [${item.type}] ${item.content.substring(0, 200)}...`).join('\n')}

要求：
1. 创建清晰的文档结构，包含目录
2. 每个分类作为一个章节
3. 图片用 ![描述](路径) 格式
4. 链接用 [标题](URL) 格式
5. 代码用代码块格式
6. 添加内容摘要和见解
7. 保持逻辑清晰，易于阅读
8. 在文档末尾添加相关的总结和建议

请生成完整的Markdown文档：`;

    try {
      const response = await this.callAI(prompt, {
        temperature: 0.5,
        maxTokens: 4000,
        systemPrompt: '你是一个专业的技术文档编写专家，擅长将分散的信息整理成结构化的Markdown文档。'
      });

      this.emit('markdown', { categories, items, markdown: response });
      return response;
      
    } catch (error) {
      console.error('Markdown生成失败:', error);
      throw new Error(`Markdown生成失败: ${error.message}`);
    }
  }

  async summarizeContent(content, maxLength = 100) {
    const prompt = `请为以下内容生成一个简洁的摘要，不超过${maxLength}字：

${content}

要求：
1. 提取核心信息
2. 保持简洁明了
3. 突出重点
4. 不超过${maxLength}字`;

    try {
      const response = await this.callAI(prompt, {
        temperature: 0.3,
        maxTokens: 200,
        systemPrompt: '你是一个专业的内容摘要专家，擅长提取和概括核心信息。'
      });

      return response.trim();
      
    } catch (error) {
      console.error('内容摘要失败:', error);
      return content.substring(0, maxLength);
    }
  }

  async extractKeywords(content, maxCount = 10) {
    const prompt = `请从以下内容中提取${maxCount}个最重要的关键词：

${content}

要求：
1. 提取最具代表性的关键词
2. 返回JSON数组格式: ["关键词1", "关键词2", ...]
3. 不超过${maxCount}个
4. 按重要性排序`;

    try {
      const response = await this.callAI(prompt, {
        temperature: 0.2,
        maxTokens: 300,
        systemPrompt: '你是一个专业的关键词提取专家，擅长识别文本中的核心概念。'
      });

      // 尝试解析JSON响应
      try {
        const jsonMatch = response.match(/\[(.*?)\]/);
        if (jsonMatch) {
          const keywords = JSON.parse(jsonMatch[0]);
          return Array.isArray(keywords) ? keywords.slice(0, maxCount) : [];
        }
      } catch (parseError) {
        console.error('关键词JSON解析失败:', parseError);
      }

      // 备用方案：从响应中提取关键词
      const lines = response.split('\n');
      const keywords = [];
      for (const line of lines) {
        const match = line.match(/["']([^"']+)["']/);
        if (match && keywords.length < maxCount) {
          keywords.push(match[1]);
        }
      }
      
      return keywords;
      
    } catch (error) {
      console.error('关键词提取失败:', error);
      return [];
    }
  }

  async categorizeContent(content) {
    const prompt = `请为以下内容确定最合适的分类：

${content}

从以下分类中选择最合适的一个：
- 技术文档
- 学习笔记
- 工作相关
- 生活记录
- 想法灵感
- 参考资料
- 项目信息
- 其他

只返回分类名称，不需要解释。`;

    try {
      const response = await this.callAI(prompt, {
        temperature: 0.2,
        maxTokens: 50,
        systemPrompt: '你是一个专业的内容分类专家，擅长快速准确地识别内容类型。'
      });

      const category = response.trim();
      const validCategories = ['技术文档', '学习笔记', '工作相关', '生活记录', '想法灵感', '参考资料', '项目信息', '其他'];
      
      return validCategories.includes(category) ? category : '其他';
      
    } catch (error) {
      console.error('内容分类失败:', error);
      return '其他';
    }
  }

  async detectDuplicates(items) {
    if (!items || items.length < 2) {
      return [];
    }

    const prompt = `请检测以下内容中的重复或高度相似的项目：

${items.map((item, i) => `${i}. ${item.content.substring(0, 200)}...`).join('\n')}

返回JSON格式的重复项组：
[
  [索引1, 索引2],
  [索引3, 索引4, 索引5]
]

要求：
1. 只返回真正重复或高度相似的内容
2. 每组至少包含2个项目
3. 索引从0开始
4. 返回JSON数组格式`;

    try {
      const response = await this.callAI(prompt, {
        temperature: 0.1,
        maxTokens: 1000,
        systemPrompt: '你是一个专业的内容去重专家，擅长识别重复和相似的内容。'
      });

      // 尝试解析JSON响应
      try {
        const jsonMatch = response.match(/\[(.*?)\]/);
        if (jsonMatch) {
          const duplicates = JSON.parse(jsonMatch[0]);
          return Array.isArray(duplicates) ? duplicates : [];
        }
      } catch (parseError) {
        console.error('重复项JSON解析失败:', parseError);
      }

      return [];
      
    } catch (error) {
      console.error('重复检测失败:', error);
      return [];
    }
  }

  // 批量处理内容
  async batchProcess(items, operations = ['summarize', 'keywords', 'categorize']) {
    const results = [];
    
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const result = { id: item.id, ...item };
      
      try {
        if (operations.includes('summarize')) {
          result.summary = await this.summarizeContent(item.content);
        }
        
        if (operations.includes('keywords')) {
          result.keywords = await this.extractKeywords(item.content);
        }
        
        if (operations.includes('categorize')) {
          result.category = await this.categorizeContent(item.content);
        }
        
        results.push(result);
        
        // 添加延迟避免API限制
        if (i < items.length - 1) {
          await this.delay(500);
        }
        
      } catch (error) {
        console.error(`批量处理项目 ${item.id} 失败:`, error);
        results.push({ ...result, error: error.message });
      }
    }
    
    return results;
  }

  // 对话功能
  async chat(message, history = [], options = {}) {
    // 检查API Key是否已配置
    if (!this.config.apiKey) {
      return {
        success: false,
        error: 'API Key未配置，请先在设置中配置AI服务'
      };
    }

    const messages = [
      {
        role: 'system',
        content: '你是一个专业的写作助手，帮助用户改进文档内容和解答问题。请用简洁明了的语言回答问题。'
      }
    ];

    // 添加历史对话记录（过滤掉不需要的字段）
    history.forEach(msg => {
      if (msg.role === 'user' || msg.role === 'assistant') {
        messages.push({
          role: msg.role,
          content: msg.content
        });
      }
    });
    
    // 添加当前消息
    messages.push({
      role: 'user',
      content: message
    });

    const { stream = false, onData = null, onEnd = null, onError = null } = options;

    try {
      const response = await axios.post(`${this.config.baseURL}/chat/completions`, {
        model: this.config.model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 2000,
        stream: stream
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        timeout: this.config.timeout,
        responseType: stream ? 'stream' : 'json'
      });

      if (stream) {
        return new Promise((resolve, reject) => {
          let fullContent = '';
          
          response.data.on('data', (chunk) => {
            const lines = chunk.toString().split('\n');
            
            for (const line of lines) {
              if (line.trim().startsWith('data: ')) {
                const data = line.replace('data: ', '').trim();
                
                if (data === '[DONE]') {
                  if (onEnd) onEnd();
                  resolve({
                    success: true,
                    content: fullContent,
                    usage: null
                  });
                  return;
                }
                
                try {
                  const parsed = JSON.parse(data);
                  if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                    const content = parsed.choices[0].delta.content;
                    fullContent += content;
                    if (onData) onData(content);
                  }
                } catch (e) {
                  // 忽略解析错误
                }
              }
            }
          });
          
          response.data.on('end', () => {
            if (onEnd) onEnd();
            resolve({
              success: true,
              content: fullContent,
              usage: null
            });
          });
          
          response.data.on('error', (error) => {
            if (onError) onError(error);
            reject(error);
          });
        });
      } else {
        // 非流式模式
        if (response.data && response.data.choices && response.data.choices.length > 0) {
          const content = response.data.choices[0].message.content;
          this.emit('chat', { message, response: content, usage: response.data.usage });
          return {
            success: true,
            content: content,
            usage: response.data.usage
          };
        } else {
          throw new Error('AI响应格式不正确');
        }
      }
    } catch (error) {
      console.error('AI对话失败:', error);
      if (onError) onError(error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 文本处理功能
  async processText(text, action, context = '') {
    const prompts = {
      rewrite: `请重写以下文本，使其更清晰、更有条理：\n\n${text}`,
      expand: `请扩展以下文本，添加更多细节和解释：\n\n${text}`,
      summarize: `请总结以下文本的主要内容：\n\n${text}`,
      improve: `请改进以下文本的语言表达和结构：\n\n${text}`,
      translate: `请将以下文本翻译成中文：\n\n${text}`,
      explain: `请解释以下文本的含义：\n\n${text}`,
      correct: `请修正以下文本的语法和拼写错误：\n\n${text}`,
      outline: `请为以下文本创建一个大纲：\n\n${text}`
    };

    const prompt = prompts[action] || `请处理以下文本：\n\n${text}`;
    
    try {
      const response = await this.callAI(prompt, {
        temperature: 0.5,
        maxTokens: 3000,
        systemPrompt: '你是一个专业的文本处理助手，请根据用户的要求处理文本并返回高质量的结果。'
      });

      this.emit('text-process', { text, action, result: response });
      return {
        success: true,
        originalText: text,
        processedText: response,
        action: action
      };
    } catch (error) {
      console.error('文本处理失败:', error);
      return {
        success: false,
        error: error.message,
        originalText: text,
        action: action
      };
    }
  }

  // 内容生成功能
  async generateContent(topic, type = 'article', length = 'medium', style = 'professional') {
    const lengthPrompts = {
      short: '简短的',
      medium: '中等长度的',
      long: '详细的'
    };

    const typePrompts = {
      article: '文章',
      summary: '总结',
      outline: '大纲',
      list: '列表',
      guide: '指南',
      tutorial: '教程'
    };

    const stylePrompts = {
      professional: '专业',
      casual: '轻松',
      technical: '技术性',
      creative: '创意'
    };

    const prompt = `请写一篇${lengthPrompts[length]}、${stylePrompts[style]}风格的${typePrompts[type]}，主题是：${topic}`;
    
    try {
      const response = await this.callAI(prompt, {
        temperature: 0.7,
        maxTokens: 4000,
        systemPrompt: '你是一个专业的内容创作者，请根据要求创作高质量的内容。'
      });

      this.emit('content-generate', { topic, type, length, style, result: response });
      return {
        success: true,
        content: response,
        topic: topic,
        type: type,
        length: length,
        style: style
      };
    } catch (error) {
      console.error('内容生成失败:', error);
      return {
        success: false,
        error: error.message,
        topic: topic,
        type: type,
        length: length,
        style: style
      };
    }
  }

  // 获取处理统计
  getStatistics() {
    return {
      totalRequests: this.listenerCount('response'),
      totalAnalyses: this.listenerCount('analysis'),
      totalMarkdowns: this.listenerCount('markdown'),
      totalChats: this.listenerCount('chat'),
      totalTextProcesses: this.listenerCount('text-process'),
      totalContentGenerations: this.listenerCount('content-generate')
    };
  }

  // 清理资源
  destroy() {
    this.removeAllListeners();
  }
}

module.exports = AIService;