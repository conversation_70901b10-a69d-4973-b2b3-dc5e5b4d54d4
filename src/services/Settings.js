const Store = require('electron-store');
const { EventEmitter } = require('events');

class Settings extends EventEmitter {
  constructor() {
    super();
    this.store = new Store({
      name: 'qnotes-settings',
      defaults: {
        // 应用设置
        currentMode: 'collection',
        launchAtStartup: true,
        showTrayIcon: true,
        
        // 收集模式设置
        collection: {
          autoSave: true,
          showPreview: true,
          dragDropEnabled: true,
          quickCaptureKey: 'CommandOrControl+N',
          popupSize: { width: 300, height: 150 },
          popupDuration: 5000
        },
        
        // 整理模式设置
        organization: {
          windowSize: { width: 1200, height: 800 },
          sidebarWidth: 300,
          showLineNumbers: true,
          wordWrap: true,
          theme: 'light',
          autoSave: true,
          aiAutoAnalysis: false
        },
        
        // AI设置
        ai: {
          enabled: true,
          baseURL: 'https://api.tu-zi.com/v1',
          apiKey: 'sk-o2VwufJTd4Un6aUgTfSwON547FA1Ztz3upNEmepySuPPRgI2',
          model: 'claude-sonnet-4-20250514',
          temperature: 0.7,
          maxTokens: 4000,
          maxRetries: 3,
          retryDelay: 1000,
          timeout: 30000
        },
        
        // 数据设置
        data: {
          autoBackup: true,
          backupInterval: 24, // 小时
          maxBackups: 7,
          autoCleanup: true,
          cleanupDays: 30
        },
        
        // 界面设置
        ui: {
          language: 'zh-CN',
          theme: 'light',
          fontSize: 14,
          fontFamily: 'system',
          showTips: true,
          animations: true
        },
        
        // 快捷键设置
        shortcuts: {
          switchToCollection: 'CommandOrControl+1',
          switchToOrganization: 'CommandOrControl+2',
          newCollection: 'CommandOrControl+N',
          aiAnalysis: 'CommandOrControl+O',
          search: 'CommandOrControl+F',
          export: 'CommandOrControl+E'
        },
        
        // 导出设置
        export: {
          defaultFormat: 'markdown',
          includeMeta: true,
          includeTimestamps: true,
          sortBy: 'created_at',
          sortOrder: 'desc'
        }
      }
    });
    
    this.loaded = false;
  }

  async load() {
    try {
      // 加载设置
      this.loaded = true;
      console.log('设置加载完成');
      
      // 验证设置
      this.validateSettings();
      
      // 发送加载完成事件
      this.emit('loaded');
      
    } catch (error) {
      console.error('设置加载失败:', error);
      throw error;
    }
  }

  validateSettings() {
    // 验证AI设置
    const aiSettings = this.get('ai');
    if (!aiSettings.baseURL || !aiSettings.apiKey) {
      console.warn('AI设置不完整，部分功能可能无法使用');
    }
    
    // 验证窗口大小
    const orgSettings = this.get('organization');
    if (orgSettings.windowSize.width < 800 || orgSettings.windowSize.height < 600) {
      console.warn('窗口大小过小，重置为默认值');
      this.set('organization.windowSize', { width: 1200, height: 800 });
    }
    
    // 验证快捷键
    const shortcuts = this.get('shortcuts');
    const keys = Object.keys(shortcuts);
    const values = Object.values(shortcuts);
    const duplicates = values.filter((value, index) => values.indexOf(value) !== index);
    
    if (duplicates.length > 0) {
      console.warn('发现重复的快捷键设置:', duplicates);
    }
  }

  // 获取设置值
  get(key) {
    if (!this.loaded) {
      console.warn('设置尚未加载');
      return null;
    }
    
    return this.store.get(key);
  }

  // 设置值
  set(key, value) {
    if (!this.loaded) {
      console.warn('设置尚未加载');
      return;
    }
    
    const oldValue = this.store.get(key);
    this.store.set(key, value);
    
    // 发送变更事件
    this.emit('changed', key, value, oldValue);
    
    // 发送特定键的变更事件
    this.emit(`changed:${key}`, value, oldValue);
    
    console.log(`设置已更新: ${key} = ${JSON.stringify(value)}`);
  }

  // 批量设置
  setMany(settings) {
    Object.keys(settings).forEach(key => {
      this.set(key, settings[key]);
    });
  }

  // 删除设置
  delete(key) {
    if (!this.loaded) {
      console.warn('设置尚未加载');
      return;
    }
    
    const oldValue = this.store.get(key);
    this.store.delete(key);
    
    this.emit('deleted', key, oldValue);
    console.log(`设置已删除: ${key}`);
  }

  // 重置设置
  reset(key) {
    if (key) {
      this.store.reset(key);
      this.emit('reset', key);
      console.log(`设置已重置: ${key}`);
    } else {
      this.store.clear();
      this.emit('reset');
      console.log('所有设置已重置');
    }
  }

  // 获取所有设置
  getAll() {
    return this.store.store;
  }

  // 导出设置
  export() {
    return JSON.stringify(this.store.store, null, 2);
  }

  // 导入设置
  import(settingsJson) {
    try {
      const settings = JSON.parse(settingsJson);
      this.store.store = settings;
      this.emit('imported', settings);
      console.log('设置导入成功');
    } catch (error) {
      console.error('设置导入失败:', error);
      throw new Error('设置格式不正确');
    }
  }

  // 保存设置
  async save() {
    // electron-store 会自动保存，这里只是为了兼容性
    this.emit('saved');
    console.log('设置已保存');
  }

  // 获取模式相关设置
  getCollectionSettings() {
    return this.get('collection');
  }

  getOrganizationSettings() {
    return this.get('organization');
  }

  getAISettings() {
    return this.get('ai');
  }

  getUISettings() {
    return this.get('ui');
  }

  getShortcutSettings() {
    return this.get('shortcuts');
  }

  // 更新模式设置
  updateCollectionSettings(settings) {
    const current = this.getCollectionSettings();
    this.set('collection', { ...current, ...settings });
  }

  updateOrganizationSettings(settings) {
    const current = this.getOrganizationSettings();
    this.set('organization', { ...current, ...settings });
  }

  updateAISettings(settings) {
    const current = this.getAISettings();
    this.set('ai', { ...current, ...settings });
  }

  // AI配置相关便捷方法
  setAISettings(settings) {
    this.updateAISettings(settings);
  }

  isAIEnabled() {
    return this.get('ai.enabled') && this.get('ai.apiKey');
  }

  setAIEnabled(enabled) {
    this.set('ai.enabled', enabled);
  }

  getAIApiKey() {
    return this.get('ai.apiKey');
  }

  setAIApiKey(apiKey) {
    this.set('ai.apiKey', apiKey);
    // 如果设置了API Key，自动启用AI
    if (apiKey) {
      this.set('ai.enabled', true);
    }
  }

  getAIModel() {
    return this.get('ai.model');
  }

  setAIModel(model) {
    this.set('ai.model', model);
  }

  getAIBaseURL() {
    return this.get('ai.baseURL');
  }

  setAIBaseURL(baseURL) {
    this.set('ai.baseURL', baseURL);
  }

  updateUISettings(settings) {
    const current = this.getUISettings();
    this.set('ui', { ...current, ...settings });
  }

  updateShortcutSettings(settings) {
    const current = this.getShortcutSettings();
    this.set('shortcuts', { ...current, ...settings });
  }

  // 主题相关
  getTheme() {
    return this.get('ui.theme');
  }

  setTheme(theme) {
    this.set('ui.theme', theme);
    this.emit('themeChanged', theme);
  }

  // 语言相关
  getLanguage() {
    return this.get('ui.language');
  }

  setLanguage(language) {
    this.set('ui.language', language);
    this.emit('languageChanged', language);
  }

  // 窗口状态相关
  getWindowState(windowName) {
    return this.get(`windows.${windowName}`);
  }

  setWindowState(windowName, state) {
    this.set(`windows.${windowName}`, state);
  }

  // 最近使用的文件
  addRecentFile(filePath) {
    const recentFiles = this.get('recentFiles') || [];
    const filtered = recentFiles.filter(file => file !== filePath);
    filtered.unshift(filePath);
    
    // 保持最多10个最近文件
    this.set('recentFiles', filtered.slice(0, 10));
  }

  getRecentFiles() {
    return this.get('recentFiles') || [];
  }

  clearRecentFiles() {
    this.set('recentFiles', []);
  }

  // 获取设置摘要
  getSummary() {
    return {
      currentMode: this.get('currentMode'),
      aiEnabled: this.get('ai.enabled'),
      theme: this.get('ui.theme'),
      language: this.get('ui.language'),
      autoSave: this.get('organization.autoSave'),
      showTrayIcon: this.get('showTrayIcon'),
      totalCollections: this.get('stats.totalCollections') || 0,
      totalDocuments: this.get('stats.totalDocuments') || 0
    };
  }

  // 更新统计信息
  updateStats(stats) {
    this.set('stats', { ...this.get('stats'), ...stats });
  }

  // 检查设置版本和迁移
  checkMigration() {
    const currentVersion = this.get('version') || '1.0.0';
    const targetVersion = '1.0.0';
    
    if (currentVersion !== targetVersion) {
      console.log(`设置版本迁移: ${currentVersion} -> ${targetVersion}`);
      this.migrateSettings(currentVersion, targetVersion);
      this.set('version', targetVersion);
    }
  }

  migrateSettings(fromVersion, toVersion) {
    // 在这里实现设置迁移逻辑
    console.log(`执行设置迁移: ${fromVersion} -> ${toVersion}`);
  }

  // 获取设置文件路径
  getSettingsPath() {
    return this.store.path;
  }

  // 监听设置变化
  onChanged(key, callback) {
    this.on(`changed:${key}`, callback);
  }

  // 移除监听
  offChanged(key, callback) {
    this.off(`changed:${key}`, callback);
  }
}

module.exports = Settings;