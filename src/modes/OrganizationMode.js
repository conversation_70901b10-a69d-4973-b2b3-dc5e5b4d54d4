const { BrowserWindow, <PERSON>u, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const { EventEmitter } = require('events');
const AIService = require('../services/AIService');

class OrganizationMode extends EventEmitter {
  constructor(appController) {
    super();
    this.appController = appController;
    this.mainWindow = null;
    this.settingsWindow = null;
    this.exportWindow = null;
    this.isActive = false;
    this.settings = null;
    this.currentDocument = null;
    this.collections = [];
    this.analysisResults = null;
    this.aiService = new AIService(appController);
  }

  async activate() {
    try {
      console.log('激活整理模式...');
      
      // 获取设置
      this.settings = this.appController.settings.getOrganizationSettings();
      
      // 创建主窗口
      this.createMainWindow();
      
      // 设置IPC处理
      this.setupIPC();
      
      // 加载数据
      await this.loadCollections();
      
      this.isActive = true;
      console.log('整理模式激活完成');
      
    } catch (error) {
      console.error('整理模式激活失败:', error);
      throw error;
    }
  }

  async deactivate() {
    try {
      console.log('停用整理模式...');
      
      // 保存当前文档
      await this.saveCurrentDocument();
      
      // 隐藏主窗口
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.hide();
      }
      
      // 关闭设置窗口
      if (this.settingsWindow && !this.settingsWindow.isDestroyed()) {
        this.settingsWindow.close();
      }
      
      // 关闭导出窗口
      if (this.exportWindow && !this.exportWindow.isDestroyed()) {
        this.exportWindow.close();
      }
      
      this.isActive = false;
      console.log('整理模式停用完成');
      
    } catch (error) {
      console.error('整理模式停用失败:', error);
    }
  }

  createMainWindow() {
    const { width, height } = this.settings.windowSize;
    
    this.mainWindow = new BrowserWindow({
      width,
      height,
      minWidth: 800,
      minHeight: 600,
      show: false,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false // 开发环境禁用web安全性
      }
    });
    
    // 设置 Content Security Policy
    this.mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': ['default-src \'self\' \'unsafe-inline\' \'unsafe-eval\' data: blob:']
        }
      });
    });
    
    // 加载主界面
    this.mainWindow.loadFile(path.join(__dirname, '../ui/organization.html'));
    
    // 窗口事件
    this.mainWindow.on('ready-to-show', () => {
      if (!this.mainWindow.isDestroyed()) {
        this.mainWindow.show();
        this.mainWindow.focus();
        
        }
    });
    
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
    
    // 窗口大小变化时保存设置
    this.mainWindow.on('resize', () => {
      if (!this.mainWindow.isDestroyed()) {
        const [width, height] = this.mainWindow.getSize();
        this.appController.settings.updateOrganizationSettings({
          windowSize: { width, height }
        });
      }
    });
    
    // 监听页面加载完成
    this.mainWindow.webContents.on('did-finish-load', () => {
      console.log('Organization page loaded');
      // 初始化页面
      this.initializePage();
      // FIX: Load collections after the page is confirmed to be loaded
      this.loadCollections();
    });
    
    // 监听控制台消息
    this.mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
      console.log(`Console [${level}]: ${message}`);
    });
    
    // 创建应用菜单
    this.createApplicationMenu();
    
    console.log('主窗口创建完成');
  }

  createApplicationMenu() {
    const template = [
      {
        label: '文件',
        submenu: [
          {
            label: '新建文档',
            accelerator: 'CmdOrCtrl+N',
            click: () => this.createNewDocument()
          },
          {
            label: '打开文档',
            accelerator: 'CmdOrCtrl+O',
            click: () => this.openDocument()
          },
          {
            label: '保存文档',
            accelerator: 'CmdOrCtrl+S',
            click: () => this.saveCurrentDocument()
          },
          {
            label: '另存为',
            accelerator: 'CmdOrCtrl+Shift+S',
            click: () => this.saveDocumentAs()
          },
          { type: 'separator' },
          {
            label: '导出',
            submenu: [
              {
                label: '导出为 Markdown',
                click: () => this.exportAs('markdown')
              },
              {
                label: '导出为 HTML',
                click: () => this.exportAs('html')
              },
              {
                label: '导出为 PDF',
                click: () => this.exportAs('pdf')
              }
            ]
          },
          { type: 'separator' },
          {
            label: '切换到收集模式',
            accelerator: 'CmdOrCtrl+1',
            click: () => this.appController.switchMode('collection')
          }
        ]
      },
      {
        label: '编辑',
        submenu: [
          { role: 'undo', label: '撤销' },
          { role: 'redo', label: '重做' },
          { type: 'separator' },
          { role: 'cut', label: '剪切' },
          { role: 'copy', label: '复制' },
          { role: 'paste', label: '粘贴' },
          { role: 'selectall', label: '全选' },
          { type: 'separator' },
          {
            label: '搜索',
            accelerator: 'CmdOrCtrl+F',
            click: () => this.showSearch()
          },
          {
            label: '替换',
            accelerator: 'CmdOrCtrl+H',
            click: () => this.showReplace()
          }
        ]
      },
      {
        label: '视图',
        submenu: [
          { role: 'reload', label: '重新加载' },
          { role: 'forceReload', label: '强制重新加载' },
          { role: 'toggleDevTools', label: '切换开发者工具' },
          { type: 'separator' },
          { role: 'resetZoom', label: '重置缩放' },
          { role: 'zoomIn', label: '放大' },
          { role: 'zoomOut', label: '缩小' },
          { type: 'separator' },
          { role: 'togglefullscreen', label: '切换全屏' },
          { type: 'separator' },
          {
            label: '切换侧边栏',
            accelerator: 'CmdOrCtrl+B',
            click: () => this.toggleSidebar()
          },
          {
            label: '切换预览',
            accelerator: 'CmdOrCtrl+P',
            click: () => this.togglePreview()
          }
        ]
      },
      {
        label: 'AI',
        submenu: [
          {
            label: '分析收藏内容',
            accelerator: 'CmdOrCtrl+A',
            click: () => this.triggerAIAnalysis()
          },
          {
            label: '生成 Markdown',
            accelerator: 'CmdOrCtrl+M',
            click: () => this.generateMarkdown()
          },
          {
            label: '批量处理',
            click: () => this.batchProcess()
          },
          { type: 'separator' },
          {
            label: '检测重复内容',
            click: () => this.detectDuplicates()
          },
          {
            label: '提取关键词',
            click: () => this.extractKeywords()
          }
        ]
      },
      {
        label: '工具',
        submenu: [
          {
            label: '统计信息',
            click: () => this.showStatistics()
          },
          {
            label: '清理数据',
            click: () => this.cleanupData()
          },
          { type: 'separator' },
          {
            label: '设置',
            accelerator: 'CmdOrCtrl+,',
            click: () => this.showSettings()
          }
        ]
      },
      {
        label: '帮助',
        submenu: [
          {
            label: '关于',
            click: () => this.showAbout()
          },
          {
            label: '使用指南',
            click: () => this.showHelp()
          }
        ]
      }
    ];
    
    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  async loadCollections() {
    try {
      this.collections = await this.appController.getCollections();
      
      // 发送数据到渲染进程
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('collections-loaded', this.collections);
      }
      
      console.log(`加载了 ${this.collections.length} 个收藏`);
      
    } catch (error) {
      console.error('加载收藏失败:', error);
    }
  }

  setupIPC() {
    // 确保关键的跨模式IPC处理器存在
    this.ensureGlobalIPCHandlers();
    
    // 获取单个收藏
    this.appController.ipcManager.handle('get-collection', async (event, id) => {
      return await this.appController.database.getCollection(id);
    });
    
    // 更新收藏
    this.appController.ipcManager.handle('update-collection', async (event, id, data) => {
      return await this.appController.updateCollection(id, data);
    });
    
    // 删除收藏
    this.appController.ipcManager.handle('delete-collection', async (event, id) => {
      return await this.appController.deleteCollection(id);
    });
    
    // AI分析
    this.appController.ipcManager.handle('analyze-collections', async (event, items) => {
      return await this.appController.analyzeCollections(items);
    });
    
    // 生成Markdown
    this.appController.ipcManager.handle('generate-markdown', async (event, categories, items) => {
      return await this.appController.generateMarkdown(categories, items);
    });

    // AI配置管理
    this.appController.ipcManager.handle('ai-get-config', async (event) => {
      return this.appController.aiService.getConfig();
    });

    this.appController.ipcManager.handle('ai-update-config', async (event, config) => {
      this.appController.aiService.updateConfig(config);
      return { success: true };
    });

    this.appController.ipcManager.handle('ai-test-connection', async (event) => {
      return await this.appController.aiService.testConnection();
    });

    // AI对话功能
    this.appController.ipcManager.handle('ai-chat', async (event, message, history, options = {}) => {
      return await this.appController.aiService.chat(message, history, options);
    });
    
    // 流式AI对话功能
    this.appController.ipcManager.handle('ai-chat-stream', async (event, message, history) => {
      return await this.appController.aiService.chat(message, history, {
        stream: true,
        onData: (chunk) => {
          // 发送流式数据到渲染进程
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('ai-stream-data', chunk);
          }
        },
        onEnd: () => {
          // 发送流式结束信号
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('ai-stream-end');
          }
        },
        onError: (error) => {
          // 发送流式错误信号
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('ai-stream-error', error.message);
          }
        }
      });
    });

    // AI文本处理
    this.appController.ipcManager.handle('ai-process-text', async (event, text, action, context) => {
      return await this.appController.aiService.processText(text, action, context);
    });

    // AI内容生成
    this.appController.ipcManager.handle('ai-generate-content', async (event, topic, type, length, style) => {
      return await this.appController.aiService.generateContent(topic, type, length, style);
    });

    // AI统计信息
    this.appController.ipcManager.handle('ai-get-statistics', async (event) => {
      return this.appController.aiService.getStatistics();
    });
    
    // 创建文档
    this.appController.ipcManager.handle('create-document', async (event, data) => {
      return await this.appController.database.addDocument(data);
    });

    // 保存文档
    this.appController.ipcManager.handle('save-document', async (event, data) => {
      return await this.appController.database.addDocument(data);
    });
    
    // 获取文档列表
    this.appController.ipcManager.handle('get-documents', async (event) => {
      try {
        return await this.appController.database.getDocuments();
      } catch (error) {
        console.error('获取文档列表失败:', error);
        return [];
      }
    });

    // 获取单个文档
    this.appController.ipcManager.handle('get-document', async (event, id) => {
      try {
        return await this.appController.database.getDocument(id);
      } catch (error) {
        console.error('获取文档失败:', error);
        throw error;
      }
    });

    // 获取文档内容
    this.appController.ipcManager.handle('get-document-content', async (event, id) => {
      try {
        const doc = await this.appController.database.getDocument(id);
        return doc ? doc.content : '';
      } catch (error) {
        console.error('获取文档内容失败:', error);
        return '';
      }
    });
    
    // 更新文档
    this.appController.ipcManager.handle('update-document', async (event, id, data) => {
      try {
        return await this.appController.database.updateDocument(id, data);
      } catch (error) {
        console.error('更新文档失败:', error);
        throw error;
      }
    });
    
    // 删除文档
    this.appController.ipcManager.handle('delete-document', async (event, id) => {
      try {
        return await this.appController.database.deleteDocument(id);
      } catch (error) {
        console.error('删除文档失败:', error);
        throw error;
      }
    });
    
    // 导出数据
    this.appController.ipcManager.handle('export-data', async (event, format) => {
      return await this.appController.exportData(format);
    });
    
    // 搜索
    this.appController.ipcManager.handle('search-collections', async (event, query, options) => {
      return await this.appController.database.search(query, options);
    });
    
    // 获取统计信息
    this.appController.ipcManager.handle('get-statistics', async (event) => {
      return await this.appController.getStatistics();
    });
    
    // 清理数据
    this.appController.ipcManager.handle('cleanup-data', async (event) => {
      return await this.appController.database.cleanup();
    });
    
    // 切换模式
    this.appController.ipcManager.handle('switch-mode', async (event, mode) => {
      return await this.appController.switchMode(mode);
    });
    
    // 获取设置
    this.appController.ipcManager.handle('get-settings', async (event) => {
      return this.appController.settings.getAll();
    });
    
    // 更新设置
    this.appController.ipcManager.handle('update-settings', async (event, settings) => {
      this.appController.settings.setMany(settings);
      return true;
    });
    
    // 文件操作
    this.appController.ipcManager.handle('show-open-dialog', async (event, options) => {
      const result = await dialog.showOpenDialog(this.mainWindow, options);
      return result;
    });
    
    this.appController.ipcManager.handle('show-save-dialog', async (event, options) => {
      const result = await dialog.showSaveDialog(this.mainWindow, options);
      return result;
    });
    
    this.appController.ipcManager.handle('write-file', async (event, filePath, content) => {
      await fs.writeFile(filePath, content, 'utf8');
      return true;
    });
    
    this.appController.ipcManager.handle('read-file', async (event, filePath) => {
      return await fs.readFile(filePath, 'utf8');
    });
    
    // 打开外部链接
    this.appController.ipcManager.handle('open-external', async (event, url) => {
      shell.openExternal(url);
      return true;
    });
  }

  // 初始化页面
  initializePage() {
    try {
      // 发送初始化数据到渲染进程
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('page-initialized', {
          mode: 'organization',
          settings: this.appController.settings.getOrganizationSettings()
        });
      }
    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  }

  // 显示主窗口
  showMainWindow() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.show();
      this.mainWindow.focus();
    } else {
      // 如果窗口不存在，重新创建
      this.createMainWindow();
    }
  }

  // 隐藏主窗口
  hideMainWindow() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.hide();
    }
  }

  // 触发AI分析
  async triggerAIAnalysis() {
    try {
      // 发送分析开始信号
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('ai-analysis-started');
      }
      
      // 获取待分析的收藏
      const collections = await this.appController.getCollections({ ai_processed: false });
      
      if (collections.length === 0) {
        this.mainWindow.webContents.send('ai-analysis-completed', {
          success: false,
          message: '没有待分析的收藏'
        });
        return;
      }
      
      // 执行AI分析
      this.analysisResults = await this.appController.analyzeCollections(collections);
      
      // 发送分析结果
      this.mainWindow.webContents.send('ai-analysis-completed', {
        success: true,
        results: this.analysisResults,
        collections
      });
      
    } catch (error) {
      console.error('AI分析失败:', error);
      this.mainWindow.webContents.send('ai-analysis-completed', {
        success: false,
        error: error.message
      });
    }
  }

  // 生成Markdown
  async generateMarkdown() {
    try {
      if (!this.analysisResults) {
        throw new Error('请先进行AI分析');
      }
      
      // 发送生成开始信号
      this.mainWindow.webContents.send('markdown-generation-started');
      
      // 生成Markdown
      const markdown = await this.appController.generateMarkdown(
        this.analysisResults.categories,
        this.collections
      );
      
      // 发送生成结果
      this.mainWindow.webContents.send('markdown-generation-completed', {
        success: true,
        markdown
      });
      
    } catch (error) {
      console.error('Markdown生成失败:', error);
      this.mainWindow.webContents.send('markdown-generation-completed', {
        success: false,
        error: error.message
      });
    }
  }

  // 批量处理
  async batchProcess() {
    try {
      const collections = await this.appController.getCollections({ ai_processed: false });
      
      if (collections.length === 0) {
        dialog.showMessageBox(this.mainWindow, {
          type: 'info',
          title: '批量处理',
          message: '没有待处理的收藏'
        });
        return;
      }
      
      // 显示批量处理窗口
      this.showBatchProcessWindow(collections);
      
    } catch (error) {
      console.error('批量处理失败:', error);
      dialog.showErrorBox('批量处理失败', error.message);
    }
  }

  showBatchProcessWindow(collections) {
    const batchWindow = new BrowserWindow({
      width: 600,
      height: 400,
      parent: this.mainWindow,
      modal: true,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });
    
    batchWindow.loadFile(path.join(__dirname, '../ui/batch-process.html'));
    
    batchWindow.webContents.once('did-finish-load', () => {
      batchWindow.webContents.send('batch-data', collections);
    });
  }

  // 检测重复内容
  async detectDuplicates() {
    try {
      const collections = await this.appController.getCollections();
      const duplicates = await this.appController.aiService.detectDuplicates(collections);
      
      this.mainWindow.webContents.send('duplicates-detected', duplicates);
      
    } catch (error) {
      console.error('重复检测失败:', error);
      dialog.showErrorBox('重复检测失败', error.message);
    }
  }

  // 提取关键词
  async extractKeywords() {
    try {
      const collections = await this.appController.getCollections();
      const results = [];
      
      for (const collection of collections) {
        const keywords = await this.appController.aiService.extractKeywords(collection.content);
        results.push({ id: collection.id, keywords });
      }
      
      this.mainWindow.webContents.send('keywords-extracted', results);
      
    } catch (error) {
      console.error('关键词提取失败:', error);
      dialog.showErrorBox('关键词提取失败', error.message);
    }
  }

  // 显示设置
  showSettings() {
    if (this.settingsWindow) {
      this.settingsWindow.focus();
      return;
    }
    
    this.settingsWindow = new BrowserWindow({
      width: 800,
      height: 600,
      parent: this.mainWindow,
      modal: true,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });
    
    this.settingsWindow.loadFile(path.join(__dirname, '../ui/settings.html'));
    
    this.settingsWindow.on('closed', () => {
      this.settingsWindow = null;
    });
  }

  // 导出功能
  async exportAs(format) {
    try {
      const { filePath } = await dialog.showSaveDialog(this.mainWindow, {
        defaultPath: `qnotes-export.${format}`,
        filters: [
          { name: format.toUpperCase(), extensions: [format] }
        ]
      });
      
      if (!filePath) return;
      
      let content;
      if (format === 'markdown') {
        if (this.analysisResults) {
          content = await this.appController.generateMarkdown(
            this.analysisResults.categories,
            this.collections
          );
        } else {
          content = this.generateSimpleMarkdown();
        }
      } else {
        content = await this.appController.exportData(format);
      }
      
      await fs.writeFile(filePath, content, 'utf8');
      
      dialog.showMessageBox(this.mainWindow, {
        type: 'info',
        title: '导出成功',
        message: `文件已保存到: ${filePath}`
      });
      
    } catch (error) {
      console.error('导出失败:', error);
      dialog.showErrorBox('导出失败', error.message);
    }
  }

  generateSimpleMarkdown() {
    let markdown = '# 收藏内容\n\n';
    
    this.collections.forEach((collection, index) => {
      markdown += `## ${index + 1}. ${collection.type}\n\n`;
      markdown += `${collection.content}\n\n`;
      if (collection.source) {
        markdown += `*来源: ${collection.source}*\n\n`;
      }
      markdown += `*时间: ${collection.timestamp}*\n\n`;
      markdown += '---\n\n';
    });
    
    return markdown;
  }

  // 创建新文档
  async createNewDocument() {
    this.currentDocument = null;
    this.mainWindow.webContents.send('new-document');
  }

  // 打开文档
  async openDocument() {
    const { filePaths } = await dialog.showOpenDialog(this.mainWindow, {
      filters: [
        { name: 'Markdown', extensions: ['md', 'markdown'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });
    
    if (filePaths.length > 0) {
      const content = await fs.readFile(filePaths[0], 'utf8');
      this.mainWindow.webContents.send('open-document', { path: filePaths[0], content });
    }
  }

  // 保存当前文档
  async saveCurrentDocument() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('save-document-request');
    }
  }

  // 另存为
  async saveDocumentAs() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('save-document-as-request');
    }
  }

  // 显示搜索
  showSearch() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('show-search');
    }
  }

  // 显示替换
  showReplace() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('show-replace');
    }
  }

  // 切换侧边栏
  toggleSidebar() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('toggle-sidebar');
    }
  }

  // 切换预览
  togglePreview() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('toggle-preview');
    }
  }

  // 显示统计信息
  async showStatistics() {
    try {
      const stats = await this.appController.getStatistics();
      this.mainWindow.webContents.send('show-statistics', stats);
    } catch (error) {
      console.error('获取统计信息失败:', error);
      dialog.showErrorBox('获取统计信息失败', error.message);
    }
  }

  // 显示导出对话框
  async showExportDialog() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('show-export-dialog');
    }
  }

  // 清理数据
  async cleanupData() {
    const result = await dialog.showMessageBox(this.mainWindow, {
      type: 'question',
      buttons: ['确定', '取消'],
      title: '清理数据',
      message: '确定要清理30天前的未分类收藏吗？此操作无法撤销。'
    });
    
    if (result.response === 0) {
      try {
        const cleaned = await this.appController.database.cleanup();
        dialog.showMessageBox(this.mainWindow, {
          type: 'info',
          title: '清理完成',
          message: `已清理 ${cleaned} 条过期收藏`
        });
        
        // 重新加载数据
        await this.loadCollections();
        
      } catch (error) {
        console.error('数据清理失败:', error);
        dialog.showErrorBox('数据清理失败', error.message);
      }
    }
  }

  // 显示关于
  showAbout() {
    dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '关于 QNotes',
      message: 'QNotes - 双模式智能内容收集整理工具',
      detail: `版本: 1.0.0\n基于 Electron 和 AI 技术构建\n\n© 2024 Your Company`
    });
  }

  // 显示帮助
  showHelp() {
    shell.openExternal('https://github.com/yourcompany/qnotes/wiki');
  }

  // 确保全局IPC处理器存在
  ensureGlobalIPCHandlers() {
    const { clipboard } = require('electron');
    
    // 快速捕获处理器
    if (!this.appController.ipcManager.hasHandler('quick-capture')) {
      this.appController.ipcManager.handle('quick-capture', async (event, data) => {
        try {
          const result = await this.appController.addCollection({
            content: data.content,
            type: data.type || 'text',
            source: data.source || 'quick-capture',
            timestamp: new Date().toISOString()
          });
          
          return result;
        } catch (error) {
          console.error('快速捕获失败:', error);
          throw error;
        }
      });
    }
    
    // 模式切换处理器
    if (!this.appController.ipcManager.hasHandler('switch-to-organization')) {
      this.appController.ipcManager.handle('switch-to-organization', async (event) => {
        try {
          await this.appController.switchMode('organization');
          return true;
        } catch (error) {
          console.error('切换到整理模式失败:', error);
          throw error;
        }
      });
    }
    
    // 剪贴板处理器
    if (!this.appController.ipcManager.hasHandler('get-clipboard-content')) {
      this.appController.ipcManager.handle('get-clipboard-content', () => {
        const text = clipboard.readText();
        const image = clipboard.readImage();
        
        return {
          text,
          hasImage: !image.isEmpty(),
          image: image.isEmpty() ? null : image.toDataURL()
        };
      });
    }
    
    if (!this.appController.ipcManager.hasHandler('set-clipboard-content')) {
      this.appController.ipcManager.handle('set-clipboard-content', (event, content) => {
        clipboard.writeText(content);
      });
    }
  }

  // 数据变化处理
  onDataChanged() {
    this.loadCollections();
  }

  // 清理资源
  cleanup() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.close();
    }
    
    if (this.settingsWindow && !this.settingsWindow.isDestroyed()) {
      this.settingsWindow.close();
    }
    
    if (this.exportWindow && !this.exportWindow.isDestroyed()) {
      this.exportWindow.close();
    }
  }
}

module.exports = OrganizationMode;