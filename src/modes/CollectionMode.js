const { <PERSON><PERSON>er<PERSON>indow, Tray, <PERSON>u, nativeImage, ipcMain, clipboard, screen } = require('electron');
const path = require('path');
const { EventEmitter } = require('events');

class CollectionMode extends EventEmitter {
  constructor(appController) {
    super();
    this.appController = appController;
    this.tray = null;
    this.dragWindow = null;
    this.quickCaptureWindow = null;
    this.isListening = false;
    this.isDragging = false;
    this.settings = null;
    this.collectionCount = 0;
  }

  async activate() {
    try {
      console.log('激活收集模式...');
      
      // 获取设置
      this.settings = this.appController.settings.getCollectionSettings();
      
      // 设置IPC处理
      this.setupIPC();
      
      // 开始监听拖拽
      this.startGlobalDragListener();
      
      // 隐藏主窗口
      this.hideMainWindow();
      
      console.log('收集模式激活完成');
      
    } catch (error) {
      console.error('收集模式激活失败:', error);
      throw error;
    }
  }

  async deactivate() {
    try {
      console.log('停用收集模式...');
      
      // 停止监听拖拽
      this.stopGlobalDragListener();
      
      // 关闭拖拽窗口
      if (this.dragWindow) {
        this.dragWindow.close();
        this.dragWindow = null;
      }
      
      // 快速捕获窗口现在由AppController管理
      
      console.log('收集模式停用完成');
      
    } catch (error) {
      console.error('收集模式停用失败:', error);
    }
  }

  // 托盘现在由AppController管理

  // 托盘菜单、提示和计数现在由AppController管理

  startGlobalDragListener() {
    if (this.isListening) return;
    
    this.isListening = true;
    console.log('开始监听全局拖拽...');
    
    // 监听剪贴板变化（作为拖拽的替代方案）
    this.clipboardInterval = setInterval(() => {
      if (!this.isDragging) {
        this.checkClipboardContent();
      }
    }, 2000);
    
    // 显示使用提示
    this.showUsageTip();
  }

  showUsageTip() {
    // 显示使用提示
    console.log('💡 使用提示: 复制内容后按 Control+Option+Command+N 快速收藏，或右键托盘图标查看更多选项');
  }

  stopGlobalDragListener() {
    this.isListening = false;
    if (this.clipboardInterval) {
      clearInterval(this.clipboardInterval);
      this.clipboardInterval = null;
    }
    console.log('停止监听全局拖拽');
  }

  checkClipboardContent() {
    try {
      const content = clipboard.readText();
      if (content && content.length > 20) {
        // 检查是否是新内容
        if (content !== this.lastClipboardContent) {
          this.lastClipboardContent = content;
          
          // 显示智能提示（如果内容看起来有价值）
          if (this.isValuableContent(content)) {
            this.showQuickSaveNotification(content);
          }
        }
      }
    } catch (error) {
      // 忽略剪贴板读取错误
    }
  }

  isValuableContent(content) {
    // 判断内容是否值得收藏
    if (content.length < 20) return false;
    
    // 检查是否包含有价值的模式
    const valuablePatterns = [
      /https?:\/\/[^\s]+/,  // URL
      /[\u4e00-\u9fa5]{10,}/, // 中文内容
      /[A-Z][a-z]+\s+[A-Z][a-z]+/, // 标题格式
      /\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/, // 日期
      /\w+@\w+\.\w+/, // 邮箱
    ];
    
    return valuablePatterns.some(pattern => pattern.test(content));
  }

  showQuickSaveNotification(content) {
    // 检查是否最近已经显示过提示
    const now = Date.now();
    if (this.lastNotificationTime && now - this.lastNotificationTime < 10000) {
      return; // 10秒内不重复提示
    }
    
    this.lastNotificationTime = now;
    
    // 显示系统通知（如果支持）
    try {
      const { Notification } = require('electron');
      if (Notification.isSupported()) {
        const notification = new Notification({
          title: 'QNotes - 检测到新内容',
          body: `发现可收藏内容，按 Control+Option+Command+N 快速保存\n${content.substring(0, 50)}...`,
          icon: path.join(__dirname, '../assets/tray-icon.png')
        });
        
        notification.on('click', () => {
          this.appController.showQuickCapture();
        });
        
        notification.show();
      }
    } catch (error) {
      console.log('系统通知不可用，使用控制台提示');
      console.log(`💡 检测到新内容: "${content.substring(0, 50)}..." - 按 Control+Option+Command+N 快速收藏`);
    }
  }

  showDragWindow(content, type = 'text') {
    if (this.dragWindow) {
      this.dragWindow.close();
    }
    
    const { width, height } = this.settings.popupSize;
    const display = screen.getPrimaryDisplay();
    const { x, y } = screen.getCursorScreenPoint();
    
    this.dragWindow = new BrowserWindow({
      width,
      height,
      x: x - width / 2,
      y: y - height / 2,
      frame: false,
      transparent: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });
    
    // 加载拖拽弹窗HTML
    this.dragWindow.loadFile(path.join(__dirname, '../ui/drag-popup.html'));
    
    // 发送内容到窗口
    this.dragWindow.webContents.once('did-finish-load', () => {
      this.dragWindow.webContents.send('content-dropped', {
        content,
        type,
        timestamp: new Date().toISOString()
      });
    });
    
    // 设置窗口事件
    this.dragWindow.on('closed', () => {
      this.dragWindow = null;
      this.isDragging = false;
    });
    
    // 自动关闭窗口
    setTimeout(() => {
      if (this.dragWindow) {
        this.dragWindow.close();
      }
    }, this.settings.popupDuration);
    
    this.isDragging = true;
  }

  // 快速收藏现在由AppController管理

  hideMainWindow() {
    // 隐藏主窗口（如果存在）
    if (this.appController.organizationMode && this.appController.organizationMode.mainWindow) {
      this.appController.organizationMode.mainWindow.hide();
    }
  }

  // IPC 事件处理
  setupIPC() {
    // 保存收藏
    this.appController.ipcManager.handle('save-collection', async (event, data) => {
      try {
        const result = await this.appController.addCollection(data);
        
        // 更新计数
        await this.updateCollectionCount();
        
        // 发送成功消息
        event.sender.send('collection-saved', { success: true, result });
        
        return result;
      } catch (error) {
        console.error('保存收藏失败:', error);
        event.sender.send('collection-saved', { success: false, error: error.message });
        throw error;
      }
    });
    
    // 快速捕获 - 确保这个处理器始终可用
    if (!this.appController.ipcManager.hasHandler('quick-capture')) {
      this.appController.ipcManager.handle('quick-capture', async (event, data) => {
        try {
          const result = await this.appController.addCollection({
            content: data.content,
            type: data.type || 'text',
            source: data.source || 'quick-capture',
            timestamp: new Date().toISOString()
          });
          
          await this.updateCollectionCount();
          
          return result;
        } catch (error) {
          console.error('快速捕获失败:', error);
          throw error;
        }
      });
    }
    
    // 切换到整理模式 - 确保这个处理器始终可用
    if (!this.appController.ipcManager.hasHandler('switch-to-organization')) {
      this.appController.ipcManager.handle('switch-to-organization', async (event) => {
        try {
          await this.appController.switchMode('organization');
          return true;
        } catch (error) {
          console.error('切换到整理模式失败:', error);
          throw error;
        }
      });
    }
    
    // 获取剪贴板内容 - 确保这个处理器始终可用
    if (!this.appController.ipcManager.hasHandler('get-clipboard-content')) {
      this.appController.ipcManager.handle('get-clipboard-content', () => {
        const text = clipboard.readText();
        const image = clipboard.readImage();
        
        return {
          text,
          hasImage: !image.isEmpty(),
          image: image.isEmpty() ? null : image.toDataURL()
        };
      });
    }
    
    // 设置剪贴板内容 - 确保这个处理器始终可用
    if (!this.appController.ipcManager.hasHandler('set-clipboard-content')) {
      this.appController.ipcManager.handle('set-clipboard-content', (event, content) => {
        clipboard.writeText(content);
      });
    }
  }

  // 显示统计信息
  async showStatistics() {
    try {
      const stats = await this.appController.getStatistics();
      
      // 创建统计窗口
      const statsWindow = new BrowserWindow({
        width: 500,
        height: 400,
        frame: true,
        resizable: false,
        alwaysOnTop: true,
        webPreferences: {
          nodeIntegration: true,
          contextIsolation: false
        }
      });
      
      statsWindow.loadFile(path.join(__dirname, '../ui/statistics.html'));
      
      statsWindow.webContents.once('did-finish-load', () => {
        statsWindow.webContents.send('statistics-data', stats);
      });
      
      statsWindow.on('closed', () => {
        // 统计窗口关闭后的清理
      });
      
    } catch (error) {
      console.error('显示统计信息失败:', error);
    }
  }

  // 显示设置
  showSettings() {
    // 切换到整理模式并显示设置
    this.appController.switchMode('organization').then(() => {
      this.appController.organizationMode.showSettings();
    });
  }

  // 数据变化处理
  onDataChanged() {
    // 数据变化处理现在由AppController管理
    console.log('CollectionMode: 数据已更新');
  }

  // 设置变化处理
  onSettingsChanged(key, value) {
    if (key.startsWith('collection.')) {
      this.settings = this.appController.settings.getCollectionSettings();
      console.log('收集模式设置已更新:', key, value);
    }
  }

  // 清理资源
  cleanup() {
    this.stopGlobalDragListener();
    
    if (this.dragWindow) {
      this.dragWindow.close();
    }
    
    // 托盘和快速收藏现在由AppController管理
  }
}

module.exports = CollectionMode;