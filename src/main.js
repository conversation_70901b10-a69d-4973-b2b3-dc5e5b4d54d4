const { app, <PERSON><PERSON><PERSON><PERSON><PERSON>ow, <PERSON><PERSON>, Tray, globalShortcut, ipcMain } = require('electron');
const path = require('path');
const AppController = require('./controllers/AppController');

class QNotesApp {
  constructor() {
    this.appController = null;
    this.isDev = process.argv.includes('--dev');
  }

  async initialize() {
    try {
      // 创建应用控制器
      this.appController = new AppController();
      await this.appController.initialize();

      // 注册全局快捷键
      this.registerGlobalShortcuts();

      // 设置应用事件监听
      this.setupAppEvents();

      console.log('QNotes应用初始化完成');
    } catch (error) {
      console.error('应用初始化失败:', error);
      app.quit();
    }
  }

  registerGlobalShortcuts() {
    try {
      // 获取设置中的快捷键配置
      const shortcuts = this.appController?.settings?.getShortcutSettings() || {};
      
      // 切换到收集模式
      const collectionKey = shortcuts.switchToCollection || 'CommandOrControl+1';
      if (globalShortcut.register(collectionKey, () => {
        this.appController.switchMode('collection');
      })) {
        console.log(`收集模式快捷键已注册: ${collectionKey}`);
      }

      // 切换到整理模式
      const organizationKey = shortcuts.switchToOrganization || 'CommandOrControl+2';
      if (globalShortcut.register(organizationKey, () => {
        this.appController.switchMode('organization');
      })) {
        console.log(`整理模式快捷键已注册: ${organizationKey}`);
      }

      // 新建收藏（快速收藏现在是全局功能）
      const newCollectionKey = 'Control+Option+Command+N'; // 强制使用新快捷键，忽略旧配置
      if (globalShortcut.register(newCollectionKey, () => {
        // 快速收藏现在是全局功能，在任何模式下都可以使用
        this.appController.showQuickCapture();
      })) {
        console.log(`新建快捷键已注册: ${newCollectionKey}`);
      }

      // AI分析（整理模式）
      const aiAnalysisKey = shortcuts.aiAnalysis || 'CommandOrControl+A';
      if (globalShortcut.register(aiAnalysisKey, () => {
        if (this.appController.currentMode === 'organization') {
          this.appController.organizationMode.triggerAIAnalysis();
        }
      })) {
        console.log(`AI分析快捷键已注册: ${aiAnalysisKey}`);
      }

      // 搜索快捷键
      const searchKey = shortcuts.search || 'CommandOrControl+F';
      if (globalShortcut.register(searchKey, () => {
        if (this.appController.currentMode === 'organization') {
          this.appController.organizationMode.showSearch();
        }
      })) {
        console.log(`搜索快捷键已注册: ${searchKey}`);
      }

      // 导出快捷键
      const exportKey = shortcuts.export || 'CommandOrControl+E';
      if (globalShortcut.register(exportKey, () => {
        if (this.appController.currentMode === 'organization' && this.appController.organizationMode) {
          this.appController.organizationMode.showExportDialog();
        }
      })) {
        console.log(`导出快捷键已注册: ${exportKey}`);
      }
      
    } catch (error) {
      console.error('注册全局快捷键失败:', error);
    }
  }

  setupAppEvents() {
    // 当所有窗口都关闭时
    app.on('window-all-closed', () => {
      // 在收集模式下，应用应该继续运行（即使没有可见窗口）
      // 只有在非macOS平台且不是收集模式时才退出
      if (process.platform !== 'darwin' && 
          this.appController && 
          this.appController.currentMode !== 'collection') {
        app.quit();
      }
    });

    // 当应用被激活时（macOS）
    app.on('activate', () => {
      if (this.appController) {
        if (this.appController.currentMode === 'organization') {
          this.appController.organizationMode.showMainWindow();
        } else {
          // 在收集模式下，显示快速捕获窗口
          this.appController.showQuickCapture();
        }
      }
    });

    // 应用即将退出时
    app.on('before-quit', () => {
      // 注销全局快捷键
      globalShortcut.unregisterAll();
      
      // 清理资源
      if (this.appController) {
        this.appController.cleanup();
      }
    });
  }
}

// 应用入口点
const qnotesApp = new QNotesApp();

// 当 Electron 完成初始化时
app.whenReady().then(() => {
  qnotesApp.initialize();
});

// 阻止多个实例
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // 当第二个实例启动时，聚焦到现有实例
    if (qnotesApp.appController && qnotesApp.appController.currentMode === 'organization') {
      qnotesApp.appController.organizationMode.showMainWindow();
    }
  });
}