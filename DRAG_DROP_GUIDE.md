# QNotes 拖拽插入功能使用指南

## 功能概述

QNotes 现在支持精准的文本拖拽插入功能，让你可以轻松地将素材库中的内容拖拽到 Markdown 编辑器的任意位置。

## 主要特性

### 1. 精准位置插入
- 实时计算鼠标位置对应的文本光标位置
- 支持多种光标位置计算方法（现代浏览器API + 兜底方案）
- 可视化插入位置指示器

### 2. 智能内容格式化
- 文本内容：直接插入
- 链接：自动转换为 `[标题](链接)` 格式
- 图片：自动转换为 `![描述](图片链接)` 格式
- 代码：自动包装为代码块 ````

### 3. 流畅的交互体验
- 拖拽时的视觉反馈
- 自定义拖拽预览
- 动画效果和过渡

## 使用方法

### 基本操作

1. **选择素材**：在左侧素材库中找到要插入的内容
2. **开始拖拽**：点击并拖拽素材项
3. **定位插入点**：将鼠标移动到编辑器中的目标位置
4. **观察指示器**：蓝色闪烁的竖线显示插入位置
5. **完成插入**：松开鼠标完成插入

### 高级功能

#### 多种素材类型支持
- **文本素材**：直接插入原始文本
- **链接素材**：自动格式化为 Markdown 链接
- **图片素材**：自动格式化为 Markdown 图片
- **代码素材**：自动包装为代码块

#### 智能换行处理
- 自动在插入内容前后添加适当的换行符
- 避免内容粘连在一起
- 保持文档结构清晰

## 技术实现

### 核心组件

#### 1. DragSource (拖拽源)
```javascript
// 素材项设置为可拖拽
<div class="material-item" draggable="true" data-id="...">
```

#### 2. DropTarget (放置目标)
```javascript
// Markdown编辑器作为放置目标
class MarkdownEditor {
    bindDragEvents() {
        this.editor.addEventListener('dragover', this.handleDragOver);
        this.editor.addEventListener('drop', this.handleDrop);
    }
}
```

#### 3. CursorTracker (光标追踪)
```javascript
calculateCursorPosition(e) {
    // 方法1: 现代浏览器API
    if (document.caretPositionFromPoint) {
        return this.getTextPositionFromDOMPosition(caretPosition);
    }
    
    // 方法2: Webkit兼容
    if (document.caretRangeFromPoint) {
        return this.getTextPositionFromRange(range);
    }
    
    // 方法3: 手动计算兜底
    return this.calculatePositionManually(e, rect);
}
```

#### 4. TextInserter (文本插入)
```javascript
insertTextAtPosition(text, position) {
    const beforeText = currentValue.substring(0, position);
    const afterText = currentValue.substring(position);
    const newValue = beforeText + insertText + afterText;
    
    this.editor.value = newValue;
    this.editor.setSelectionRange(newCursorPosition, newCursorPosition);
}
```

### 精确位置计算

#### 三种计算方法

1. **现代浏览器API** (`document.caretPositionFromPoint`)
   - 最准确的方法
   - 支持 Firefox 等现代浏览器

2. **Webkit兼容** (`document.caretRangeFromPoint`)
   - Chrome/Safari 支持
   - 较好的兼容性

3. **手动计算兜底**
   - 基于字符宽度和行高计算
   - 确保所有浏览器都能工作

#### 字符宽度测量

```javascript
getAverageCharWidth() {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    const style = getComputedStyle(this.editor);
    
    context.font = `${style.fontSize} ${style.fontFamily}`;
    const metrics = context.measureText(testString);
    
    return metrics.width / testString.length;
}
```

### 可视化指示器

#### 插入位置指示器
```javascript
showInsertionIndicator(position) {
    const coordinates = this.getCoordinatesFromPosition(position);
    const rect = this.editor.getBoundingClientRect();
    
    this.insertionIndicator.style.left = `${rect.left + coordinates.x}px`;
    this.insertionIndicator.style.top = `${rect.top + coordinates.y}px`;
    this.insertionIndicator.style.display = 'block';
}
```

#### 坐标转换
```javascript
getCoordinatesFromPosition(position) {
    // 创建临时DOM元素测量文本位置
    const div = document.createElement('div');
    // 复制编辑器样式
    div.style.font = style.font;
    div.style.fontSize = style.fontSize;
    // ...
    
    // 设置文本到指定位置
    div.textContent = textBeforePosition;
    
    // 添加测量标记
    const span = document.createElement('span');
    span.textContent = '|';
    div.appendChild(span);
    
    // 获取位置
    const coordinates = {
        x: spanRect.left - divRect.left,
        y: spanRect.top - divRect.top
    };
    
    return coordinates;
}
```

## 性能优化

### 1. 防抖处理
```javascript
// 使用 requestAnimationFrame 优化拖拽事件
this.rafId = requestAnimationFrame(() => {
    this.handleDragOver(e);
});
```

### 2. 字符宽度缓存
```javascript
// 缓存字符宽度计算结果
if (this._charWidth) return this._charWidth;
this._charWidth = metrics.width / testString.length;
```

### 3. DOM操作优化
- 延迟移除临时元素
- 复用插入指示器
- 最小化重排和重绘

## 样式定制

### 拖拽状态样式
```css
.material-item.dragging {
    opacity: 0.5;
    transform: scale(0.95);
    cursor: grabbing;
}
```

### 插入指示器样式
```css
.markdown-insertion-indicator {
    width: 2px;
    height: 20px;
    background-color: #007acc;
    animation: blink 1s infinite;
}
```

### 拖拽预览样式
```css
.drag-preview {
    background: white;
    border: 1px solid #ddd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    max-width: 200px;
}
```

## 测试验证

### 测试页面
运行 `test-drag-drop.html` 来测试拖拽功能：

```bash
# 在项目根目录打开测试页面
open test-drag-drop.html
```

### 测试场景
1. 不同类型素材的拖拽
2. 不同位置的精确插入
3. 长文本中的位置计算
4. 多行文本的处理
5. 浏览器兼容性测试

## 故障排除

### 常见问题

1. **拖拽无响应**
   - 检查 `draggable="true"` 属性
   - 确认事件绑定正确
   - 查看控制台错误信息

2. **位置计算不准确**
   - 检查字体样式是否一致
   - 验证padding和margin设置
   - 测试不同浏览器的兼容性

3. **插入指示器不显示**
   - 检查CSS样式是否正确加载
   - 确认z-index层级设置
   - 验证DOM结构是否正确

### 调试技巧

1. **开启调试日志**
```javascript
console.log('开始拖拽素材:', material.id);
console.log('计算位置:', position);
console.log('插入内容:', contentToInsert);
```

2. **检查拖拽数据**
```javascript
console.log('拖拽数据:', e.dataTransfer.getData('application/x-material'));
```

3. **验证位置计算**
```javascript
console.log('鼠标位置:', e.clientX, e.clientY);
console.log('计算位置:', this.calculateCursorPosition(e));
```

## 扩展功能

### 未来改进方向

1. **多选拖拽**：支持同时拖拽多个素材
2. **拖拽排序**：素材库内的拖拽排序
3. **跨窗口拖拽**：支持从外部应用拖拽内容
4. **智能格式化**：根据上下文自动选择格式
5. **撤销重做**：拖拽操作的撤销支持

### 自定义扩展

你可以通过以下方式扩展拖拽功能：

1. **自定义素材类型**
```javascript
formatMaterialContent(material) {
    switch (material.type) {
        case 'custom-type':
            return `自定义格式: ${material.content}`;
        // ...
    }
}
```

2. **自定义插入逻辑**
```javascript
insertTextAtPosition(text, position) {
    // 添加自定义处理逻辑
    const processedText = this.customProcessor(text);
    // 调用原始插入方法
    super.insertTextAtPosition(processedText, position);
}
```

## 结语

QNotes 的拖拽插入功能为文档编辑提供了更加直观和高效的交互方式。通过精确的位置计算和智能的内容格式化，让素材管理和文档编写变得更加流畅。

如果你在使用过程中遇到任何问题或有改进建议，欢迎提交 Issue 或 Pull Request。 