<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a6fd8;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .chat-test {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .chat-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .chat-history {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #eee;
            padding: 10px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
        .message {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .user-message {
            background: #e3f2fd;
            text-align: right;
        }
        .ai-message {
            background: #f3e5f5;
            text-align: left;
        }
        .system-message {
            background: #fff3e0;
            text-align: center;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI对话功能测试</h1>
        
        <div class="test-section">
            <h2>1. API连接测试</h2>
            <p>测试API是否可以正常连接和响应</p>
            <button onclick="testAPIConnection()">测试API连接</button>
            <div id="apiTestResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>2. 直接API调用测试</h2>
            <p>直接调用API测试响应</p>
            <button onclick="testDirectAPI()">直接API调用</button>
            <div id="directApiResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>3. AI服务配置测试</h2>
            <p>检查AI服务配置是否正确</p>
            <button onclick="testAIConfig()">检查AI配置</button>
            <div id="configTestResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>4. 流式对话测试</h2>
            <p>测试流式AI对话功能</p>
            <div class="chat-test">
                <div id="chatHistory" class="chat-history"></div>
                <input type="text" id="chatInput" class="chat-input" placeholder="输入您的问题..." onkeypress="handleChatKeyPress(event)">
                <button onclick="sendChatMessage()">发送消息</button>
                <button onclick="clearChat()">清空对话</button>
            </div>
            <div id="chatTestResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h2>5. 文本处理测试</h2>
            <p>测试AI文本处理功能</p>
            <textarea id="textToProcess" rows="4" cols="50" placeholder="输入要处理的文本...">这是一个测试文本，请帮我改进它。</textarea><br>
            <button onclick="testTextProcessing('improve')">改进文本</button>
            <button onclick="testTextProcessing('summarize')">总结文本</button>
            <button onclick="testTextProcessing('expand')">扩展文本</button>
            <div id="textProcessResult" class="test-result"></div>
        </div>
    </div>

    <script>
        // 模拟ipcRenderer (如果在浏览器中测试)
        if (typeof window !== 'undefined' && !window.ipcRenderer) {
            window.ipcRenderer = {
                invoke: async (channel, ...args) => {
                    console.log('Mock ipcRenderer.invoke:', channel, args);
                    return { success: false, error: '在浏览器中运行，无法调用Electron IPC' };
                },
                on: (channel, callback) => {
                    console.log('Mock ipcRenderer.on:', channel);
                }
            };
        }

        let chatHistory = [];
        let isStreaming = false;

        // 测试API连接
        async function testAPIConnection() {
            const resultDiv = document.getElementById('apiTestResult');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在测试API连接...';
            
            try {
                const response = await fetch('https://api.tu-zi.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer sk-o2VwufJTd4Un6aUgTfSwON547FA1Ztz3upNEmepySuPPRgI2'
                    },
                    body: JSON.stringify({
                        model: 'claude-sonnet-4-20250514',
                        messages: [
                            { role: 'user', content: 'Hello, this is a connection test.' }
                        ],
                        max_tokens: 100
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = `✅ API连接成功！\n响应: ${data.choices[0].message.content}`;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = `❌ API连接失败: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ API连接错误: ${error.message}`;
            }
        }

        // 直接API调用测试
        async function testDirectAPI() {
            const resultDiv = document.getElementById('directApiResult');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在进行直接API调用...';
            
            try {
                const response = await fetch('https://api.tu-zi.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer sk-o2VwufJTd4Un6aUgTfSwON547FA1Ztz3upNEmepySuPPRgI2'
                    },
                    body: JSON.stringify({
                        model: 'claude-sonnet-4-20250514',
                        messages: [
                            { role: 'system', content: '你是一个专业的AI助手。' },
                            { role: 'user', content: '请简单介绍一下你自己。' }
                        ],
                        max_tokens: 200,
                        temperature: 0.7
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.choices && data.choices.length > 0) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = `✅ 直接API调用成功！\n\n响应内容:\n${data.choices[0].message.content}\n\n使用Token: ${data.usage?.total_tokens || 'N/A'}`;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = `❌ API响应格式错误:\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ 直接API调用失败: ${error.message}`;
            }
        }

        // 测试AI配置
        async function testAIConfig() {
            const resultDiv = document.getElementById('configTestResult');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在检查AI配置...';
            
            try {
                if (typeof ipcRenderer !== 'undefined') {
                    const config = await ipcRenderer.invoke('ai-get-config');
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = `✅ AI配置获取成功!\n\n配置信息:\n${JSON.stringify(config, null, 2)}`;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = '❌ 无法访问ipcRenderer，请在Electron环境中运行';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ 获取AI配置失败: ${error.message}`;
            }
        }

        // 发送聊天消息
        async function sendChatMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (!message || isStreaming) return;
            
            const historyDiv = document.getElementById('chatHistory');
            const resultDiv = document.getElementById('chatTestResult');
            
            // 添加用户消息
            addMessageToHistory('user', message);
            input.value = '';
            
            // 添加AI消息占位符
            const aiMessageDiv = addMessageToHistory('ai', '正在思考...');
            
            isStreaming = true;
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在发送消息...';
            
            try {
                if (typeof ipcRenderer !== 'undefined') {
                    // 监听流式响应
                    let aiResponse = '';
                    
                    ipcRenderer.on('ai-stream-data', (event, chunk) => {
                        aiResponse += chunk;
                        aiMessageDiv.textContent = aiResponse;
                        historyDiv.scrollTop = historyDiv.scrollHeight;
                    });
                    
                    ipcRenderer.on('ai-stream-end', () => {
                        isStreaming = false;
                        resultDiv.className = 'test-result success';
                        resultDiv.textContent = '✅ 消息发送成功！';
                        chatHistory.push({ role: 'assistant', content: aiResponse });
                    });
                    
                    ipcRenderer.on('ai-stream-error', (event, error) => {
                        isStreaming = false;
                        aiMessageDiv.textContent = `错误: ${error}`;
                        aiMessageDiv.className = 'message system-message';
                        resultDiv.className = 'test-result error';
                        resultDiv.textContent = `❌ 消息发送失败: ${error}`;
                    });
                    
                    const response = await ipcRenderer.invoke('ai-chat-stream', message, chatHistory);
                    
                    if (!response.success) {
                        throw new Error(response.error);
                    }
                    
                    chatHistory.push({ role: 'user', content: message });
                    
                } else {
                    // 浏览器环境下的模拟响应
                    setTimeout(() => {
                        aiMessageDiv.textContent = '这是一个模拟的AI响应，因为当前在浏览器环境中运行。';
                        isStreaming = false;
                        resultDiv.className = 'test-result info';
                        resultDiv.textContent = '浏览器环境模拟响应';
                    }, 1000);
                }
            } catch (error) {
                isStreaming = false;
                aiMessageDiv.textContent = `错误: ${error.message}`;
                aiMessageDiv.className = 'message system-message';
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ 聊天失败: ${error.message}`;
            }
        }

        // 添加消息到历史记录
        function addMessageToHistory(role, content) {
            const historyDiv = document.getElementById('chatHistory');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            messageDiv.textContent = content;
            historyDiv.appendChild(messageDiv);
            historyDiv.scrollTop = historyDiv.scrollHeight;
            return messageDiv;
        }

        // 清空聊天
        function clearChat() {
            document.getElementById('chatHistory').innerHTML = '';
            document.getElementById('chatTestResult').textContent = '';
            chatHistory = [];
        }

        // 处理回车键
        function handleChatKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendChatMessage();
            }
        }

        // 测试文本处理
        async function testTextProcessing(action) {
            const textArea = document.getElementById('textToProcess');
            const text = textArea.value.trim();
            const resultDiv = document.getElementById('textProcessResult');
            
            if (!text) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ 请输入要处理的文本';
                return;
            }
            
            resultDiv.className = 'test-result info';
            resultDiv.textContent = `正在${action}文本...`;
            
            try {
                if (typeof ipcRenderer !== 'undefined') {
                    const response = await ipcRenderer.invoke('ai-process-text', text, action);
                    
                    if (response.success) {
                        resultDiv.className = 'test-result success';
                        resultDiv.textContent = `✅ 文本处理成功！\n\n原文:\n${response.originalText}\n\n处理结果:\n${response.processedText}`;
                    } else {
                        resultDiv.className = 'test-result error';
                        resultDiv.textContent = `❌ 文本处理失败: ${response.error}`;
                    }
                } else {
                    resultDiv.className = 'test-result info';
                    resultDiv.textContent = '浏览器环境无法测试文本处理功能';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ 文本处理错误: ${error.message}`;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI对话功能测试页面加载完成');
            
            // 如果在Electron环境中，显示提示
            if (typeof ipcRenderer !== 'undefined') {
                console.log('检测到Electron环境，所有功能可用');
            } else {
                console.log('浏览器环境，部分功能受限');
            }
        });
    </script>
</body>
</html> 