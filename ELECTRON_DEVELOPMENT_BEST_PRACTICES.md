# Electron 开发最佳实践规则

## 目录
1. [安全最佳实践](#安全最佳实践)
2. [架构和进程模型](#架构和进程模型)
3. [IPC通信最佳实践](#ipc通信最佳实践)
4. [上下文隔离和预加载脚本](#上下文隔离和预加载脚本)
5. [内容安全策略](#内容安全策略)
6. [沙盒化](#沙盒化)
7. [依赖管理和构建](#依赖管理和构建)
8. [性能优化](#性能优化)
9. [跨平台兼容性](#跨平台兼容性)
10. [测试和调试](#测试和调试)

## 安全最佳实践

### 1. 内容加载安全

#### ✅ 好的做法
```javascript
// 使用 HTTPS 加载外部资源
browserWindow.loadURL('https://example.com')

// HTML 中使用 HTTPS
<script crossorigin src="https://example.com/react.js"></script>
<link rel="stylesheet" href="https://example.com/style.css">
```

#### ❌ 避免的做法
```javascript
// 不要使用 HTTP 加载外部资源
browserWindow.loadURL('http://example.com')

// HTML 中避免 HTTP
<script crossorigin src="http://example.com/react.js"></script>
<link rel="stylesheet" href="http://example.com/style.css">
```

### 2. BrowserWindow 安全配置

#### ✅ 推荐的安全配置
```javascript
const mainWindow = new BrowserWindow({
  webPreferences: {
    // 启用上下文隔离（默认启用）
    contextIsolation: true,
    
    // 禁用 Node.js 集成
    nodeIntegration: false,
    
    // 禁用 Node.js 在 Worker 中的集成
    nodeIntegrationInWorker: false,
    
    // 启用沙盒模式
    sandbox: true,
    
    // 使用预加载脚本安全地暴露 API
    preload: path.join(__dirname, 'preload.js'),
    
    // 启用 Web 安全（默认启用）
    webSecurity: true,
    
    // 禁用不安全的内容加载
    allowRunningInsecureContent: false,
    
    // 不启用实验性功能
    experimentalFeatures: false
  }
})
```

#### ❌ 不安全的配置
```javascript
const mainWindow = new BrowserWindow({
  webPreferences: {
    // 危险：禁用上下文隔离
    contextIsolation: false,
    
    // 危险：启用 Node.js 集成
    nodeIntegration: true,
    
    // 危险：禁用 Web 安全
    webSecurity: false,
    
    // 危险：允许不安全内容
    allowRunningInsecureContent: true,
    
    // 危险：启用实验性功能
    experimentalFeatures: true,
    
    // 危险：启用特定的 Blink 功能
    enableBlinkFeatures: 'ExecCommandInJavaScript'
  }
})
```

### 3. 权限请求处理

#### ✅ 安全的权限处理
```javascript
const { session } = require('electron')
const { URL } = require('node:url')

session
  .fromPartition('some-partition')
  .setPermissionRequestHandler((webContents, permission, callback) => {
    const parsedUrl = new URL(webContents.getURL())

    // 验证 URL 和权限类型
    if (permission === 'notifications' && parsedUrl.protocol === 'https:' && parsedUrl.host === 'example.com') {
      callback(true) // 批准权限请求
    } else {
      callback(false) // 拒绝权限请求
    }
  })
```

### 4. 导航控制

#### ✅ 限制导航到已知 URL
```javascript
const { app } = require('electron')
const { URL } = require('node:url')

app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)

    if (parsedUrl.origin !== 'https://example.com') {
      event.preventDefault()
    }
  })
})
```

### 5. 新窗口创建控制

#### ✅ 安全的窗口打开处理
```javascript
const { app, shell } = require('electron')

app.on('web-contents-created', (event, contents) => {
  contents.setWindowOpenHandler(({ url }) => {
    if (isSafeForExternalOpen(url)) {
      setImmediate(() => {
        shell.openExternal(url)
      })
    }
    return { action: 'deny' }
  })
})

function isSafeForExternalOpen(url) {
  // 实现 URL 安全检查逻辑
  return url.startsWith('https://') && !url.includes('malicious')
}
```

### 6. shell.openExternal 安全使用

#### ✅ 安全使用
```javascript
const { shell } = require('electron')

// 只打开受信任的 URL
shell.openExternal('https://example.com/index.html')
```

#### ❌ 不安全使用
```javascript
const { shell } = require('electron')

// 危险：直接使用用户控制的数据
shell.openExternal(USER_CONTROLLED_DATA_HERE)
```

## 架构和进程模型

### 1. 进程分离原则

Electron 应用由以下进程组成：
- **主进程**：管理应用生命周期和创建渲染进程
- **渲染进程**：显示 UI，每个 BrowserWindow 都有自己的渲染进程
- **预加载脚本**：在渲染进程中运行，但在独立的上下文中

### 2. 主进程职责

#### ✅ 主进程应该处理
```javascript
const { app, BrowserWindow, ipcMain, dialog } = require('electron')

// 应用生命周期管理
app.whenReady().then(createWindow)

// 窗口管理
function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js')
    }
  })
  
  mainWindow.loadFile('index.html')
}

// 系统级 API 调用
ipcMain.handle('dialog:openFile', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openFile'],
    filters: [
      { name: 'Text Files', extensions: ['txt'] }
    ]
  })
  return result.filePaths[0]
})

// 应用退出处理
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})
```

### 3. 渲染进程职责

#### ✅ 渲染进程应该处理
```javascript
// 用户界面逻辑
document.getElementById('openFileBtn').addEventListener('click', async () => {
  const filePath = await window.electronAPI.openFile()
  document.getElementById('filePath').textContent = filePath
})

// DOM 操作
function updateUI(data) {
  document.getElementById('content').innerHTML = data
}
```

## IPC通信最佳实践

### 1. 使用 contextBridge 安全暴露 API

#### ✅ 安全的 API 暴露（预加载脚本）
```javascript
const { contextBridge, ipcRenderer } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
  // 单向通信：渲染进程到主进程
  setTitle: (title) => ipcRenderer.send('set-title', title),
  
  // 双向通信：请求-响应模式
  openFile: () => ipcRenderer.invoke('dialog:openFile'),
  
  // 事件监听：主进程到渲染进程
  onUpdateCounter: (callback) => ipcRenderer.on('update-counter', (_event, value) => callback(value)),
  
  // 移除监听器
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
})
```

#### ❌ 不安全的 API 暴露
```javascript
// 危险：直接暴露 ipcRenderer
contextBridge.exposeInMainWorld('electronAPI', {
  send: ipcRenderer.send,
  on: ipcRenderer.on
})

// 危险：暴露完整的事件对象
contextBridge.exposeInMainWorld('electronAPI', {
  onUpdateCounter: (callback) => ipcRenderer.on('update-counter', callback)
})
```

### 2. 主进程 IPC 处理

#### ✅ 安全的 IPC 处理
```javascript
const { ipcMain } = require('electron')

// 使用 handle 处理异步请求
ipcMain.handle('get-user-data', async (event) => {
  // 验证发送者
  if (!validateSender(event.senderFrame)) {
    return null
  }
  
  return await getUserData()
})

// 使用 on 处理单向消息
ipcMain.on('set-title', (event, title) => {
  // 验证发送者
  if (!validateSender(event.senderFrame)) {
    return
  }
  
  const webContents = event.sender
  const win = BrowserWindow.fromWebContents(webContents)
  win.setTitle(title)
})

function validateSender(frame) {
  // 验证发送者的 URL
  const url = new URL(frame.url)
  return url.host === 'trusted-domain.com'
}
```

### 3. 渲染进程 IPC 使用

#### ✅ 正确的 IPC 使用
```javascript
// 使用暴露的 API
async function openFile() {
  try {
    const filePath = await window.electronAPI.openFile()
    displayFile(filePath)
  } catch (error) {
    console.error('Failed to open file:', error)
  }
}

// 监听主进程事件
window.electronAPI.onUpdateCounter((value) => {
  document.getElementById('counter').textContent = value
})
```

## 上下文隔离和预加载脚本

### 1. 上下文隔离

#### ✅ 启用上下文隔离（默认）
```javascript
const mainWindow = new BrowserWindow({
  webPreferences: {
    contextIsolation: true, // 默认为 true
    preload: path.join(__dirname, 'preload.js')
  }
})
```

### 2. 预加载脚本最佳实践

#### ✅ 安全的预加载脚本
```javascript
const { contextBridge, ipcRenderer } = require('electron')

// 只暴露必要的 API
contextBridge.exposeInMainWorld('electronAPI', {
  // 文件操作
  openFile: () => ipcRenderer.invoke('dialog:openFile'),
  saveFile: (content) => ipcRenderer.invoke('dialog:saveFile', content),
  
  // 应用控制
  minimize: () => ipcRenderer.send('window:minimize'),
  maximize: () => ipcRenderer.send('window:maximize'),
  close: () => ipcRenderer.send('window:close'),
  
  // 系统信息
  getVersion: () => ipcRenderer.invoke('app:getVersion'),
  
  // 事件监听
  onMenuAction: (callback) => {
    ipcRenderer.on('menu:action', (_event, action) => callback(action))
  }
})
```

### 3. TypeScript 类型定义

#### ✅ 类型安全的 API 定义
```typescript
// types/electron.d.ts
export interface IElectronAPI {
  openFile: () => Promise<string>
  saveFile: (content: string) => Promise<boolean>
  minimize: () => void
  maximize: () => void
  close: () => void
  getVersion: () => Promise<string>
  onMenuAction: (callback: (action: string) => void) => void
}

declare global {
  interface Window {
    electronAPI: IElectronAPI
  }
}
```

## 内容安全策略

### 1. HTML 中的 CSP

#### ✅ 严格的 CSP 策略
```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
  <title>My Electron App</title>
</head>
<body>
  <!-- 应用内容 -->
</body>
</html>
```

### 2. 通过 webRequest 设置 CSP

#### ✅ 动态 CSP 设置
```javascript
const { session } = require('electron')

session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
  callback({
    responseHeaders: {
      ...details.responseHeaders,
      'Content-Security-Policy': ['default-src \'self\' https://trusted-cdn.com']
    }
  })
})
```

## 沙盒化

### 1. 启用沙盒模式

#### ✅ 全局启用沙盒
```javascript
const { app } = require('electron')

// 在应用准备就绪之前启用沙盒
app.enableSandbox()

app.whenReady().then(() => {
  // 所有 BrowserWindow 都将被沙盒化
  const win = new BrowserWindow({
    webPreferences: {
      preload: path.join(__dirname, 'preload.js')
    }
  })
})
```

#### ✅ 单个窗口启用沙盒
```javascript
const win = new BrowserWindow({
  webPreferences: {
    sandbox: true,
    preload: path.join(__dirname, 'preload.js')
  }
})
```

### 2. 沙盒化的预加载脚本

#### ✅ 沙盒环境下的预加载脚本
```javascript
const { contextBridge, ipcRenderer } = require('electron')

// 在沙盒环境中，只能使用 contextBridge 暴露 API
contextBridge.exposeInMainWorld('electronAPI', {
  // 安全的 API 暴露
  platform: process.platform,
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  },
  
  // IPC 通信
  openFile: () => ipcRenderer.invoke('dialog:openFile')
})
```

## 依赖管理和构建

### 1. 依赖管理

#### ✅ 正确的依赖管理
```json
{
  "name": "my-electron-app",
  "version": "1.0.0",
  "main": "main.js",
  "scripts": {
    "start": "electron .",
    "build": "electron-builder",
    "dev": "electron . --inspect=5858"
  },
  "devDependencies": {
    "electron": "^latest",
    "electron-builder": "^latest"
  },
  "dependencies": {
    // 只包含运行时需要的依赖
  }
}
```

### 2. 构建配置

#### ✅ electron-builder 配置
```json
{
  "build": {
    "appId": "com.company.app",
    "productName": "My Electron App",
    "directories": {
      "output": "dist"
    },
    "files": [
      "main.js",
      "preload.js",
      "src/**/*",
      "node_modules/**/*"
    ],
    "mac": {
      "category": "public.app-category.productivity"
    },
    "win": {
      "target": "nsis"
    },
    "linux": {
      "target": "AppImage"
    }
  }
}
```

## 性能优化

### 1. 窗口优化

#### ✅ 优化窗口显示
```javascript
const win = new BrowserWindow({
  show: false, // 初始隐藏
  webPreferences: {
    preload: path.join(__dirname, 'preload.js')
  }
})

// 等待内容准备就绪再显示
win.once('ready-to-show', () => {
  win.show()
})
```

### 2. 内存管理

#### ✅ 正确的内存管理
```javascript
// 清理不再需要的监听器
ipcMain.removeHandler('old-handler')

// 清理定时器
clearInterval(intervalId)

// 窗口关闭时清理资源
win.on('closed', () => {
  // 清理资源
  win = null
})
```

### 3. 离屏渲染

#### ✅ 高性能离屏渲染
```javascript
const win = new BrowserWindow({
  webPreferences: {
    offscreen: true
  }
})

win.webContents.on('paint', (event, dirty, image) => {
  // 处理渲染帧
  updateBitmap(dirty, image.toBitmap())
})
```

## 跨平台兼容性

### 1. 平台特定代码

#### ✅ 平台检测
```javascript
const { app } = require('electron')

// 平台特定的应用行为
app.on('window-all-closed', () => {
  // macOS 上保持应用运行
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  // macOS 上重新创建窗口
  if (process.platform === 'darwin' && BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})
```

### 2. 路径处理

#### ✅ 跨平台路径处理
```javascript
const path = require('path')

// 使用 path.join 构建路径
const preloadPath = path.join(__dirname, 'preload.js')
const iconPath = path.join(__dirname, 'assets', 'icon.png')
```

## 测试和调试

### 1. 调试配置

#### ✅ VS Code 调试配置
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Electron Main",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}",
      "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron",
      "args": [".", "--inspect=5858"],
      "outputCapture": "std"
    }
  ]
}
```

### 2. 自动化测试

#### ✅ 测试框架集成
```javascript
const { Application } = require('spectron')
const electronPath = require('electron')

describe('Application launch', () => {
  let app

  beforeEach(async () => {
    app = new Application({
      path: electronPath,
      args: [path.join(__dirname, '..')]
    })
    await app.start()
  })

  afterEach(async () => {
    if (app && app.isRunning()) {
      await app.stop()
    }
  })

  it('shows an initial window', async () => {
    const count = await app.client.getWindowCount()
    expect(count).toBe(1)
  })
})
```

## 安全检查清单

### 部署前检查

- [ ] 启用上下文隔离
- [ ] 禁用 Node.js 集成
- [ ] 启用沙盒模式
- [ ] 设置严格的 CSP 策略
- [ ] 验证所有 IPC 通信
- [ ] 限制导航到可信域名
- [ ] 控制新窗口创建
- [ ] 安全使用 shell.openExternal
- [ ] 移除调试代码和日志
- [ ] 启用代码签名

### 运行时监控

- [ ] 监控内存使用
- [ ] 检查异常和错误
- [ ] 验证权限请求
- [ ] 监控网络请求
- [ ] 检查文件系统访问

## 总结

遵循这些最佳实践可以帮助您构建安全、高性能和可维护的 Electron 应用程序。关键原则包括：

1. **安全优先**：始终假设渲染进程不可信
2. **最小权限**：只暴露必要的 API
3. **进程隔离**：正确使用主进程和渲染进程
4. **安全通信**：使用 contextBridge 和 IPC 进行安全通信
5. **持续更新**：保持 Electron 和依赖项的最新版本

这些实践将帮助您构建既安全又高效的 Electron 应用程序。 