#!/usr/bin/env node

// 素材加载问题修复脚本
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const Database = require('./src/database/Database');

let mainWindow;
let database;

async function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false
    }
  });

  // 加载整理界面
  await mainWindow.loadFile('src/ui/organization.html');
  
  // 打开开发者工具
  mainWindow.webContents.openDevTools();
  
  console.log('窗口创建完成，等待页面加载...');
  
  // 等待页面完全加载后进行调试
  mainWindow.webContents.once('dom-ready', async () => {
    console.log('DOM已准备就绪，开始调试...');
    
    setTimeout(async () => {
      await debugMaterialsLoading();
    }, 2000);
  });
}

async function debugMaterialsLoading() {
  console.log('=== 开始调试素材加载问题 ===');
  
  try {
    // 注入调试代码到渲染进程
    const result = await mainWindow.webContents.executeJavaScript(`
      (async function() {
        const debug = {
          logs: [],
          errors: []
        };
        
        function log(message, data) {
          console.log(message, data);
          debug.logs.push({ message, data, timestamp: Date.now() });
        }
        
        function error(message, err) {
          console.error(message, err);
          debug.errors.push({ message, error: err?.message || err, timestamp: Date.now() });
        }
        
        try {
          // 1. 检查DOM元素
          log('1. 检查DOM元素');
          const materialsList = document.getElementById('materialsList');
          log('materialsList元素存在:', !!materialsList);
          
          if (materialsList) {
            log('materialsList当前内容:', materialsList.innerHTML.substring(0, 200));
          }
          
          // 2. 检查全局对象
          log('2. 检查全局对象');
          log('organizationApp存在:', typeof organizationApp);
          log('CoreUtils存在:', typeof CoreUtils);
          log('ipcRenderer存在:', typeof ipcRenderer);
          
          // 3. 检查材料管理器
          if (typeof organizationApp !== 'undefined' && organizationApp.materialsManager) {
            log('3. 检查材料管理器');
            const manager = organizationApp.materialsManager;
            log('materialsManager存在:', true);
            log('当前materials数组长度:', manager.materials ? manager.materials.length : 'null');
            
            if (manager.materials && manager.materials.length > 0) {
              log('前3个素材示例:', manager.materials.slice(0, 3).map(m => ({
                id: m.id,
                type: m.type,
                content: m.content ? m.content.substring(0, 50) + '...' : 'null'
              })));
            }
            
            // 4. 尝试手动加载素材
            log('4. 尝试手动加载素材');
            try {
              await manager.loadMaterials();
              log('手动加载完成，新的materials长度:', manager.materials.length);
              
              // 5. 检查渲染结果
              if (materialsList) {
                log('渲染后的materialsList内容:', materialsList.innerHTML.substring(0, 500));
              }
              
            } catch (loadError) {
              error('手动加载失败:', loadError);
            }
            
          } else {
            error('materialsManager不存在或未初始化');
          }
          
          // 6. 测试IPC通信
          log('6. 测试IPC通信');
          try {
            const collections = await ipcRenderer.invoke('get-collections');
            log('IPC get-collections成功，返回数量:', collections ? collections.length : 'null');
            
            if (collections && collections.length > 0) {
              log('IPC返回的前3个素材:', collections.slice(0, 3).map(c => ({
                id: c.id,
                type: c.type,
                content: c.content ? c.content.substring(0, 50) + '...' : 'null'
              })));
            }
            
          } catch (ipcError) {
            error('IPC通信失败:', ipcError);
          }
          
          return debug;
          
        } catch (err) {
          error('调试脚本执行失败:', err);
          return debug;
        }
      })();
    `);
    
    console.log('\n=== 渲染进程调试结果 ===');
    console.log('日志数量:', result.logs.length);
    console.log('错误数量:', result.errors.length);
    
    result.logs.forEach((log, index) => {
      console.log(`${index + 1}. ${log.message}`, log.data);
    });
    
    if (result.errors.length > 0) {
      console.log('\n错误信息:');
      result.errors.forEach((err, index) => {
        console.error(`${index + 1}. ${err.message}:`, err.error);
      });
    }
    
  } catch (error) {
    console.error('调试执行失败:', error);
  }
}

async function initDatabase() {
  try {
    console.log('初始化数据库...');
    database = new Database();
    await database.init();
    
    // 验证数据库内容
    const collections = await database.getCollections();
    console.log(`数据库初始化完成，包含 ${collections.length} 个收藏`);
    
    return true;
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return false;
  }
}

function setupIPC() {
  console.log('设置IPC处理器...');
  
  // get-collections处理器
  ipcMain.handle('get-collections', async (event, options = {}) => {
    try {
      console.log('IPC get-collections调用，参数:', options);
      const result = await database.getCollections(options);
      console.log(`IPC get-collections返回 ${result.length} 个项目`);
      
      // 验证数据格式
      if (!Array.isArray(result)) {
        console.error('警告：返回的不是数组:', typeof result);
        return [];
      }
      
      return result;
      
    } catch (error) {
      console.error('get-collections处理器错误:', error);
      throw error;
    }
  });
  
  // 其他IPC处理器
  ipcMain.handle('update-collection', async (event, id, data) => {
    try {
      return await database.updateCollection(id, data);
    } catch (error) {
      console.error('update-collection错误:', error);
      throw error;
    }
  });
  
  ipcMain.handle('delete-collection', async (event, id) => {
    try {
      return await database.deleteCollection(id);
    } catch (error) {
      console.error('delete-collection错误:', error);
      throw error;
    }
  });
  
  console.log('IPC处理器设置完成');
}

app.whenReady().then(async () => {
  console.log('=== 素材加载修复工具启动 ===');
  
  // 初始化数据库
  const dbInitialized = await initDatabase();
  if (!dbInitialized) {
    console.error('数据库初始化失败，退出应用');
    app.quit();
    return;
  }
  
  // 设置IPC处理器
  setupIPC();
  
  // 创建窗口
  await createWindow();
  
  console.log('修复工具启动完成，请查看开发者工具中的调试信息');
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});