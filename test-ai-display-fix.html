<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话显示修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
        }
        
        .editor-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .ai-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            height: 600px;
        }
        
        .ai-chat-history {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 15px;
            background: #fafafa;
        }
        
        .ai-message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 8px;
            max-width: 90%;
        }
        
        .ai-message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
        }
        
        .ai-message.assistant {
            background: #f1f3f4;
            color: #333;
        }
        
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
        }
        
        .message-role {
            font-weight: 600;
        }
        
        .message-time {
            opacity: 0.7;
        }
        
        .message-content {
            line-height: 1.5;
            margin-bottom: 8px;
        }
        
        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .action-btn:hover {
            background: #f0f0f0;
            border-color: #bbb;
        }
        
        .ai-input-area {
            display: flex;
            gap: 10px;
        }
        
        .ai-input-area textarea {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            min-height: 40px;
        }
        
        .ai-input-area button {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .editor-area {
            height: 400px;
        }
        
        .editor-area textarea {
            width: 100%;
            height: 100%;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background: #4caf50;
        }
        
        .notification.error {
            background: #f44336;
        }
        
        .notification.warning {
            background: #ff9800;
        }
        
        .notification.info {
            background: #2196f3;
        }
        
        .test-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 4px;
        }
        
        .test-controls button {
            margin-right: 10px;
            padding: 8px 16px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-controls button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <h1>AI对话显示修复测试</h1>
    
    <div class="test-controls">
        <button onclick="addTestMessage()">添加测试AI消息</button>
        <button onclick="addUserMessage()">添加用户消息</button>
        <button onclick="clearHistory()">清空历史</button>
        <button onclick="runDebugChecks()">运行调试检查</button>
    </div>
    
    <div class="container">
        <div class="editor-panel">
            <h3>编辑器</h3>
            <div class="editor-area">
                <textarea id="markdownEditor" placeholder="在这里编写内容...可以选择文本来测试替换功能"></textarea>
            </div>
        </div>
        
        <div class="ai-panel">
            <h3>AI助手</h3>
            <div class="ai-chat-history" id="aiChatHistory">
                <div class="ai-welcome">
                    <h4>AI 助手</h4>
                    <p>你好！我是你的AI写作助手。你可以：</p>
                    <ul>
                        <li>与我对话，获取写作建议</li>
                        <li>选择文本后使用操作按钮</li>
                        <li>让我帮你改进、总结或扩展内容</li>
                    </ul>
                </div>
            </div>
            
            <div class="ai-input-area">
                <textarea id="aiInput" placeholder="输入消息..."></textarea>
                <button onclick="sendTestMessage()">发送</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟AI管理器
        class TestAIManager {
            constructor() {
                this.aiHistory = [];
                this.currentAIMessage = null;
                this.isAIStreaming = false;
                this.bindEvents();
            }
            
            bindEvents() {
                // 绑定AI消息操作按钮事件（使用事件委托）
                const aiChatHistory = document.getElementById('aiChatHistory');
                if (aiChatHistory) {
                    aiChatHistory.addEventListener('click', (event) => {
                        const button = event.target.closest('.action-btn');
                        if (!button) return;
                        
                        event.preventDefault();
                        event.stopPropagation();
                        
                        const action = button.getAttribute('data-action');
                        const content = button.getAttribute('data-content');
                        const messageIndex = button.getAttribute('data-message-index');
                        
                        switch (action) {
                            case 'copy':
                                this.copyToClipboard(content);
                                break;
                            case 'insert':
                                this.insertToEditor(content);
                                break;
                            case 'replace':
                                this.replaceSelectedText(content);
                                break;
                            case 'regenerate':
                                this.regenerateMessage(parseInt(messageIndex));
                                break;
                            default:
                                console.warn('未知的操作类型:', action);
                        }
                    });
                }
            }
            
            copyToClipboard(text) {
                if (!text) {
                    this.showNotification('没有内容可复制', 'warning');
                    return;
                }
                
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(text).then(() => {
                        this.showNotification('已复制到剪贴板', 'success');
                    }).catch(err => {
                        console.error('复制失败:', err);
                        this.fallbackCopyToClipboard(text);
                    });
                } else {
                    this.fallbackCopyToClipboard(text);
                }
            }
            
            fallbackCopyToClipboard(text) {
                try {
                    const textarea = document.createElement('textarea');
                    textarea.value = text;
                    textarea.style.position = 'fixed';
                    textarea.style.opacity = '0';
                    textarea.style.left = '-9999px';
                    document.body.appendChild(textarea);
                    
                    textarea.select();
                    textarea.setSelectionRange(0, 99999);
                    const successful = document.execCommand('copy');
                    
                    document.body.removeChild(textarea);
                    
                    if (successful) {
                        this.showNotification('已复制到剪贴板', 'success');
                    } else {
                        this.showNotification('复制失败，请手动复制', 'error');
                    }
                } catch (err) {
                    console.error('复制失败:', err);
                    this.showNotification('复制失败: ' + err.message, 'error');
                }
            }
            
            insertToEditor(text) {
                if (!text) {
                    this.showNotification('没有内容可插入', 'warning');
                    return;
                }
                
                const editor = document.getElementById('markdownEditor');
                if (!editor) {
                    this.showNotification('编辑器未找到', 'error');
                    return;
                }
                
                try {
                    const startPos = editor.selectionStart;
                    const endPos = editor.selectionEnd;
                    const beforeText = editor.value.substring(0, startPos);
                    const afterText = editor.value.substring(endPos);
                    
                    editor.value = beforeText + text + afterText;
                    editor.selectionStart = editor.selectionEnd = startPos + text.length;
                    editor.focus();
                    
                    this.showNotification('已插入到编辑器', 'success');
                } catch (error) {
                    console.error('插入失败:', error);
                    this.showNotification('插入失败: ' + error.message, 'error');
                }
            }
            
            replaceSelectedText(text) {
                if (!text) {
                    this.showNotification('没有内容可替换', 'warning');
                    return;
                }
                
                const editor = document.getElementById('markdownEditor');
                if (!editor) {
                    this.showNotification('编辑器未找到', 'error');
                    return;
                }
                
                try {
                    const startPos = editor.selectionStart;
                    const endPos = editor.selectionEnd;
                    
                    if (startPos === endPos) {
                        this.showNotification('请先选择要替换的文本', 'warning');
                        return;
                    }
                    
                    const beforeText = editor.value.substring(0, startPos);
                    const afterText = editor.value.substring(endPos);
                    
                    editor.value = beforeText + text + afterText;
                    editor.selectionStart = startPos;
                    editor.selectionEnd = startPos + text.length;
                    editor.focus();
                    
                    this.showNotification('已替换选中文本', 'success');
                } catch (error) {
                    console.error('替换失败:', error);
                    this.showNotification('替换失败: ' + error.message, 'error');
                }
            }
            
            regenerateMessage(messageIndex) {
                this.showNotification('重新生成功能已触发', 'info');
                console.log('重新生成消息:', messageIndex);
            }
            
            showNotification(message, type = 'info', duration = 3000) {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.classList.add('show');
                }, 100);
                
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, duration);
            }
            
            escapeHtml(text) {
                if (!text) return '';
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
            
            formatTime(timestamp) {
                const now = new Date();
                const time = new Date(timestamp);
                const diff = now - time;

                if (diff < 60000) return '刚刚';
                if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
                if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
                
                return time.toLocaleTimeString();
            }
            
            renderAIHistory() {
                const container = document.getElementById('aiChatHistory');
                if (!container) return;
                
                if (this.aiHistory.length === 0) {
                    container.innerHTML = `
                        <div class="ai-welcome">
                            <h4>AI 助手</h4>
                            <p>你好！我是你的AI写作助手。你可以：</p>
                            <ul>
                                <li>与我对话，获取写作建议</li>
                                <li>选择文本后使用操作按钮</li>
                                <li>让我帮你改进、总结或扩展内容</li>
                            </ul>
                        </div>
                    `;
                    return;
                }
                
                const html = this.aiHistory.map(message => this.renderAIMessage(message)).join('');
                container.innerHTML = html;
                
                container.scrollTop = container.scrollHeight;
            }
            
            renderAIMessage(message) {
                const isUser = message.role === 'user';
                const isAssistant = message.role === 'assistant';
                const className = isUser ? 'user' : 'assistant';
                
                let contentHtml = `<div class="message-content">${message.content}</div>`;
                let headerHtml = `
                    <div class="message-header">
                        <span class="message-role">${isUser ? '你' : 'AI助手'}</span>
                        <span class="message-time">${this.formatTime(message.timestamp)}</span>
                    </div>
                `;
                
                if (isAssistant) {
                    contentHtml += `
                        <div class="message-actions">
                            <button class="action-btn copy-btn" data-action="copy" data-content="${this.escapeHtml(message.content)}" title="复制">
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                                </svg>
                                复制
                            </button>
                            <button class="action-btn insert-btn" data-action="insert" data-content="${this.escapeHtml(message.content)}" title="插入">
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                </svg>
                                插入
                            </button>
                            <button class="action-btn replace-btn" data-action="replace" data-content="${this.escapeHtml(message.content)}" title="替换">
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z"/>
                                </svg>
                                替换
                            </button>
                            <button class="action-btn regenerate-btn" data-action="regenerate" data-message-index="${this.aiHistory.indexOf(message)}" title="重新生成">
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                                </svg>
                                重新生成
                            </button>
                        </div>
                    `;
                }
                
                return `
                    <div class="ai-message ${className}">
                        ${headerHtml}
                        ${contentHtml}
                    </div>
                `;
            }
        }
        
        // 初始化测试AI管理器
        const testAIManager = new TestAIManager();
        
        // 测试函数
        function addTestMessage() {
            const testMessage = {
                role: 'assistant',
                content: '这是一条测试AI消息。你可以点击下方的按钮来测试复制、插入、替换等功能。这段文本足够长，可以用来测试各种操作的效果。',
                timestamp: Date.now()
            };
            
            testAIManager.aiHistory.push(testMessage);
            testAIManager.renderAIHistory();
        }
        
        function addUserMessage() {
            const userMessage = {
                role: 'user',
                content: '这是一条用户消息，用于测试对话历史的显示效果。',
                timestamp: Date.now()
            };
            
            testAIManager.aiHistory.push(userMessage);
            testAIManager.renderAIHistory();
        }
        
        function clearHistory() {
            testAIManager.aiHistory = [];
            testAIManager.renderAIHistory();
        }
        
        function sendTestMessage() {
            const input = document.getElementById('aiInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            testAIManager.aiHistory.push({
                role: 'user',
                content: message,
                timestamp: Date.now()
            });
            
            // 模拟AI回复
            setTimeout(() => {
                testAIManager.aiHistory.push({
                    role: 'assistant',
                    content: `这是对"${message}"的AI回复。你可以使用下方的按钮来测试各种操作功能。`,
                    timestamp: Date.now()
                });
                testAIManager.renderAIHistory();
            }, 500);
            
            input.value = '';
            testAIManager.renderAIHistory();
        }
        
        function runDebugChecks() {
            console.log('=== 调试检查 ===');
            console.log('AI历史记录:', testAIManager.aiHistory);
            console.log('aiChatHistory元素:', document.getElementById('aiChatHistory'));
            console.log('markdownEditor元素:', document.getElementById('markdownEditor'));
            
            const actionButtons = document.querySelectorAll('.action-btn');
            console.log('操作按钮数量:', actionButtons.length);
            
            actionButtons.forEach((btn, index) => {
                console.log(`按钮 ${index + 1}:`, {
                    action: btn.getAttribute('data-action'),
                    content: btn.getAttribute('data-content'),
                    messageIndex: btn.getAttribute('data-message-index')
                });
            });
            
            testAIManager.showNotification('调试检查完成，请查看控制台', 'info');
        }
        
        // 键盘事件
        document.getElementById('aiInput').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendTestMessage();
            }
        });
        
        // 初始化时添加一条测试消息
        addTestMessage();
    </script>
</body>
</html> 