# QNotes 项目文档

## 📖 项目概述

QNotes 是一个现代化的双模式智能内容收集整理工具，基于 Electron 开发，集成了先进的AI助手功能。

### 核心特性
- 🔄 **双模式设计**：收集模式 + 整理模式
- 🤖 **AI 智能助手**：流式对话、文本处理、内容生成
- 📱 **跨平台支持**：Windows、macOS、Linux
- 🎨 **现代化UI**：基于Material Design 3原则
- 🗂️ **智能分类**：自动整理和分类内容
- 📝 **Markdown编辑**：支持实时预览和编辑

## 🏗️ 项目架构

### 目录结构
```
qnotes/
├── src/                        # 源代码目录
│   ├── main.js                 # 主进程入口
│   ├── controllers/            # 控制器层
│   │   ├── AppController.js    # 应用主控制器
│   │   └── IPCManager.js       # IPC通信管理器
│   ├── modes/                  # 模式实现
│   │   ├── QuickCaptureMode.js # 收集模式
│   │   └── OrganizationMode.js # 整理模式
│   ├── services/               # 服务层
│   │   ├── DatabaseService.js  # 数据库服务
│   │   ├── SettingsService.js  # 设置服务
│   │   └── AIService.js        # AI服务
│   ├── utils/                  # 工具函数
│   │   └── helpers.js          # 帮助函数
│   └── ui/                     # 用户界面
│       ├── quick-capture.html  # 收集模式界面
│       ├── organization.html   # 整理模式界面
│       ├── settings.html       # 设置界面
│       ├── drag-popup.html     # 拖拽弹窗
│       ├── scripts/            # 前端脚本
│       │   ├── quick-capture.js
│       │   ├── organization.js
│       │   ├── markdown-editor.js
│       │   ├── drag-popup.js
│       │   └── settings.js
│       └── styles/             # 样式文件
│           ├── quick-capture.css
│           ├── organization.css
│           ├── settings.css
│           └── drag-popup.css
├── assets/                     # 资源文件
│   └── icons/                  # 图标文件
├── package.json               # 项目配置
└── README.md                  # 项目说明
```

## 🗄️ 数据库结构

### SQLite 数据库表结构

#### 1. collections 表（素材表）
```sql
CREATE TABLE collections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content TEXT NOT NULL,           -- 内容文本
    type VARCHAR(20) DEFAULT 'text', -- 类型：text/image/link/code/video/audio
    source VARCHAR(255),             -- 来源信息
    image_data TEXT,                 -- 图片数据（base64）
    metadata TEXT,                   -- 元数据（JSON格式）
    category VARCHAR(100),           -- 分类
    keywords TEXT,                   -- 关键词（逗号分隔）
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. documents 表（文档表）
```sql
CREATE TABLE documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,     -- 标题
    content TEXT,                    -- 内容
    folder VARCHAR(255),             -- 文件夹
    metadata TEXT,                   -- 元数据（JSON格式）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. settings 表（设置表）
```sql
CREATE TABLE settings (
    key VARCHAR(255) PRIMARY KEY,    -- 设置键
    value TEXT,                      -- 设置值（JSON格式）
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 数据迁移系统
- 版本管理：通过 `settings.db_version` 跟踪数据库版本
- 自动迁移：应用启动时自动检查并执行必要的数据库升级
- 迁移脚本：位于 `DatabaseService.js` 的 `runMigrations()` 方法

## 🤖 AI 服务集成

### AI 服务架构
- **服务类**：`AIService.js`
- **API 提供商**：tu-zi.com（Claude Sonnet 4）
- **支持模型**：Claude、GPT-4 系列
- **通信方式**：基于 HTTP 的流式API

### 核心功能

#### 1. 流式对话
```javascript
// 主进程 IPC 处理
this.appController.ipcManager.handle('ai-chat-stream', async (event, message, history) => {
  return await this.aiService.chat(message, history, {
    stream: true,
    onData: (chunk) => {
      this.mainWindow.webContents.send('ai-stream-data', chunk);
    },
    onEnd: () => {
      this.mainWindow.webContents.send('ai-stream-end');
    }
  });
});

// 前端监听
ipcRenderer.on('ai-stream-data', (event, chunk) => {
  this.currentAIContent += chunk;
  this.updateAIMessage();
});
```

#### 2. 文本处理
- **重写**：`rewrite`
- **扩写**：`expand`
- **总结**：`summarize`
- **改进**：`improve`
- **翻译**：`translate`
- **解释**：`explain`

#### 3. 内容生成
- **类型**：文章、总结、大纲、列表、指南、教程
- **长度**：短（100-300字）、中（300-800字）、长（800-1500字）
- **风格**：专业、轻松、技术性、创意

#### 4. 智能分析
- **内容分类**：自动识别内容类别
- **关键词提取**：提取核心关键词
- **重复检测**：识别相似内容
- **统计分析**：生成内容统计报告

## 🎯 核心功能实现

### 1. 收集模式（QuickCaptureMode）
**文件位置**：`src/modes/QuickCaptureMode.js` + `src/ui/quick-capture.html`

**主要功能**：
- 全局快捷键唤起（默认：Cmd/Ctrl + Shift + C）
- 文本、图片、链接快速收集
- 拖拽收集（拖拽弹窗：`drag-popup.html`）
- 自动分类和标签
- 实时保存到数据库

**核心方法**：
```javascript
// 显示快速收集窗口
async show(options = {}) {
  // 窗口配置和显示逻辑
}

// 保存收集内容
async saveCollection(data) {
  // 数据验证和保存到数据库
}
```

### 2. 整理模式（OrganizationMode）
**文件位置**：`src/modes/OrganizationMode.js` + `src/ui/organization.html`

**主要功能**：
- 三栏布局：素材管理 + Markdown编辑器 + AI助手/预览
- 素材管理：现代卡片式列表，支持搜索、筛选、批量操作
- 文件管理：树形结构，支持文件夹创建和组织
- Markdown编辑：实时预览、工具栏、快捷键
- AI助手：流式对话、文本处理、内容生成

**核心组件**：
```javascript
class OrganizationApp {
  // 素材管理
  async loadMaterials()           // 加载素材列表
  renderMaterialItem(material)    // 渲染单个素材卡片
  async editMaterial(id)          // 编辑素材
  
  // 文件管理
  async createNewFile()           // 创建新文件
  async createNewFolder()         // 创建文件夹
  showInputModal(title, message, defaultValue, callback) // 输入对话框
  
  // AI功能
  async sendAIMessage()           // 发送AI消息
  setupAIStreamListeners()        // 设置流式监听
  async processSelectedText(action) // 处理选中文本
  
  // 拖拽功能
  getTextPositionFromPoint(x, y)  // 获取精确文本位置
  showDragIndicator(x, y)         // 显示拖拽指示器
}
```

### 3. 拖拽精准插入系统
**实现位置**：`organization.js` 的拖拽相关方法

**核心特性**：
- 使用浏览器原生API (`caretPositionFromPoint`) 实现精确定位
- 支持中文字符宽度特殊计算
- 动态字体度量和行高检测
- 实时视觉指示器（渐变色、动画效果）

**关键算法**：
```javascript
getTextPositionFromPoint(clientX, clientY) {
  // 1. 尝试使用原生API
  if (document.caretPositionFromPoint) {
    const range = document.caretPositionFromPoint(clientX, clientY);
    // 返回精确位置
  }
  
  // 2. 回退到计算方法
  const charMetrics = this.getCharacterMetrics();
  const lineHeight = this.getActualLineHeight();
  // 基于字符度量计算位置
}
```

### 4. 现代化UI设计
**设计原则**：基于Material Design 3和2024年最佳实践

**核心特性**：
- **无边框设计**：减少视觉噪音
- **单焦点卡片**：每个素材卡片聚焦单一内容
- **微交互动画**：悬停效果、变换动画
- **响应式布局**：适配不同屏幕尺寸
- **深色模式支持**：完整的深色主题

**样式架构**：
```css
/* 现代卡片设计 */
.material-item {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 12px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

/* 悬停效果 */
.material-item:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}
```

## 🔧 关键技术实现

### 1. IPC 通信架构
**文件位置**：`src/controllers/IPCManager.js`

**设计模式**：
- 主进程注册处理器
- 渲染进程调用 `ipcRenderer.invoke()`
- 支持同步和异步通信
- 错误处理和超时机制

### 2. 设置管理系统
**文件位置**：`src/services/SettingsService.js`

**功能特性**：
- JSON格式配置存储
- 实时配置更新
- 默认值管理
- 配置验证和迁移

### 3. 数据库服务
**文件位置**：`src/services/DatabaseService.js`

**核心功能**：
- SQLite数据库操作
- 自动迁移系统
- 事务支持
- 查询构建器

### 4. Markdown编辑器
**文件位置**：`src/ui/scripts/markdown-editor.js`

**功能特性**：
- 实时预览
- 语法高亮
- 工具栏操作
- 快捷键支持
- 拖拽插入

## 🚀 开发指南

### 环境要求
- Node.js 16+
- npm 或 yarn
- 支持的操作系统：Windows 10+, macOS 10.15+, Linux

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm start
```

### 打包构建
```bash
npm run build
```

### 代码结构规范
1. **类名**：PascalCase（如：`OrganizationApp`）
2. **方法名**：camelCase（如：`loadMaterials`）
3. **文件名**：kebab-case（如：`organization.html`）
4. **常量**：UPPER_SNAKE_CASE（如：`DEFAULT_CONFIG`）

### Git工作流
1. 功能开发：`feature/功能名称`
2. 错误修复：`fix/问题描述`
3. 文档更新：`docs/更新内容`
4. 样式调整：`style/样式名称`

## 🔍 常见问题排查

### 1. AI功能不工作
- 检查网络连接
- 验证API Key配置
- 查看控制台错误信息
- 测试API连接状态

### 2. 拖拽插入不准确
- 检查字体设置
- 确认编辑器焦点状态
- 验证文本区域边界计算

### 3. 数据库问题
- 检查文件权限
- 查看数据库版本
- 运行数据库迁移

### 4. 界面样式问题
- 清除CSS缓存
- 检查CSS文件完整性
- 验证响应式断点

## 📋 待优化项目

### 短期目标
- [ ] 增加更多AI模型支持
- [ ] 优化大文件处理性能
- [ ] 增加主题自定义功能
- [ ] 添加插件系统

### 长期规划
- [ ] 云同步功能
- [ ] 团队协作功能
- [ ] 移动端应用
- [ ] Web版本

## 📞 技术支持

### 项目维护者
- 主要开发：Claude (AI Assistant)
- 项目发起：joe

### 联系方式
- 问题反馈：通过GitHub Issues
- 功能建议：通过GitHub Discussions

---

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 修复拖拽素材精准插入功能
- ✅ 重新设计素材管理列表样式（基于2024最佳实践）
- ✅ 修复文件和文件夹创建功能（现代化输入模态框）
- ✅ 更换AI助手图标为更明显的AI图标
- ✅ 完善流式AI对话功能
- ✅ 实现Material Design 3现代化UI
- ✅ 添加完整的项目文档

### 技术债务
- 部分旧代码需要重构
- 测试覆盖率需要提升
- 性能优化空间较大

---

*本文档将随项目发展持续更新，建议在修改代码前先阅读相关章节以快速定位代码位置。*