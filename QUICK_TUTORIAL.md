# 🚀 QNotes 5分钟快速教程

## 第一步：启动应用
```bash
npm run dev
```

## 第二步：添加你的第一个收藏

### 方法 1：快速收藏（推荐⭐）
1. **复制任何有趣的内容** - 比如这段文字
2. **按 `Cmd + N`** （Windows: `Ctrl + N`）
3. **快速收藏窗口弹出**
4. **点击"粘贴"按钮**，内容自动填入
5. **点击"保存"** ✅

### 方法 2：智能提示收藏
1. **复制有价值的内容**（链接、长文本等）
2. **等待2秒**，应用会自动检测
3. **看到通知提示时**，点击通知或按 `Cmd + N`
4. **保存收藏** ✅

### 方法 3：托盘菜单
1. **右键点击托盘图标** 📚
2. **选择"快速收藏"**
3. **输入内容并保存** ✅

## 第三步：查看和管理收藏

### 切换到整理模式
```bash
按 Cmd + 2  # 或右键托盘 → 整理模式
```

现在你会看到：
- **左侧**：你的所有收藏列表
- **右侧**：Markdown 编辑器
- **工具栏**：各种功能按钮

## 第四步：体验 AI 智能分析

1. **选择几个收藏**（点击收藏项，可多选）
2. **点击 "AI 分析" 按钮** 🤖
3. **等待分析完成**（需要网络连接）
4. **查看智能分类结果**

AI 会自动：
- 📂 将内容分类
- 🏷️ 提取关键词  
- 📝 生成摘要
- 🔍 发现重复内容

## 第五步：生成智能文档

1. **AI 分析完成后**
2. **点击 "生成" 按钮** 📄
3. **在右侧编辑器查看生成的 Markdown 文档**
4. **可以编辑、完善文档内容**

## 第六步：保存和导出

### 保存文档
```bash
按 Cmd + S  # 保存到本地文件
```

### 导出多种格式
```bash
按 Cmd + E  # 选择导出格式
```
支持：Markdown、HTML、PDF、JSON

## 🎯 快速上手场景

### 场景 1：收集学习资料
```
1. 浏览网页时复制有用段落
2. Cmd + N 快速保存
3. 添加来源（可选）
4. 一天结束后切换到整理模式
5. AI 分析 → 自动分类为学习笔记
```

### 场景 2：工作内容整理
```
1. 会议中复制重要信息
2. 邮件中复制关键段落
3. 定期（每周）进行 AI 分析
4. 生成工作周报或项目文档
```

### 场景 3：灵感收集
```
1. 随时保存突然想到的点子
2. 收集相关的参考资料
3. AI 分析找出关联性
4. 整理成完整的创意文档
```

## 🔧 常用快捷键速查

| 功能 | 快捷键 | 说明 |
|------|--------|------|
| 快速收藏 | `Cmd + N` | 最常用！|
| 收集模式 | `Cmd + 1` | 轻量级 |
| 整理模式 | `Cmd + 2` | 完整功能 |
| AI 分析 | `Cmd + A` | 智能分类 |
| 搜索 | `Cmd + F` | 查找内容 |
| 保存文档 | `Cmd + S` | 保存编辑 |
| 导出 | `Cmd + E` | 多格式导出 |

## 💡 使用技巧

### 提高收藏效率
- **养成复制后立即 `Cmd + N` 的习惯**
- **善用类型标记**（文本/链接/图片）
- **添加简单的来源标记**

### 提高整理效率  
- **定期批量分析**（不要一个个处理）
- **利用搜索和筛选功能**
- **保存有价值的分析结果为文档**

### 团队协作
- **统一分类标准**
- **定期导出分享**
- **建立知识库文档**

## 🚨 注意事项

1. **AI 功能需要网络连接**
2. **首次使用可能需要系统权限**
3. **建议定期导出备份重要数据**
4. **复制内容后稍等2秒可触发智能提示**

---

## 🎉 开始使用吧！

**记住最重要的快捷键：`Cmd + N`**

这就是你从"看到内容"到"保存收藏"的最快路径！

现在就试试复制这段文字，然后按 `Cmd + N` 😉