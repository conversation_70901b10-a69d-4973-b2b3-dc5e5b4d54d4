#!/usr/bin/env node

// 专门用于调试素材UI加载问题的脚本
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const Database = require('./src/database/Database');

let mainWindow;
let database;

async function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false
    }
  });

  // 加载整理界面
  await mainWindow.loadFile('src/ui/organization.html');
  
  // 打开开发者工具
  mainWindow.webContents.openDevTools();
  
  // 注入调试代码
  setTimeout(async () => {
    await mainWindow.webContents.executeJavaScript(`
      console.log('=== 素材加载调试开始 ===');
      
      // 检查DOM元素
      const materialsList = document.getElementById('materialsList');
      console.log('materialsList元素:', materialsList);
      console.log('materialsList innerHTML:', materialsList ? materialsList.innerHTML : 'null');
      
      // 检查全局变量
      console.log('organizationApp:', typeof organizationApp);
      console.log('window.organizationApp:', typeof window.organizationApp);
      
      // 检查材料管理器
      if (window.organizationApp && window.organizationApp.materialsManager) {
        console.log('materialsManager存在');
        console.log('materials数组:', window.organizationApp.materialsManager.materials);
        console.log('materials长度:', window.organizationApp.materialsManager.materials.length);
      } else {
        console.log('materialsManager不存在');
      }
      
      // 尝试手动加载素材
      if (window.organizationApp && window.organizationApp.materialsManager) {
        console.log('尝试手动加载素材...');
        try {
          await window.organizationApp.materialsManager.loadMaterials();
          console.log('手动加载完成，materials:', window.organizationApp.materialsManager.materials.length);
        } catch (error) {
          console.error('手动加载失败:', error);
        }
      }
      
      console.log('=== 素材加载调试结束 ===');
    `);
  }, 3000);
}

async function initDatabase() {
  try {
    console.log('初始化数据库...');
    database = new Database();
    await database.init();
    console.log('数据库初始化完成');
    
    // 测试数据库查询
    const collections = await database.getCollections();
    console.log('数据库中的收藏数量:', collections.length);
    
    if (collections.length > 0) {
      console.log('前3个收藏示例:');
      collections.slice(0, 3).forEach((item, index) => {
        console.log(`${index + 1}:`, {
          id: item.id,
          type: item.type,
          content: item.content ? item.content.substring(0, 50) + '...' : 'null',
          category: item.category,
          created_at: item.created_at
        });
      });
    }
    
    return true;
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return false;
  }
}

function setupIPC() {
  // get-collections处理器
  ipcMain.handle('get-collections', async (event, options = {}) => {
    try {
      console.log('IPC get-collections调用，参数:', options);
      const result = await database.getCollections(options);
      console.log('IPC get-collections返回:', result.length, '个项目');
      
      // 验证数据格式
      if (!Array.isArray(result)) {
        console.error('警告：返回的不是数组:', typeof result);
        return [];
      }
      
      // 检查每个项目的必要字段
      const validItems = result.filter(item => {
        const isValid = item && item.id && item.content !== undefined;
        if (!isValid) {
          console.warn('发现无效项目:', item);
        }
        return isValid;
      });
      
      console.log('有效项目数量:', validItems.length);
      return validItems;
      
    } catch (error) {
      console.error('get-collections处理器错误:', error);
      throw error;
    }
  });
  
  // 其他必要的IPC处理器
  ipcMain.handle('update-collection', async (event, id, data) => {
    try {
      return await database.updateCollection(id, data);
    } catch (error) {
      console.error('update-collection错误:', error);
      throw error;
    }
  });
  
  ipcMain.handle('delete-collection', async (event, id) => {
    try {
      return await database.deleteCollection(id);
    } catch (error) {
      console.error('delete-collection错误:', error);
      throw error;
    }
  });
}

app.whenReady().then(async () => {
  console.log('Electron应用启动...');
  
  // 初始化数据库
  const dbInitialized = await initDatabase();
  if (!dbInitialized) {
    console.error('数据库初始化失败，退出应用');
    app.quit();
    return;
  }
  
  // 设置IPC处理器
  setupIPC();
  
  // 创建窗口
  await createWindow();
  
  console.log('调试应用启动完成');
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});