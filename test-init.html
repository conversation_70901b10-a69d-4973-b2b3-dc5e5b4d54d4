<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初始化测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .log { background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>初始化测试</h1>
    <div id="log" class="log">开始测试...\n</div>
    
    <!-- 模拟原始HTML结构 -->
    <div id="materialsList">加载中...</div>
    
    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : (type === 'success' ? 'success' : '');
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            console.log(message);
        }
        
        // 全局错误捕获
        window.addEventListener('error', function(e) {
            log(`ERROR: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            log(`UNHANDLED PROMISE REJECTION: ${e.reason}`, 'error');
        });
        
        async function testInitialization() {
            try {
                log('开始初始化测试');
                
                // 设置全局变量
                window.ipcRenderer = null; // 模拟浏览器环境
                
                log('加载脚本...');
                
                // 加载脚本
                const scripts = [
                    'src/ui/scripts/core.js',
                    'src/ui/scripts/files-manager.js',
                    'src/ui/scripts/materials-manager.js',
                    'src/ui/scripts/ui-manager.js',
                    'src/ui/scripts/app.js'
                ];
                
                for (const src of scripts) {
                    await loadScript(src);
                    log(`✅ ${src} 加载完成`);
                }
                
                log('所有脚本加载完成，开始初始化应用');
                
                // 检查类是否存在
                if (typeof OrganizationApp === 'undefined') {
                    throw new Error('OrganizationApp 类未定义');
                }
                
                log('创建 OrganizationApp 实例');
                const app = new OrganizationApp();
                
                log('调用 init 方法');
                await app.init();
                
                log('初始化完成！', 'success');
                
            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
            }
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = (e) => {
                    log(`脚本加载失败: ${src}`, 'error');
                    reject(e);
                };
                document.head.appendChild(script);
            });
        }
        
        // 开始测试
        testInitialization();
    </script>
</body>
</html>
