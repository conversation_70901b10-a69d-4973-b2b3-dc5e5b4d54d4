# QNotes - 智能内容收集与整理工具

QNotes是一个基于Electron的智能内容收集与整理工具，支持快速收集、智能分析和高效整理各类内容。

## 🚀 主要功能

### 双模式设计
- **收集模式**: 快速收集内容，支持拖拽、剪贴板监听等多种方式
- **整理模式**: 智能分析和整理收集的内容，提供AI辅助功能

### 🤖 AI智能助手
- **对话功能**: 支持与AI进行自然语言对话，获取写作建议和内容分析
- **文本处理**: 智能改进、总结、扩展、翻译等文本处理功能
- **流式输出**: 实时显示AI回复，提供流畅的交互体验
- **选中文本处理**: 选择文本后可快速进行AI处理操作
- **操作按钮**: 一键复制、插入、替换AI生成的内容

### 📝 内容管理
- **Markdown编辑**: 支持Markdown语法的富文本编辑
- **智能分类**: AI自动分析和分类收集的内容
- **标签管理**: 灵活的标签系统，便于内容组织
- **全文搜索**: 快速查找和定位内容

## 🛠️ 技术栈

- **框架**: Electron + Node.js
- **前端**: HTML5 + CSS3 + JavaScript
- **数据库**: SQLite
- **AI服务**: Claude API (支持流式输出)
- **编辑器**: 自定义Markdown编辑器

## 📋 开发规范

### Electron 开发最佳实践
项目遵循 [Electron 开发最佳实践规则](ELECTRON_DEVELOPMENT_BEST_PRACTICES.md)，包含：

- **安全最佳实践**: 上下文隔离、沙盒化、CSP策略等
- **架构设计**: 主进程与渲染进程分离、IPC通信规范
- **性能优化**: 内存管理、窗口优化、离屏渲染等
- **跨平台兼容**: 平台特定代码处理、路径管理等
- **测试调试**: 自动化测试、调试配置等

### 安全原则
- 启用上下文隔离和沙盒模式
- 禁用Node.js集成，通过预加载脚本安全暴露API
- 使用contextBridge进行安全的IPC通信
- 设置严格的内容安全策略(CSP)
- 验证所有IPC通信的发送者

## 📦 安装与运行

### 环境要求
- Node.js 16.0 或更高版本
- npm 或 yarn

### 安装步骤
```bash
# 克隆项目
git clone <repository-url>
cd qnotes

# 安装依赖
npm install

# 启动应用
npm start
```

### 开发模式
```bash
# 开发模式启动
npm run dev

# 构建应用
npm run build
```

## ⚙️ 配置

### AI服务配置
AI功能需要配置API Key。在`src/services/Settings.js`中：

```javascript
ai: {
  enabled: true,
  baseURL: 'https://api.tu-zi.com/v1',
  apiKey: 'your-api-key-here',
  model: 'claude-sonnet-4-20250514',
  temperature: 0.7,
  maxTokens: 4000
}
```

### 数据库配置
应用使用SQLite数据库，数据文件存储在`data/`目录下。

## 🎯 使用指南

### 基本操作
1. **启动应用**: 运行`npm start`
2. **选择模式**: 在主界面选择收集模式或整理模式
3. **AI对话**: 在右侧AI面板中与助手对话
4. **文本处理**: 选择文本后使用快捷操作按钮

### AI功能使用
- **发送消息**: 在AI面板底部输入框中输入消息
- **复制回复**: 点击AI回复下方的"复制"按钮
- **插入内容**: 点击"插入"按钮将AI回复插入到编辑器光标位置
- **替换文本**: 选择文本后点击"替换"按钮用AI回复替换选中内容
- **重新生成**: 点击"重新生成"按钮让AI重新回答

### 快捷操作
- **选中文本处理**: 选择文本后右侧会显示快捷操作按钮（改进、总结、扩展、翻译等）
- **键盘快捷键**: 在AI输入框中按Enter发送消息，Shift+Enter换行

## 🔧 故障排除

### AI功能不工作
1. 检查API Key是否正确配置
2. 确认网络连接正常
3. 查看控制台错误日志

### 操作按钮无响应
1. 确认AI服务已启用
2. 检查浏览器控制台是否有错误
3. 尝试刷新应用

## 📚 开发文档

### 项目结构
```
qnotes/
├── src/
│   ├── main.js                 # 主进程入口
│   ├── controllers/            # 控制器
│   ├── database/              # 数据库相关
│   ├── modes/                 # 模式管理
│   ├── services/              # 服务层
│   ├── ui/                    # 用户界面
│   │   ├── scripts/           # 前端脚本
│   │   ├── styles/            # 样式文件
│   │   └── *.html             # 页面文件
│   └── utils/                 # 工具函数
├── data/                      # 数据文件
├── config/                    # 配置文件
└── scripts/                   # 脚本文件
```

### 核心模块
- **AIManager**: AI功能管理，包括对话、文本处理、操作按钮等
- **EditorManager**: 编辑器管理，支持Markdown编辑和文本操作
- **MaterialsManager**: 材料管理，负责内容的收集和整理
- **UIManager**: 界面管理，控制UI状态和交互

### 最近更新

#### v1.2.0 - AI对话显示和操作功能修复
- 🔧 修复AI消息操作按钮点击失效问题
- 🔧 改进复制功能，支持现代和传统浏览器
- 🔧 增强插入和替换功能的编辑器查找逻辑
- 🔧 优化流式消息更新和按钮状态维护
- 🔧 完善错误处理和用户反馈机制
- ✨ 使用事件委托替代内联事件处理
- ✨ 添加调试工具和测试页面

#### v1.1.0 - AI聊天界面重设计
- ✨ 重新设计AI对话助手界面，实现极简交互设计
- ✨ 实现选中文本自动显示和快捷操作功能
- ✨ 实现AI回复的操作按钮（复制/插入/替换）
- ✨ 实现对话历史管理和编辑功能
- ✨ 优化流式输出和用户体验

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持与反馈

如有问题或建议，请：
1. 创建 Issue
2. 发送邮件至 <EMAIL>
3. 查看项目文档和FAQ

---

**QNotes** - 让内容收集和整理变得更加智能和高效！