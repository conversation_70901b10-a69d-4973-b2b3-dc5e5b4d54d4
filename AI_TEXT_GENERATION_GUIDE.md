# AI助手增强文本生成功能指南

## 🎯 功能概述

AI助手现在支持增强的文本生成功能，当您选中文本进行AI处理时，生成的内容会先在AI对话框中显示，然后提供多种操作选项供您选择。

## ✨ 核心特性

### 1. 智能文本处理流程
- **选中文本** → **AI处理** → **对话框显示** → **选择操作**
- 支持选中文本处理和整个文档处理
- 实时视觉反馈和加载动画

### 2. 多种操作选项
- **📋 复制**：将生成的内容复制到剪贴板
- **➕ 插入**：在当前光标位置插入生成的内容
- **🔄 替换**：替换选中的文本或整个文档
- **🔄 重新生成**：重新运行AI处理生成新内容

### 3. 精准插入功能
- 集成了精准拖拽插入系统
- 智能换行和格式化处理
- 支持光标位置精确定位

## 🚀 使用方法

### 基本操作流程

1. **选择文本**
   ```
   在Markdown编辑器中选中要处理的文本
   如果没有选中文本，将处理整个文档内容
   ```

2. **选择处理类型**
   - 点击"重写"按钮：重新表达内容
   - 点击"扩写"按钮：添加更多细节
   - 点击"总结"按钮：提取关键要点
   - 点击"改进"按钮：优化语言表达

3. **查看生成结果**
   ```
   AI助手面板会自动激活
   显示用户请求和AI生成的内容
   内容以特殊样式高亮显示
   ```

4. **选择操作**
   - **复制**：保存到剪贴板，可在其他地方使用
   - **插入**：在当前光标位置添加内容
   - **替换**：用生成的内容替换原文本
   - **重新生成**：如果不满意，重新处理

### 高级功能

#### 精准插入模式
```javascript
// 使用MarkdownEditor的精准插入功能
if (this.markdownEditor && this.markdownEditor.insertTextAtPosition) {
    this.markdownEditor.insertTextAtPosition(data.processedText, insertPosition);
}
```

#### 智能格式化
- 自动添加适当的换行符
- 避免内容粘连
- 保持文档结构清晰

## 🎨 界面设计

### AI对话框增强
```css
.ai-processed-text {
    background-color: #f8f9fa;
    border-left: 4px solid #3182ce;
    padding: 12px;
    border-radius: 6px;
    max-height: 300px;
    overflow-y: auto;
}
```

### 操作按钮样式
```css
.ai-message-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;
}

.ai-message-actions button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
}
```

### 加载动画
```css
.typing-indicator::before {
    content: "";
    width: 12px;
    height: 12px;
    border: 2px solid #3182ce;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
```

## 🔧 技术实现

### 核心方法

#### 1. processSelectedText()
```javascript
async processSelectedText(action) {
    // 获取选中文本或整个文档
    // 切换到AI面板
    // 显示用户请求
    // 调用AI处理
    // 显示结果和操作按钮
}
```

#### 2. 操作方法
```javascript
copyProcessedText()      // 复制到剪贴板
insertProcessedText()    // 插入到光标位置
replaceProcessedText()   // 替换选中文本
regenerateProcessedText() // 重新生成
```

### 数据结构
```javascript
window.currentTextProcessingData = {
    originalText: "原始文本",
    processedText: "处理后的文本",
    action: "rewrite|expand|summarize|improve",
    isFullDocument: true|false,
    selectionStart: 0,
    selectionEnd: 100
};
```

## 🧪 测试验证

### 自动化测试
```bash
# 运行测试脚本
./test-ai-generation.sh

# 打开测试页面
open test-ai-text-generation.html
```

### 手动测试步骤
1. 在编辑器中输入示例文本
2. 选中部分文本
3. 点击处理按钮
4. 验证AI对话框显示
5. 测试各种操作按钮
6. 检查插入和替换功能

## 📋 功能清单

### ✅ 已实现功能
- [x] 选中文本AI处理
- [x] AI对话框结果显示
- [x] 复制、插入、替换操作
- [x] 重新生成功能
- [x] 精准插入集成
- [x] 智能格式化
- [x] 视觉反馈和动画
- [x] 测试页面和脚本

### 🔄 处理类型支持
- [x] 重写（rewrite）
- [x] 扩写（expand）
- [x] 总结（summarize）
- [x] 改进（improve）
- [x] 翻译（translate）
- [x] 解释（explain）
- [x] 纠错（correct）
- [x] 大纲（outline）

## 🎉 使用示例

### 示例1：重写文本
```
原文：人工智能是一门综合性的前沿学科
处理：点击"重写"按钮
结果：在AI对话框显示重写版本
操作：选择"替换"应用到编辑器
```

### 示例2：扩写内容
```
原文：AI在各个行业中的应用越来越广泛
处理：点击"扩写"按钮
结果：在AI对话框显示详细扩写内容
操作：选择"插入"添加到文档
```

### 示例3：总结文档
```
原文：选中整个文档内容
处理：点击"总结"按钮
结果：在AI对话框显示要点总结
操作：选择"复制"保存到剪贴板
```

## 🔮 未来扩展

### 可能的增强功能
- 批量文本处理
- 自定义处理模板
- 历史记录管理
- 快捷键支持
- 更多AI模型选择

### 性能优化
- 缓存处理结果
- 异步处理优化
- 内存使用优化
- 响应速度提升

---

**注意**：此功能需要配置AI服务才能正常使用。请确保在设置中正确配置了AI API密钥和服务地址。 