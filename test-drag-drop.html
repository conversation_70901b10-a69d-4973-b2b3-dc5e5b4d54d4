<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽测试 - QNotes</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .sidebar {
            width: 300px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .editor-area {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .material-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: grab;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .material-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .material-item.dragging {
            opacity: 0.5;
            transform: scale(0.95);
            cursor: grabbing;
        }
        
        .material-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }
        
        .material-content {
            color: #666;
            font-size: 14px;
        }
        
        .material-type {
            display: inline-block;
            background: #007acc;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-bottom: 8px;
        }
        
        #editor {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
        }
        
        .markdown-insertion-indicator {
            position: absolute;
            width: 2px;
            height: 20px;
            background-color: #007acc;
            z-index: 1000;
            display: none;
            pointer-events: none;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .preview {
            margin-top: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        
        .preview h1, .preview h2, .preview h3 {
            margin-top: 0;
        }
        
        .preview code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .preview pre {
            background: #f1f3f4;
            padding: 12px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h3>素材库</h3>
            <div class="material-item" draggable="true" data-type="text" data-content="这是一段测试文本，可以拖拽到编辑器中。">
                <div class="material-type">文本</div>
                <div class="material-title">测试文本</div>
                <div class="material-content">这是一段测试文本，可以拖拽到编辑器中。</div>
            </div>
            
            <div class="material-item" draggable="true" data-type="link" data-content="https://github.com" data-title="GitHub">
                <div class="material-type">链接</div>
                <div class="material-title">GitHub</div>
                <div class="material-content">https://github.com</div>
            </div>
            
            <div class="material-item" draggable="true" data-type="code" data-content="function hello() {\n    console.log('Hello, World!');\n}">
                <div class="material-type">代码</div>
                <div class="material-title">Hello函数</div>
                <div class="material-content">function hello() {...}</div>
            </div>
            
            <div class="material-item" draggable="true" data-type="image" data-content="https://via.placeholder.com/300x200" data-title="示例图片">
                <div class="material-type">图片</div>
                <div class="material-title">示例图片</div>
                <div class="material-content">https://via.placeholder.com/300x200</div>
            </div>
        </div>
        
        <div class="editor-area">
            <h3>Markdown编辑器</h3>
            <textarea id="editor" placeholder="开始编写文档，或者从左侧拖拽素材到这里..."># 欢迎使用拖拽功能

这是一个支持拖拽插入的Markdown编辑器。

## 使用方法

1. 从左侧素材库选择内容
2. 拖拽到编辑器的任意位置
3. 松开鼠标完成插入

试试看吧！</textarea>
            
            <div class="preview" id="preview">
                <h1>欢迎使用拖拽功能</h1>
                <p>这是一个支持拖拽插入的Markdown编辑器。</p>
                <h2>使用方法</h2>
                <ol>
                    <li>从左侧素材库选择内容</li>
                    <li>拖拽到编辑器的任意位置</li>
                    <li>松开鼠标完成插入</li>
                </ol>
                <p>试试看吧！</p>
            </div>
        </div>
    </div>

    <script src="src/ui/scripts/markdown-editor.js"></script>
    <script>
        // 初始化编辑器
        const editor = document.getElementById('editor');
        const preview = document.getElementById('preview');
        const markdownEditor = new MarkdownEditor(editor, preview);
        
        // 设置素材拖拽
        document.querySelectorAll('.material-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                const type = item.dataset.type;
                const content = item.dataset.content;
                const title = item.dataset.title || item.querySelector('.material-title').textContent;
                
                // 设置拖拽数据
                e.dataTransfer.setData('text/plain', content);
                e.dataTransfer.setData('application/x-material', JSON.stringify({
                    type: type,
                    content: content,
                    title: title
                }));
                
                e.dataTransfer.effectAllowed = 'copy';
                item.classList.add('dragging');
            });
            
            item.addEventListener('dragend', () => {
                item.classList.remove('dragging');
            });
        });
        
        // 更新预览
        function updatePreview() {
            const content = editor.value;
            const html = markdownEditor.markdownToHtml(content);
            preview.innerHTML = html;
        }
        
        // 监听编辑器变化
        editor.addEventListener('contentChanged', updatePreview);
        
        console.log('拖拽测试页面已加载');
    </script>
</body>
</html> 