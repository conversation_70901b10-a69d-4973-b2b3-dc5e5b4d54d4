/**
 * AI流式输出演示脚本
 * 展示核心功能的实现原理
 */

class AIStreamingDemo {
    constructor() {
        this.isStreaming = false;
        this.currentContent = '';
        this.streamInterval = null;
        this.callbacks = {};
    }

    // 注册事件回调
    on(event, callback) {
        if (!this.callbacks[event]) {
            this.callbacks[event] = [];
        }
        this.callbacks[event].push(callback);
    }

    // 触发事件
    emit(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => callback(data));
        }
    }

    // 模拟AI流式输出
    async simulateStreaming(text, options = {}) {
        const {
            chunkSize = 2,
            delay = 100,
            onStart = null,
            onChunk = null,
            onComplete = null,
            onError = null
        } = options;

        if (this.isStreaming) {
            throw new Error('已有流式输出正在进行中');
        }

        this.isStreaming = true;
        this.currentContent = '';
        
        try {
            // 触发开始事件
            this.emit('stream-start', { text, options });
            if (onStart) onStart();

            // 分块处理文本
            let index = 0;
            
            while (index < text.length) {
                // 计算当前块的大小
                const currentChunkSize = Math.min(
                    Math.floor(Math.random() * chunkSize) + 1,
                    text.length - index
                );
                
                // 提取当前块
                const chunk = text.substr(index, currentChunkSize);
                this.currentContent += chunk;
                index += currentChunkSize;

                // 触发块事件
                this.emit('stream-chunk', { 
                    chunk, 
                    content: this.currentContent, 
                    progress: index / text.length 
                });
                if (onChunk) onChunk(chunk, this.currentContent);

                // 等待指定延迟
                await new Promise(resolve => setTimeout(resolve, delay));
            }

            // 触发完成事件
            this.emit('stream-complete', { 
                content: this.currentContent, 
                totalChunks: Math.ceil(text.length / chunkSize) 
            });
            if (onComplete) onComplete(this.currentContent);

        } catch (error) {
            // 触发错误事件
            this.emit('stream-error', { error: error.message });
            if (onError) onError(error);
        } finally {
            this.isStreaming = false;
        }
    }

    // 停止流式输出
    stop() {
        if (this.streamInterval) {
            clearInterval(this.streamInterval);
            this.streamInterval = null;
        }
        this.isStreaming = false;
        this.emit('stream-stop', { content: this.currentContent });
    }

    // 获取当前状态
    getStatus() {
        return {
            isStreaming: this.isStreaming,
            currentContent: this.currentContent,
            contentLength: this.currentContent.length
        };
    }
}

// 使用示例
function demoBasicUsage() {
    console.log('🚀 基本使用演示');
    console.log('================');

    const demo = new AIStreamingDemo();
    const testText = "这是一个AI流式输出的演示。通过逐字符的方式展示文本生成过程，让用户能够实时看到AI的思考和输出过程。这种方式提供了更好的用户体验，让AI的回复显得更加自然和生动。";

    // 注册事件监听器
    demo.on('stream-start', (data) => {
        console.log('📝 开始流式输出...');
        console.log(`📊 文本长度: ${data.text.length} 字符`);
    });

    demo.on('stream-chunk', (data) => {
        process.stdout.write(data.chunk);
    });

    demo.on('stream-complete', (data) => {
        console.log('\n\n✅ 流式输出完成');
        console.log(`📊 总块数: ${data.totalChunks}`);
        console.log(`📊 最终长度: ${data.content.length} 字符`);
    });

    demo.on('stream-error', (data) => {
        console.log(`\n❌ 流式输出错误: ${data.error}`);
    });

    // 开始演示
    demo.simulateStreaming(testText, {
        chunkSize: 3,
        delay: 50
    });
}

// 高级使用演示
function demoAdvancedUsage() {
    console.log('\n🎯 高级使用演示');
    console.log('================');

    const demo = new AIStreamingDemo();
    const responses = [
        "经过AI分析，这段文本可以进行以下优化：",
        "1. 语法结构更加清晰和流畅",
        "2. 词汇选择更加准确和丰富", 
        "3. 逻辑表达更加连贯和有力",
        "4. 整体风格更加专业和易读"
    ];

    let currentIndex = 0;

    function processNext() {
        if (currentIndex >= responses.length) {
            console.log('\n🎉 所有处理完成！');
            return;
        }

        const currentText = responses[currentIndex];
        console.log(`\n📝 处理第 ${currentIndex + 1} 部分:`);
        
        demo.simulateStreaming(currentText, {
            chunkSize: 2,
            delay: 80,
            onStart: () => {
                console.log('⏳ 开始处理...');
            },
            onChunk: (chunk, content) => {
                process.stdout.write(chunk);
            },
            onComplete: (content) => {
                console.log('\n✅ 处理完成');
                currentIndex++;
                setTimeout(processNext, 500);
            },
            onError: (error) => {
                console.log(`\n❌ 处理失败: ${error.message}`);
            }
        });
    }

    processNext();
}

// 性能测试演示
function demoPerformanceTest() {
    console.log('\n⚡ 性能测试演示');
    console.log('================');

    const demo = new AIStreamingDemo();
    const longText = "这是一个很长的文本，用于测试AI流式输出的性能。".repeat(100);
    
    const startTime = Date.now();
    let chunkCount = 0;

    demo.on('stream-start', () => {
        console.log('🚀 开始性能测试...');
        console.log(`📊 文本长度: ${longText.length} 字符`);
    });

    demo.on('stream-chunk', (data) => {
        chunkCount++;
        // 不输出内容，只统计
    });

    demo.on('stream-complete', (data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log('📊 性能测试结果:');
        console.log(`   ⏱️  总用时: ${duration}ms`);
        console.log(`   📦 总块数: ${chunkCount}`);
        console.log(`   📏 文本长度: ${data.content.length} 字符`);
        console.log(`   ⚡ 平均速度: ${Math.round(data.content.length / duration * 1000)} 字符/秒`);
        console.log(`   📈 块处理速度: ${Math.round(chunkCount / duration * 1000)} 块/秒`);
    });

    demo.simulateStreaming(longText, {
        chunkSize: 5,
        delay: 10
    });
}

// 错误处理演示
function demoErrorHandling() {
    console.log('\n🚨 错误处理演示');
    console.log('================');

    const demo = new AIStreamingDemo();
    
    // 模拟错误情况
    demo.simulateStreaming("测试文本", {
        chunkSize: 2,
        delay: 100,
        onStart: () => {
            console.log('📝 开始处理...');
            // 模拟在处理过程中发生错误
            setTimeout(() => {
                throw new Error('模拟网络连接中断');
            }, 300);
        },
        onChunk: (chunk) => {
            process.stdout.write(chunk);
        },
        onComplete: (content) => {
            console.log('\n✅ 处理完成');
        },
        onError: (error) => {
            console.log(`\n❌ 处理失败: ${error.message}`);
            console.log('🔄 可以尝试重新处理...');
        }
    });
}

// 主演示函数
async function runDemo() {
    console.log('🎭 AI流式输出演示程序');
    console.log('====================');
    console.log('本演示展示了AI流式输出的核心实现原理');
    console.log('');

    // 基本使用
    demoBasicUsage();

    // 等待基本演示完成
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 高级使用
    demoAdvancedUsage();

    // 等待高级演示完成
    await new Promise(resolve => setTimeout(resolve, 8000));

    // 性能测试
    demoPerformanceTest();

    // 等待性能测试完成
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 错误处理
    demoErrorHandling();
}

// 如果直接运行此脚本
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIStreamingDemo;
} else if (typeof window === 'undefined') {
    // Node.js环境
    runDemo().catch(console.error);
}

// 导出供其他模块使用
if (typeof module !== 'undefined') {
    module.exports = { AIStreamingDemo, runDemo };
} 