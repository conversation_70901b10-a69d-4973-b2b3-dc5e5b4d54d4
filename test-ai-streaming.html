<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI流式输出测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 800px;
            height: 600px;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .demo-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .controls {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            background: #f8fafc;
        }

        .control-group {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-bottom: 12px;
        }

        .control-group label {
            font-weight: 500;
            color: #374151;
            min-width: 80px;
        }

        .control-group input, .control-group select {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .control-group button {
            padding: 8px 16px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .control-group button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .control-group button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .ai-chat-history {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #ffffff;
        }

        /* 引入AI样式 */
        .ai-message {
            margin-bottom: 20px;
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .ai-message.user {
            margin-left: 20px;
        }

        .ai-message.assistant {
            margin-right: 20px;
        }

        .ai-message-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .ai-message.user .ai-message-content {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .ai-message.assistant .ai-message-content {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .ai-message-header {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: rgba(255, 255, 255, 0.5);
        }

        .ai-message.user .ai-message-header {
            background: rgba(255, 255, 255, 0.1);
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        .ai-avatar, .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            flex-shrink: 0;
        }

        .user-avatar {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .ai-status {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }

        .ai-status-text {
            font-size: 14px;
            font-weight: 500;
        }

        .ai-typing-dots {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .ai-typing-dots span {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #667eea;
            animation: typing 1.4s infinite ease-in-out;
        }

        .ai-typing-dots span:nth-child(1) {
            animation-delay: -0.32s;
        }

        .ai-typing-dots span:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .ai-processed-text {
            padding: 16px;
            font-size: 14px;
            line-height: 1.6;
            color: #1e293b;
            background: white;
            border-radius: 8px;
            margin: 16px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .ai-message.streaming .ai-processed-text {
            position: relative;
        }

        .ai-message.streaming .ai-processed-text::after {
            content: '';
            display: inline-block;
            width: 2px;
            height: 18px;
            background-color: #667eea;
            animation: blink 1s infinite;
            margin-left: 2px;
            vertical-align: text-bottom;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .ai-message-actions {
            display: flex;
            gap: 8px;
            padding: 16px;
            background: rgba(248, 250, 252, 0.8);
            border-top: 1px solid #e2e8f0;
            flex-wrap: wrap;
        }

        .ai-action-btn {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
            color: #64748b;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .ai-action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .ai-action-copy:hover {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .ai-action-insert:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .ai-action-replace:hover {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .ai-action-regenerate:hover {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
        }

        .user-request {
            padding: 16px;
        }

        .user-request p {
            margin: 0 0 12px 0;
            font-size: 14px;
        }

        .user-content-preview {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            font-size: 13px;
            line-height: 1.5;
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }

        .stats {
            display: flex;
            gap: 20px;
            margin-top: 12px;
            font-size: 12px;
            color: #6b7280;
        }

        .stat {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .welcome-message h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #1e293b;
        }

        .welcome-message p {
            font-size: 14px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI流式输出测试</h1>
            <p>体验全新的AI对话流式输出功能</p>
        </div>

        <div class="demo-area">
            <div class="controls">
                <div class="control-group">
                    <label>输入文本:</label>
                    <input type="text" id="inputText" placeholder="输入要处理的文本..." value="这是一段测试文本，用于演示AI流式输出功能。">
                </div>
                <div class="control-group">
                    <label>处理类型:</label>
                    <select id="actionType">
                        <option value="improve">改进文本</option>
                        <option value="rewrite">重写文本</option>
                        <option value="expand">扩展内容</option>
                        <option value="summarize">总结要点</option>
                        <option value="translate">翻译文本</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>流式速度:</label>
                    <select id="streamSpeed">
                        <option value="slow">慢速 (200ms)</option>
                        <option value="normal" selected>正常 (100ms)</option>
                        <option value="fast">快速 (50ms)</option>
                        <option value="instant">瞬时 (10ms)</option>
                    </select>
                    <button id="startDemo" onclick="startDemo()">开始演示</button>
                    <button id="clearHistory" onclick="clearHistory()">清空历史</button>
                </div>
                <div class="stats">
                    <div class="stat">
                        <span>📊 总字符数:</span>
                        <span id="totalChars">0</span>
                    </div>
                    <div class="stat">
                        <span>⏱️ 处理时间:</span>
                        <span id="processTime">0ms</span>
                    </div>
                    <div class="stat">
                        <span>🔄 流式块数:</span>
                        <span id="streamChunks">0</span>
                    </div>
                </div>
            </div>

            <div class="ai-chat-history" id="chatHistory">
                <div class="welcome-message">
                    <h3>🎉 欢迎使用AI流式输出演示</h3>
                    <p>点击"开始演示"按钮体验实时流式AI文本生成效果。<br>
                    您可以调整流式速度来观察不同的输出效果。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentMessage = null;
        let streamingInterval = null;
        let startTime = 0;
        let chunkCount = 0;

        // 模拟AI响应文本
        const mockResponses = {
            improve: "经过AI优化处理，这段文本已经得到了显著改善。我们使用了更加精准的词汇选择，优化了句式结构，并增强了表达的清晰度和流畅性。同时，我们还注意到了语言的连贯性和逻辑性，确保整体表达更加专业和易于理解。",
            rewrite: "这是一个全新改写的版本：通过AI技术的深度重构，原有内容已经焕然一新。我们保持了核心思想的完整性，同时采用了更加现代化的表达方式，使得整个文本更具吸引力和可读性。新的版本在保持原意的基础上，展现出了更强的表现力。",
            expand: "基于原始文本，我们可以进行深度扩展：这段内容不仅仅是简单的文字组合，它承载着丰富的信息和深层的含义。通过AI分析，我们发现其中蕴含着多个维度的价值。首先，从技术角度来看，它展示了现代AI处理的先进性。其次，从用户体验的角度，它提供了直观且高效的交互方式。最后，从未来发展的角度，这种技术将会带来更多创新的可能性。",
            summarize: "核心要点总结：✅ 这是一个AI流式输出的演示系统；✅ 主要功能包括文本处理和实时生成；✅ 支持多种处理模式和速度调节；✅ 提供了良好的用户交互体验；✅ 展示了现代AI技术的实际应用价值。",
            translate: "English Translation: This is a test text used to demonstrate the AI streaming output functionality. Through advanced natural language processing technology, we can provide real-time, high-quality text generation and processing services. The system supports multiple languages and various text processing modes, offering users a smooth and efficient AI interaction experience."
        };

        function getActionName(action) {
            const names = {
                improve: '改进',
                rewrite: '重写',
                expand: '扩展',
                summarize: '总结',
                translate: '翻译'
            };
            return names[action] || '处理';
        }

        function getStreamDelay(speed) {
            const delays = {
                slow: 200,
                normal: 100,
                fast: 50,
                instant: 10
            };
            return delays[speed] || 100;
        }

        function addUserMessage(text, action) {
            const chatHistory = document.getElementById('chatHistory');
            const actionName = getActionName(action);
            
            const userMessage = document.createElement('div');
            userMessage.className = 'ai-message user';
            userMessage.innerHTML = `
                <div class="ai-message-content">
                    <div class="ai-message-header">
                        <div class="user-avatar">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>
                        <div class="ai-status">
                            <span class="ai-status-text">您</span>
                        </div>
                    </div>
                    <div class="user-request">
                        <p><strong>请${actionName}以下内容：</strong></p>
                        <div class="user-content-preview">${text}</div>
                    </div>
                </div>
            `;
            
            chatHistory.appendChild(userMessage);
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }

        function addAIMessage() {
            const chatHistory = document.getElementById('chatHistory');
            
            const aiMessage = document.createElement('div');
            aiMessage.className = 'ai-message assistant streaming';
            aiMessage.innerHTML = `
                <div class="ai-message-content">
                    <div class="ai-message-header">
                        <div class="ai-avatar">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.12.23-2.19.64-3.17L6.5 10.5C6.19 11.29 6 12.13 6 13c0 3.31 2.69 6 6 6s6-2.69 6-6-2.69-6-6-6c-.87 0-1.71.19-2.5.5L7.83 5.83C9.1 4.67 10.5 4 12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/>
                                <circle cx="9" cy="9" r="2"/>
                                <circle cx="15" cy="15" r="2"/>
                                <path d="M9 15l6-6"/>
                            </svg>
                        </div>
                        <div class="ai-status">
                            <span class="ai-status-text">AI正在思考中</span>
                            <div class="ai-typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                    <div class="ai-stream-content"></div>
                </div>
            `;
            
            chatHistory.appendChild(aiMessage);
            chatHistory.scrollTop = chatHistory.scrollHeight;
            
            return aiMessage;
        }

        function formatStreamContent(content) {
            // 转义HTML字符
            const escaped = content
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;');
            
            // 简单的markdown格式化
            return escaped
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/`(.*?)`/g, '<code>$1</code>')
                .replace(/\n\n/g, '</p><p>')
                .replace(/\n/g, '<br>')
                .replace(/^(.*)$/, '<p>$1</p>');
        }

        function streamText(message, text, delay) {
            let currentContent = '';
            let index = 0;
            chunkCount = 0;
            
            // 更新状态为正在输出
            const statusText = message.querySelector('.ai-status-text');
            statusText.textContent = 'AI正在输出中...';
            
            streamingInterval = setInterval(() => {
                if (index < text.length) {
                    // 每次添加1-3个字符，模拟真实的流式输出
                    const chunkSize = Math.min(Math.floor(Math.random() * 3) + 1, text.length - index);
                    currentContent += text.substr(index, chunkSize);
                    index += chunkSize;
                    chunkCount++;
                    
                    // 更新内容
                    const streamContent = message.querySelector('.ai-stream-content');
                    streamContent.innerHTML = `<div class="ai-processed-text">${formatStreamContent(currentContent)}</div>`;
                    
                    // 更新统计信息
                    document.getElementById('totalChars').textContent = currentContent.length;
                    document.getElementById('streamChunks').textContent = chunkCount;
                    
                    // 滚动到底部
                    const chatHistory = document.getElementById('chatHistory');
                    chatHistory.scrollTop = chatHistory.scrollHeight;
                } else {
                    // 流式输出完成
                    clearInterval(streamingInterval);
                    finishStreaming(message, currentContent);
                }
            }, delay);
        }

        function finishStreaming(message, finalContent) {
            message.classList.remove('streaming');
            
            // 更新状态
            const statusText = message.querySelector('.ai-status-text');
            statusText.textContent = '生成完成';
            
            // 移除打字动画
            const typingDots = message.querySelector('.ai-typing-dots');
            if (typingDots) {
                typingDots.remove();
            }
            
            // 添加成功指示器
            const statusDiv = message.querySelector('.ai-status');
            statusDiv.innerHTML += '<div class="ai-status-indicator success" style="width: 8px; height: 8px; border-radius: 50%; background-color: #10b981;"></div>';
            
            // 更新最终内容
            message.innerHTML = `
                <div class="ai-message-content">
                    <div class="ai-message-header">
                        <div class="ai-avatar">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.12.23-2.19.64-3.17L6.5 10.5C6.19 11.29 6 12.13 6 13c0 3.31 2.69 6 6 6s6-2.69 6-6-2.69-6-6-6c-.87 0-1.71.19-2.5.5L7.83 5.83C9.1 4.67 10.5 4 12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8z"/>
                                <circle cx="9" cy="9" r="2"/>
                                <circle cx="15" cy="15" r="2"/>
                                <path d="M9 15l6-6"/>
                            </svg>
                        </div>
                        <div class="ai-status">
                            <span class="ai-status-text">生成完成</span>
                            <div class="ai-status-indicator success" style="width: 8px; height: 8px; border-radius: 50%; background-color: #10b981;"></div>
                        </div>
                    </div>
                    <div class="ai-processed-text">${formatStreamContent(finalContent)}</div>
                </div>
            `;
            
            // 添加操作按钮
            const actionButtons = document.createElement('div');
            actionButtons.className = 'ai-message-actions';
            actionButtons.innerHTML = `
                <button class="ai-action-btn ai-action-copy" onclick="copyText('${finalContent.replace(/'/g, '\\\'')}')" title="复制生成的内容">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                    <span>复制</span>
                </button>
                <button class="ai-action-btn ai-action-insert" onclick="insertText('${finalContent.replace(/'/g, '\\\'')}')" title="插入到编辑器">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    <span>插入</span>
                </button>
                <button class="ai-action-btn ai-action-replace" onclick="replaceText('${finalContent.replace(/'/g, '\\\'')}')" title="替换原文">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                    <span>替换</span>
                </button>
                <button class="ai-action-btn ai-action-regenerate" onclick="regenerateText()" title="重新生成">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                    </svg>
                    <span>重新生成</span>
                </button>
            `;
            
            message.querySelector('.ai-message-content').appendChild(actionButtons);
            
            // 更新处理时间
            const endTime = Date.now();
            document.getElementById('processTime').textContent = `${endTime - startTime}ms`;
            
            // 重置状态
            currentMessage = null;
            
            // 滚动到底部
            const chatHistory = document.getElementById('chatHistory');
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }

        function startDemo() {
            const inputText = document.getElementById('inputText').value.trim();
            const actionType = document.getElementById('actionType').value;
            const streamSpeed = document.getElementById('streamSpeed').value;
            
            if (!inputText) {
                alert('请输入要处理的文本');
                return;
            }
            
            // 禁用按钮
            const startBtn = document.getElementById('startDemo');
            startBtn.disabled = true;
            startBtn.textContent = '处理中...';
            
            // 记录开始时间
            startTime = Date.now();
            
            // 重置统计信息
            document.getElementById('totalChars').textContent = '0';
            document.getElementById('processTime').textContent = '0ms';
            document.getElementById('streamChunks').textContent = '0';
            
            // 添加用户消息
            addUserMessage(inputText, actionType);
            
            // 添加AI消息并开始流式输出
            setTimeout(() => {
                currentMessage = addAIMessage();
                const responseText = mockResponses[actionType] || mockResponses.improve;
                const delay = getStreamDelay(streamSpeed);
                
                setTimeout(() => {
                    streamText(currentMessage, responseText, delay);
                }, 500); // 模拟思考时间
            }, 300);
            
            // 重新启用按钮
            setTimeout(() => {
                startBtn.disabled = false;
                startBtn.textContent = '开始演示';
            }, 1000);
        }

        function clearHistory() {
            const chatHistory = document.getElementById('chatHistory');
            chatHistory.innerHTML = `
                <div class="welcome-message">
                    <h3>🎉 欢迎使用AI流式输出演示</h3>
                    <p>点击"开始演示"按钮体验实时流式AI文本生成效果。<br>
                    您可以调整流式速度来观察不同的输出效果。</p>
                </div>
            `;
            
            // 重置统计信息
            document.getElementById('totalChars').textContent = '0';
            document.getElementById('processTime').textContent = '0ms';
            document.getElementById('streamChunks').textContent = '0';
        }

        // 操作按钮功能
        function copyText(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('✅ 文本已复制到剪贴板');
            }).catch(() => {
                alert('❌ 复制失败');
            });
        }

        function insertText(text) {
            const inputText = document.getElementById('inputText');
            const currentValue = inputText.value;
            const cursorPos = inputText.selectionStart || currentValue.length;
            
            const newValue = currentValue.slice(0, cursorPos) + '\n\n' + text + '\n\n' + currentValue.slice(cursorPos);
            inputText.value = newValue;
            
            alert('✅ 文本已插入到输入框');
        }

        function replaceText(text) {
            document.getElementById('inputText').value = text;
            alert('✅ 已替换输入框内容');
        }

        function regenerateText() {
            startDemo();
        }

        // 回车键快速开始
        document.getElementById('inputText').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                startDemo();
            }
        });

        // 初始化时清空历史
        document.addEventListener('DOMContentLoaded', function() {
            clearHistory();
        });
    </script>
</body>
</html> 