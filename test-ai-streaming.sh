#!/bin/bash

# AI流式输出功能测试脚本
# 用于验证新的AI流式输出功能是否正常工作

echo "🚀 AI流式输出功能测试脚本"
echo "================================"

# 检查必要文件是否存在
echo "📋 检查必要文件..."

files=(
    "src/ui/scripts/organization.js"
    "src/ui/styles/organization.css"
    "test-ai-streaming.html"
    "AI_STREAMING_GUIDE.md"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file - 存在"
    else
        echo "❌ $file - 缺失"
        exit 1
    fi
done

echo ""
echo "🔍 检查JavaScript代码..."

# 检查关键函数是否存在
js_functions=(
    "bindAIEvents"
    "startAIStream"
    "handleAIStreamChunk"
    "formatStreamContent"
    "finishAIStream"
    "handleAIStreamError"
    "processSelectedText"
)

for func in "${js_functions[@]}"; do
    if grep -q "$func" src/ui/scripts/organization.js; then
        echo "✅ 函数 $func - 已实现"
    else
        echo "❌ 函数 $func - 缺失"
        exit 1
    fi
done

echo ""
echo "🎨 检查CSS样式..."

# 检查关键样式是否存在
css_classes=(
    "ai-message"
    "ai-message-content"
    "ai-message-header"
    "ai-avatar"
    "ai-typing-dots"
    "ai-processed-text"
    "ai-message-actions"
    "ai-action-btn"
    "ai-welcome"
    "ai-status-indicator"
)

for class in "${css_classes[@]}"; do
    if grep -q "\.$class" src/ui/styles/organization.css; then
        echo "✅ 样式 .$class - 已定义"
    else
        echo "❌ 样式 .$class - 缺失"
        exit 1
    fi
done

echo ""
echo "🧪 检查测试页面..."

# 检查测试页面关键元素
test_elements=(
    "chatHistory"
    "inputText"
    "actionType"
    "streamSpeed"
    "startDemo"
    "clearHistory"
)

for element in "${test_elements[@]}"; do
    if grep -q "id=\"$element\"" test-ai-streaming.html; then
        echo "✅ 元素 #$element - 已定义"
    else
        echo "❌ 元素 #$element - 缺失"
        exit 1
    fi
done

echo ""
echo "📊 代码统计..."

# 统计代码行数
js_lines=$(wc -l < src/ui/scripts/organization.js)
css_lines=$(wc -l < src/ui/styles/organization.css)
html_lines=$(wc -l < test-ai-streaming.html)
guide_lines=$(wc -l < AI_STREAMING_GUIDE.md)

echo "📄 JavaScript代码: $js_lines 行"
echo "🎨 CSS样式代码: $css_lines 行"
echo "🌐 HTML测试页面: $html_lines 行"
echo "📚 使用指南: $guide_lines 行"

total_lines=$((js_lines + css_lines + html_lines + guide_lines))
echo "📊 总计: $total_lines 行代码"

echo ""
echo "🔧 检查关键特性..."

# 检查流式输出相关特性
features=(
    "ai-stream-chunk"
    "ai-stream-start"
    "ai-stream-end"
    "ai-stream-error"
    "streaming"
    "fadeInUp"
    "typing"
    "blink"
)

for feature in "${features[@]}"; do
    if grep -q "$feature" src/ui/scripts/organization.js src/ui/styles/organization.css; then
        echo "✅ 特性 $feature - 已实现"
    else
        echo "❌ 特性 $feature - 缺失"
        exit 1
    fi
done

echo ""
echo "🌐 启动测试服务器..."

# 检查是否有Python或Node.js可用于启动本地服务器
if command -v python3 &> /dev/null; then
    echo "🐍 使用Python3启动测试服务器..."
    echo "📍 访问地址: http://localhost:8000/test-ai-streaming.html"
    echo "⚠️  请在浏览器中打开上述地址进行测试"
    echo "🛑 按Ctrl+C停止服务器"
    echo ""
    python3 -m http.server 8000
elif command -v python &> /dev/null; then
    echo "🐍 使用Python启动测试服务器..."
    echo "📍 访问地址: http://localhost:8000/test-ai-streaming.html"
    echo "⚠️  请在浏览器中打开上述地址进行测试"
    echo "🛑 按Ctrl+C停止服务器"
    echo ""
    python -m SimpleHTTPServer 8000
elif command -v node &> /dev/null; then
    echo "🟢 使用Node.js启动测试服务器..."
    if command -v npx &> /dev/null; then
        echo "📍 访问地址: http://localhost:8000/test-ai-streaming.html"
        echo "⚠️  请在浏览器中打开上述地址进行测试"
        echo "🛑 按Ctrl+C停止服务器"
        echo ""
        npx http-server -p 8000
    else
        echo "❌ 需要安装http-server: npm install -g http-server"
        exit 1
    fi
else
    echo "❌ 未找到Python或Node.js，无法启动测试服务器"
    echo "💡 请手动在浏览器中打开 test-ai-streaming.html 文件"
    echo "   或者安装Python/Node.js后重新运行此脚本"
fi

echo ""
echo "✅ 所有检查通过！"
echo "🎉 AI流式输出功能已准备就绪"
echo ""
echo "📝 测试清单："
echo "  1. 打开测试页面"
echo "  2. 输入测试文本"
echo "  3. 选择处理类型"
echo "  4. 调整流式速度"
echo "  5. 点击开始演示"
echo "  6. 观察流式输出效果"
echo "  7. 测试操作按钮功能"
echo "  8. 验证响应式设计"
echo ""
echo "🚀 开始测试吧！" 