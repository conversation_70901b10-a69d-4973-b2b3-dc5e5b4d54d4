<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文本生成功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #3182ce;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            display: flex;
            height: 600px;
        }
        
        .editor-section {
            flex: 1;
            padding: 20px;
            border-right: 1px solid #e0e6ed;
        }
        
        .ai-section {
            flex: 1;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .editor-container {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .editor-header {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .text-processing-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .text-processing-buttons button {
            padding: 6px 12px;
            background: #e2e8f0;
            border: 1px solid #cbd5e0;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .text-processing-buttons button:hover {
            background: #cbd5e0;
        }
        
        #testEditor {
            flex: 1;
            padding: 15px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.6;
            resize: none;
            outline: none;
        }
        
        .ai-chat-history {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e0e6ed;
            border-radius: 6px;
            padding: 15px;
            background: white;
            margin-bottom: 10px;
        }
        
        .ai-message {
            margin-bottom: 16px;
            padding: 12px;
            border-radius: 8px;
            max-width: 85%;
        }
        
        .ai-message.user {
            background-color: #3182ce;
            color: white;
            margin-left: auto;
        }
        
        .ai-message.assistant {
            background-color: #e2e8f0;
            color: #2d3748;
        }
        
        .ai-message-content {
            line-height: 1.5;
        }
        
        .ai-message-preview {
            background-color: rgba(255, 255, 255, 0.2);
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
            font-size: 13px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-word;
            max-height: 100px;
            overflow-y: auto;
        }
        
        .ai-message.user .ai-message-preview {
            background-color: rgba(255, 255, 255, 0.15);
            border-left: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .ai-processed-text {
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            margin: 8px 0;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre-wrap;
            word-break: break-word;
            border-left: 4px solid #3182ce;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .ai-message-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            flex-wrap: wrap;
        }
        
        .ai-message-actions button {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background-color: #3182ce;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2c5aa0;
        }
        
        .btn-success {
            background-color: #38a169;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #2f855a;
        }
        
        .btn-secondary {
            background-color: #e2e8f0;
            color: #4a5568;
            border-color: #cbd5e0;
        }
        
        .btn-secondary:hover {
            background-color: #cbd5e0;
            border-color: #a0aec0;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-style: italic;
            color: #666;
        }
        
        .typing-indicator::before {
            content: "";
            width: 12px;
            height: 12px;
            border: 2px solid #3182ce;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .instructions {
            background: #e6fffa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #38a169;
        }
        
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #2f855a;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI文本生成功能测试</h1>
            <p>测试选中文本生成功能：先在对话框显示，然后提供插入/替换选项</p>
        </div>
        
        <div class="content">
            <div class="editor-section">
                <div class="editor-container">
                    <div class="editor-header">
                        <h3>Markdown编辑器</h3>
                    </div>
                    
                    <div class="instructions">
                        <h3>测试步骤：</h3>
                        <ul>
                            <li>1. 在编辑器中输入或选中一段文本</li>
                            <li>2. 点击下方的文本处理按钮</li>
                            <li>3. 查看AI对话框中的生成结果</li>
                            <li>4. 使用"插入"或"替换"按钮应用结果</li>
                        </ul>
                    </div>
                    
                    <div class="text-processing-buttons">
                        <button onclick="processText('rewrite')">重写</button>
                        <button onclick="processText('expand')">扩写</button>
                        <button onclick="processText('summarize')">总结</button>
                        <button onclick="processText('improve')">改进</button>
                    </div>
                    
                    <textarea id="testEditor" placeholder="在这里输入或粘贴文本，然后选中部分文本进行AI处理...

示例文本：
人工智能是一门综合性的前沿学科，涉及计算机科学、数学、认知科学等多个领域。随着技术的发展，AI在各个行业中的应用越来越广泛。

选中上面的文本，然后点击处理按钮试试看！"></textarea>
                </div>
            </div>
            
            <div class="ai-section">
                <h3>AI对话历史</h3>
                <div class="ai-chat-history" id="aiChatHistory">
                    <div class="ai-message assistant">
                        <div class="ai-message-content">
                            <p>您好！我是AI文本处理助手。</p>
                            <p>请在左侧编辑器中选中文本，然后点击相应的处理按钮。我会在这里显示处理结果，并提供插入/替换选项。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟AI处理结果
        const mockAIResponses = {
            rewrite: (text) => `重写版本：\n\n${text.replace(/。/g, '，并且体现了现代科技的发展趋势。').replace(/，/g, '；同时')}`,
            expand: (text) => `扩写版本：\n\n${text}\n\n具体来说，这个领域不仅包含了深度学习、机器学习等核心技术，还涉及到自然语言处理、计算机视觉、语音识别等多个分支。在实际应用中，我们可以看到AI技术在医疗诊断、自动驾驶、智能推荐系统等方面都有重要贡献。`,
            summarize: (text) => `总结：\n\n• 人工智能是综合性前沿学科\n• 涉及多个学科领域\n• 应用范围不断扩大\n• 技术发展迅速`,
            improve: (text) => `改进版本：\n\n人工智能作为一门综合性的前沿学科，融合了计算机科学、数学、认知科学等多个学科领域的知识。伴随着技术的不断进步，AI在各个行业中的应用正变得越来越广泛和深入。`
        };
        
        // 全局变量存储处理数据
        window.currentTextProcessingData = null;
        
        function processText(action) {
            const editor = document.getElementById('testEditor');
            const aiChatHistory = document.getElementById('aiChatHistory');
            
            const selectedText = editor.value.substring(editor.selectionStart, editor.selectionEnd);
            let textToProcess = selectedText;
            let isFullDocument = false;
            
            if (!selectedText || selectedText.trim() === '') {
                textToProcess = editor.value;
                isFullDocument = true;
                
                if (!textToProcess || textToProcess.trim() === '') {
                    alert('请先输入或选中文本');
                    return;
                }
            }
            
            // 保存选中文本的位置信息
            const selectionStart = editor.selectionStart;
            const selectionEnd = editor.selectionEnd;
            
            // 添加用户请求消息
            const actionNames = {
                rewrite: '重写',
                expand: '扩写',
                summarize: '总结',
                improve: '改进'
            };
            
            const userMessage = document.createElement('div');
            userMessage.className = 'ai-message user';
            userMessage.innerHTML = `
                <div class="ai-message-content">
                    <strong>请${actionNames[action]}以下内容：</strong>
                    <div class="ai-message-preview">${isFullDocument ? '整个文档内容' : escapeHtml(textToProcess.substring(0, 200) + (textToProcess.length > 200 ? '...' : ''))}</div>
                </div>
            `;
            aiChatHistory.appendChild(userMessage);
            
            // 添加AI回复容器
            const aiReply = document.createElement('div');
            aiReply.className = 'ai-message assistant';
            aiReply.innerHTML = '<div class="typing-indicator">AI正在处理中...</div>';
            aiChatHistory.appendChild(aiReply);
            aiChatHistory.scrollTop = aiChatHistory.scrollHeight;
            
            // 模拟AI处理延迟
            setTimeout(() => {
                const processedText = mockAIResponses[action](textToProcess);
                
                // 显示生成的内容
                aiReply.innerHTML = `
                    <div class="ai-message-content">
                        <div class="ai-processed-text">${escapeHtml(processedText)}</div>
                    </div>
                `;
                
                // 创建操作按钮数据
                const buttonData = {
                    originalText: textToProcess,
                    processedText: processedText,
                    action: action,
                    isFullDocument: isFullDocument,
                    selectionStart: selectionStart,
                    selectionEnd: selectionEnd
                };
                
                // 存储按钮数据
                window.currentTextProcessingData = buttonData;
                
                // 添加操作按钮
                const actionButtons = document.createElement('div');
                actionButtons.className = 'ai-message-actions';
                actionButtons.innerHTML = `
                    <button class="btn-secondary" onclick="copyProcessedText()" title="复制生成的内容">
                        📋 复制
                    </button>
                    <button class="btn-primary" onclick="insertProcessedText()" title="插入到光标位置">
                        ➕ 插入
                    </button>
                    ${!isFullDocument ? `
                        <button class="btn-success" onclick="replaceProcessedText()" title="替换选中的文本">
                            🔄 替换
                        </button>
                    ` : `
                        <button class="btn-success" onclick="replaceProcessedText()" title="替换整个文档">
                            🔄 替换文档
                        </button>
                    `}
                    <button class="btn-secondary" onclick="regenerateProcessedText()" title="重新生成">
                        🔄 重新生成
                    </button>
                `;
                
                aiReply.appendChild(actionButtons);
                aiChatHistory.scrollTop = aiChatHistory.scrollHeight;
            }, 1500);
        }
        
        function copyProcessedText() {
            if (!window.currentTextProcessingData) return;
            
            const text = window.currentTextProcessingData.processedText;
            navigator.clipboard.writeText(text).then(() => {
                alert('已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败');
            });
        }
        
        function insertProcessedText() {
            if (!window.currentTextProcessingData) return;
            
            const editor = document.getElementById('testEditor');
            const data = window.currentTextProcessingData;
            
            const insertPosition = editor.selectionStart;
            const currentValue = editor.value;
            const beforeText = currentValue.substring(0, insertPosition);
            const afterText = currentValue.substring(insertPosition);
            
            // 添加适当的换行符
            let prefix = '';
            let suffix = '';
            
            if (beforeText && !beforeText.endsWith('\n')) {
                prefix = '\n\n';
            } else if (beforeText && !beforeText.endsWith('\n\n')) {
                prefix = '\n';
            }
            
            if (afterText && !afterText.startsWith('\n')) {
                suffix = '\n\n';
            } else if (afterText && !afterText.startsWith('\n\n')) {
                suffix = '\n';
            }
            
            const fullInsertText = prefix + data.processedText + suffix;
            const newValue = beforeText + fullInsertText + afterText;
            
            editor.value = newValue;
            editor.selectionStart = editor.selectionEnd = insertPosition + fullInsertText.length;
            editor.focus();
            
            alert('已插入生成的内容');
        }
        
        function replaceProcessedText() {
            if (!window.currentTextProcessingData) return;
            
            const editor = document.getElementById('testEditor');
            const data = window.currentTextProcessingData;
            
            if (data.isFullDocument) {
                // 替换整个文档内容
                editor.value = data.processedText;
                editor.selectionStart = 0;
                editor.selectionEnd = 0;
                alert('已替换整个文档');
            } else {
                // 替换选中的文本
                const currentValue = editor.value;
                const beforeText = currentValue.substring(0, data.selectionStart);
                const afterText = currentValue.substring(data.selectionEnd);
                
                const newValue = beforeText + data.processedText + afterText;
                editor.value = newValue;
                
                // 选中新插入的文本
                editor.selectionStart = data.selectionStart;
                editor.selectionEnd = data.selectionStart + data.processedText.length;
                
                alert('已替换选中的文本');
            }
            
            editor.focus();
        }
        
        function regenerateProcessedText() {
            if (!window.currentTextProcessingData) return;
            
            const data = window.currentTextProcessingData;
            processText(data.action);
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html> 