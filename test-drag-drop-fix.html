<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 20px;
        }
        
        .materials-panel {
            width: 300px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .editor-panel {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .material-item {
            padding: 12px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: grab;
            transition: all 0.2s ease;
            border: 1px solid #e0e6ed;
        }
        
        .material-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .material-item.dragging {
            opacity: 0.5;
            transform: scale(0.95);
            cursor: grabbing;
        }
        
        .material-type {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .material-content {
            font-size: 14px;
            color: #333;
        }
        
        #markdownEditor {
            width: 100%;
            height: 400px;
            border: 1px solid #e0e6ed;
            border-radius: 6px;
            padding: 16px;
            font-family: 'SF Mono', Monaco, Consolas, monospace;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            outline: none;
        }
        
        #markdownEditor.drag-over {
            background-color: #f0f8ff;
            border: 2px dashed #3182ce;
        }
        
        .markdown-insertion-indicator {
            position: fixed;
            width: 2px;
            height: 20px;
            background-color: #007acc;
            z-index: 1000;
            display: none;
            pointer-events: none;
            border-radius: 1px;
            box-shadow: 0 0 4px rgba(0, 122, 204, 0.5);
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 4px;
            font-size: 12px;
            color: #2d5a2d;
        }
        
        .drag-preview {
            position: absolute;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 8px 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-width: 200px;
            z-index: 1000;
            font-size: 12px;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="materials-panel">
            <h3>素材库</h3>
            <div id="materialsList">
                <div class="material-item" draggable="true" data-id="1" data-type="text">
                    <div class="material-type">📝 文本</div>
                    <div class="material-content">这是一段测试文本内容</div>
                </div>
                <div class="material-item" draggable="true" data-id="2" data-type="link">
                    <div class="material-type">🔗 链接</div>
                    <div class="material-content">https://example.com</div>
                </div>
                <div class="material-item" draggable="true" data-id="3" data-type="image">
                    <div class="material-type">🖼️ 图片</div>
                    <div class="material-content">https://via.placeholder.com/300x200</div>
                </div>
                <div class="material-item" draggable="true" data-id="4" data-type="code">
                    <div class="material-type">💻 代码</div>
                    <div class="material-content">console.log('Hello World');</div>
                </div>
            </div>
        </div>
        
        <div class="editor-panel">
            <h3>Markdown编辑器</h3>
            <textarea id="markdownEditor" placeholder="拖拽左侧素材到这里..."></textarea>
            <div class="status" id="status">
                准备就绪，可以开始拖拽测试
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="src/ui/scripts/core.js"></script>
    <script src="src/ui/scripts/markdown-editor.js"></script>
    
    <script>
        // 模拟主应用实例
        window.organizationApp = {
            materials: [
                { id: '1', type: 'text', content: '这是一段测试文本内容' },
                { id: '2', type: 'link', content: 'https://example.com', title: '示例链接' },
                { id: '3', type: 'image', content: 'https://via.placeholder.com/300x200', title: '测试图片' },
                { id: '4', type: 'code', content: 'console.log(\'Hello World\');' }
            ]
        };
        
        // 初始化编辑器
        const editor = document.getElementById('markdownEditor');
        const markdownEditor = new MarkdownEditor(editor, null, {
            enableDragDrop: true,
            enableRealtimeCursor: true,
            enableInsertionIndicator: true
        });
        
        // 绑定拖拽事件到素材项
        document.querySelectorAll('.material-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                const materialId = item.dataset.id;
                const material = window.organizationApp.materials.find(m => m.id === materialId);
                
                if (material) {
                    // 设置拖拽数据
                    e.dataTransfer.setData('text/plain', materialId);
                    e.dataTransfer.setData('application/x-material', JSON.stringify(material));
                    e.dataTransfer.effectAllowed = 'copy';
                    
                    // 添加拖拽样式
                    item.classList.add('dragging');
                    
                    // 更新状态
                    document.getElementById('status').textContent = `开始拖拽: ${material.type} - ${material.content.substring(0, 30)}...`;
                }
            });
            
            item.addEventListener('dragend', () => {
                item.classList.remove('dragging');
            });
        });
        
        // 监听素材插入事件
        editor.addEventListener('materialInserted', (e) => {
            const { content, position } = e.detail;
            document.getElementById('status').textContent = `素材插入成功: ${content.substring(0, 30)}... (位置: ${position})`;
            
            setTimeout(() => {
                document.getElementById('status').textContent = '准备就绪，可以继续拖拽测试';
            }, 3000);
        });
        
        // 添加调试信息
        console.log('测试环境初始化完成');
        console.log('主应用实例:', window.organizationApp);
        console.log('MarkdownEditor实例:', markdownEditor);
    </script>
</body>
</html> 