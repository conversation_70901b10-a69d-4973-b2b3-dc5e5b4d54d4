// 拖拽调试脚本
// 在浏览器控制台中运行此脚本来诊断拖拽问题

console.log('=== 拖拽功能调试 ===');

// 1. 检查主应用实例
console.log('1. 检查主应用实例:');
console.log('window.organizationApp:', window.organizationApp);
console.log('素材数量:', window.organizationApp?.materials?.length || 0);

// 2. 检查MarkdownEditor实例
console.log('\n2. 检查MarkdownEditor实例:');
console.log('organizationApp.markdownEditor:', window.organizationApp?.markdownEditor);
console.log('textarea:', window.organizationApp?.textarea);

// 3. 检查素材项的拖拽属性
console.log('\n3. 检查素材项:');
const materialItems = document.querySelectorAll('.material-item');
console.log('素材项数量:', materialItems.length);
materialItems.forEach((item, index) => {
    console.log(`素材项 ${index}:`, {
        id: item.dataset.id,
        type: item.dataset.type,
        draggable: item.draggable,
        hasEventListeners: item.ondragstart !== null
    });
});

// 4. 检查编辑器容器
console.log('\n4. 检查编辑器容器:');
const editorContainer = document.getElementById('editorContent');
console.log('编辑器容器:', editorContainer);
console.log('编辑器textarea:', document.getElementById('markdownTextarea'));

// 5. 测试拖拽事件绑定
console.log('\n5. 测试拖拽事件绑定:');
if (materialItems.length > 0) {
    const firstItem = materialItems[0];
    console.log('第一个素材项的事件监听器:', {
        dragstart: firstItem.ondragstart,
        dragend: firstItem.ondragend
    });
}

// 6. 检查CSS样式
console.log('\n6. 检查CSS样式:');
const styles = document.querySelector('link[href*="organization.css"]');
console.log('CSS样式文件:', styles?.href);

// 7. 提供手动测试函数
window.testDragDrop = function() {
    console.log('\n=== 手动测试拖拽功能 ===');
    
    if (!window.organizationApp) {
        console.error('主应用未初始化');
        return;
    }
    
    const materials = window.organizationApp.materials;
    if (materials.length === 0) {
        console.error('没有素材可以测试');
        return;
    }
    
    const testMaterial = materials[0];
    console.log('测试素材:', testMaterial);
    
    // 模拟拖拽数据
    const mockDataTransfer = {
        data: {},
        setData: function(type, data) {
            this.data[type] = data;
            console.log(`设置拖拽数据 ${type}:`, data);
        },
        getData: function(type) {
            return this.data[type];
        }
    };
    
    // 模拟dragstart事件
    const mockDragStartEvent = {
        dataTransfer: mockDataTransfer,
        target: document.querySelector('.material-item'),
        preventDefault: () => {}
    };
    
    console.log('模拟dragstart事件...');
    window.organizationApp.handleDragStart(mockDragStartEvent, testMaterial.id);
    
    // 检查设置的数据
    console.log('拖拽数据:', mockDataTransfer.data);
    
    // 模拟drop事件
    const mockDropEvent = {
        dataTransfer: mockDataTransfer,
        preventDefault: () => {},
        clientX: 100,
        clientY: 100
    };
    
    console.log('模拟drop事件...');
    if (window.organizationApp.markdownEditor) {
        window.organizationApp.markdownEditor.handleDrop(mockDropEvent);
    }
};

console.log('\n=== 调试完成 ===');
console.log('运行 testDragDrop() 来手动测试拖拽功能'); 