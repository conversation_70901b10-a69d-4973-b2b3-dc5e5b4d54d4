// 检查所有数据库文件的内容
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPaths = [
    '/Users/<USER>/Library/Application Support/qnotes/qnotes.db',
    '/Users/<USER>/Library/Application Support/Electron/qnotes.db',
    '/Users/<USER>/Dropbox/code/claude/qnotes/data/qnotes.db'
];

async function checkDatabase(dbPath) {
    return new Promise((resolve, reject) => {
        console.log(`\n=== 检查数据库: ${dbPath} ===`);
        
        const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
            if (err) {
                console.log(`❌ 无法打开数据库: ${err.message}`);
                resolve({ path: dbPath, error: err.message });
                return;
            }
            
            // 查询收藏数量
            db.get('SELECT COUNT(*) as count FROM collections', (err, row) => {
                if (err) {
                    console.log(`❌ 查询失败: ${err.message}`);
                    db.close();
                    resolve({ path: dbPath, error: err.message });
                    return;
                }
                
                const count = row.count;
                console.log(`✓ 收藏数量: ${count}`);
                
                if (count > 0) {
                    // 获取前几个收藏的详细信息
                    db.all('SELECT id, type, content, category, created_at FROM collections ORDER BY created_at DESC LIMIT 5', (err, rows) => {
                        if (err) {
                            console.log(`❌ 获取详细信息失败: ${err.message}`);
                        } else {
                            console.log('前5个收藏:');
                            rows.forEach((row, index) => {
                                console.log(`  ${index + 1}. ${row.type} - ${row.content ? row.content.substring(0, 50) + '...' : 'null'}`);
                            });
                        }
                        
                        db.close();
                        resolve({ path: dbPath, count, samples: rows });
                    });
                } else {
                    db.close();
                    resolve({ path: dbPath, count });
                }
            });
        });
    });
}

async function checkAllDatabases() {
    console.log('检查所有数据库文件...\n');
    
    for (const dbPath of dbPaths) {
        try {
            const result = await checkDatabase(dbPath);
            if (result.count > 0) {
                console.log(`🎯 发现数据: ${result.path} (${result.count} 个收藏)`);
            }
        } catch (error) {
            console.log(`❌ 检查失败: ${dbPath} - ${error.message}`);
        }
    }
    
    console.log('\n检查完成');
}

checkAllDatabases();