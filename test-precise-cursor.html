<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精确光标定位测试 - QNotes</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .sidebar {
            width: 300px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .editor-area {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .controls {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .control-group {
            margin-bottom: 10px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .control-group input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .material-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: grab;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .material-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .material-item.dragging {
            opacity: 0.5;
            transform: scale(0.95);
            cursor: grabbing;
        }
        
        .material-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }
        
        .material-content {
            color: #666;
            font-size: 14px;
        }
        
        .material-type {
            display: inline-block;
            background: #007acc;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-bottom: 8px;
        }
        
        #editor {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            background: #fafafa;
            transition: background-color 0.2s ease;
        }
        
        #editor.drag-over {
            background-color: rgba(0, 122, 204, 0.05);
            border-color: #007acc;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #e8f4f8;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
            font-family: monospace;
        }
        
        .instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #155724;
        }
        
        .instructions ul {
            margin-bottom: 0;
        }
        
        .instructions li {
            margin-bottom: 5px;
        }
        
        .test-text {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h2>测试控制</h2>
            
            <div class="instructions">
                <h3>测试说明</h3>
                <ul>
                    <li>拖拽左侧素材到右侧编辑器</li>
                    <li>观察蓝色光标指示器的精确定位</li>
                    <li>启用"实时光标跟随"查看光标移动</li>
                    <li>尝试在不同位置（行首、行中、行尾）插入</li>
                    <li>测试多行文本的精确定位</li>
                </ul>
            </div>
            
            <div class="controls">
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="enableRealtimeCursor" checked>
                        启用实时光标跟随
                    </label>
                </div>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="enableInsertionIndicator" checked>
                        显示插入指示器
                    </label>
                </div>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="enableDebugMode">
                        调试模式
                    </label>
                </div>
            </div>
            
            <h3>测试素材</h3>
            
            <div class="material-item" draggable="true" data-type="text" data-content="这是一段测试文本内容">
                <div class="material-type">文本</div>
                <div class="material-title">测试文本</div>
                <div class="material-content">这是一段测试文本内容</div>
            </div>
            
            <div class="material-item" draggable="true" data-type="link" data-content="https://example.com" data-title="示例链接">
                <div class="material-type">链接</div>
                <div class="material-title">示例链接</div>
                <div class="material-content">https://example.com</div>
            </div>
            
            <div class="material-item" draggable="true" data-type="code" data-content="function hello() {\n    console.log('Hello, World!');\n}">
                <div class="material-type">代码</div>
                <div class="material-title">JavaScript函数</div>
                <div class="material-content">function hello() { ... }</div>
            </div>
            
            <div class="material-item" draggable="true" data-type="text" data-content="这是一个很长的文本段落，用来测试在不同位置插入时的光标定位精确性。它包含了多个单词和标点符号。">
                <div class="material-type">文本</div>
                <div class="material-title">长文本测试</div>
                <div class="material-content">这是一个很长的文本段落...</div>
            </div>
            
            <div class="material-item" draggable="true" data-type="text" data-content="第一行\n第二行\n第三行">
                <div class="material-type">文本</div>
                <div class="material-title">多行文本</div>
                <div class="material-content">第一行<br>第二行<br>第三行</div>
            </div>
            
            <div class="status" id="status">
                状态：准备就绪
            </div>
            
            <div class="debug-info" id="debugInfo" style="display: none;">
                调试信息将在这里显示
            </div>
        </div>
        
        <div class="editor-area">
            <h2>Markdown 编辑器</h2>
            <textarea id="editor" placeholder="在这里编写Markdown内容，或者从左侧拖拽素材到这里...

# 示例文档

这是一个测试文档，用来演示精确的光标定位功能。

## 测试说明

1. 从左侧拖拽素材到编辑器的不同位置
2. 观察蓝色光标指示器的精确定位
3. 查看素材是否在正确的位置插入

## 多行测试

这里有多行文本
用来测试垂直定位
的精确性

## 代码测试

```javascript
// 这里可以插入代码
```

## 结束

测试完成后，请检查所有功能是否正常工作。"></textarea>
            
            <div class="test-text" id="testText">
                点击位置信息将在这里显示
            </div>
        </div>
    </div>

    <script src="src/ui/scripts/markdown-editor.js"></script>
    <script>
        // 初始化编辑器
        const editor = document.getElementById('editor');
        const status = document.getElementById('status');
        const debugInfo = document.getElementById('debugInfo');
        const testText = document.getElementById('testText');
        
        // 配置选项
        let editorOptions = {
            enableRealtimeCursor: true,
            enableInsertionIndicator: true,
            debounceDelay: 16
        };
        
        // 创建MarkdownEditor实例
        let markdownEditor = new MarkdownEditor(editor, null, editorOptions);
        
        // 控制选项
        document.getElementById('enableRealtimeCursor').addEventListener('change', (e) => {
            markdownEditor.enableRealtimeCursor = e.target.checked;
            updateStatus('实时光标跟随: ' + (e.target.checked ? '启用' : '禁用'));
        });
        
        document.getElementById('enableInsertionIndicator').addEventListener('change', (e) => {
            markdownEditor.enableInsertionIndicator = e.target.checked;
            updateStatus('插入指示器: ' + (e.target.checked ? '启用' : '禁用'));
        });
        
        document.getElementById('enableDebugMode').addEventListener('change', (e) => {
            debugInfo.style.display = e.target.checked ? 'block' : 'none';
            updateStatus('调试模式: ' + (e.target.checked ? '启用' : '禁用'));
        });
        
        // 绑定素材拖拽事件
        document.querySelectorAll('.material-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                const type = item.dataset.type;
                const content = item.dataset.content;
                const title = item.dataset.title || item.querySelector('.material-title').textContent;
                
                const materialData = {
                    type: type,
                    content: content,
                    title: title
                };
                
                e.dataTransfer.setData('application/x-material', JSON.stringify(materialData));
                e.dataTransfer.setData('text/plain', content);
                e.dataTransfer.effectAllowed = 'copy';
                
                item.classList.add('dragging');
                updateStatus(`开始拖拽: ${title}`);
            });
            
            item.addEventListener('dragend', (e) => {
                item.classList.remove('dragging');
                updateStatus('拖拽结束');
            });
        });
        
        // 监听编辑器事件
        editor.addEventListener('materialInserted', (e) => {
            updateStatus(`素材已插入到位置 ${e.detail.position}`);
        });
        
        // 鼠标移动事件（用于调试）
        editor.addEventListener('mousemove', (e) => {
            if (document.getElementById('enableDebugMode').checked) {
                const rect = editor.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // 计算光标位置
                const position = markdownEditor.calculatePreciseCursorPosition(e);
                
                debugInfo.textContent = `鼠标位置: (${x.toFixed(0)}, ${y.toFixed(0)}) | 计算位置: ${position}`;
            }
        });
        
        // 点击事件（显示点击位置信息）
        editor.addEventListener('click', (e) => {
            const rect = editor.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            const position = markdownEditor.calculatePreciseCursorPosition(e);
            const actualPosition = editor.selectionStart;
            
            testText.textContent = `点击位置: (${x.toFixed(0)}, ${y.toFixed(0)}) | 计算位置: ${position} | 实际位置: ${actualPosition} | 差异: ${Math.abs(position - actualPosition)}`;
        });
        
        function updateStatus(message) {
            status.textContent = `状态：${message}`;
            console.log(message);
        }
        
        // 初始化完成
        updateStatus('编辑器初始化完成，可以开始测试');
    </script>
</body>
</html> 