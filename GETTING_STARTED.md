# QNotes 快速上手指南

## 🚀 立即开始

### 1. 启动应用
```bash
# 开发模式启动（推荐）
npm run dev

# 或者使用启动脚本
node scripts/start.js

# 生产模式启动
npm start
```

### 2. 首次使用
应用启动后会默认进入**收集模式**：
- 系统托盘会出现 QNotes 图标 📚
- 没有主窗口，保持桌面整洁
- 已准备接收你的第一个收藏

## 📝 基本操作流程

### 收集内容 (收集模式)
1. **快速收藏**：
   - 按 `Cmd/Ctrl + N` 打开快速收藏窗口
   - 输入或粘贴要收藏的内容
   - 点击"保存"完成收藏

2. **拖拽收藏**：
   - 从任何应用拖拽文本到桌面
   - 弹出确认窗口
   - 点击"保存"完成收藏

3. **查看托盘**：
   - 右键点击托盘图标查看菜单
   - 显示当前收藏数量
   - 可以快速切换模式

### 整理内容 (整理模式)
1. **切换模式**：
   - 按 `Cmd/Ctrl + 2` 切换到整理模式
   - 或点击托盘菜单 → 整理模式

2. **查看收藏**：
   - 左侧边栏显示所有收藏内容
   - 可以按类型、分类筛选
   - 支持搜索功能

3. **AI 分析**：
   - 选择要分析的收藏（可多选）
   - 点击 "AI 分析" 按钮
   - 等待 AI 完成智能分类

4. **生成文档**：
   - AI 分析完成后，点击 "生成" 按钮
   - 自动生成结构化 Markdown 文档
   - 在右侧编辑器中查看和编辑

5. **编辑和导出**：
   - 使用 Markdown 编辑器完善文档
   - 支持实时预览 (`Cmd/Ctrl + P`)
   - 导出为多种格式 (`Cmd/Ctrl + E`)

## 💡 实用技巧

### 快捷键掌握
| 快捷键 | 功能 | 适用模式 |
|--------|------|----------|
| `Cmd/Ctrl + 1` | 切换到收集模式 | 全局 |
| `Cmd/Ctrl + 2` | 切换到整理模式 | 全局 |
| `Cmd/Ctrl + N` | 快速收藏/新建文档 | 全局 |
| `Cmd/Ctrl + A` | AI 分析 | 整理模式 |
| `Cmd/Ctrl + F` | 搜索 | 整理模式 |
| `Cmd/Ctrl + S` | 保存文档 | 整理模式 |
| `Cmd/Ctrl + E` | 导出 | 整理模式 |

### 编辑器技巧
- **加粗**：`Cmd/Ctrl + B` 或输入 `**文字**`
- **斜体**：`Cmd/Ctrl + I` 或输入 `*文字*`
- **链接**：`Cmd/Ctrl + K` 或输入 `[文字](链接)`
- **标题**：输入 `# 标题` (1-6个#)
- **列表**：输入 `- 项目` 或 `1. 项目`
- **代码**：输入 `` `代码` `` 或 ```代码块```

### 内容组织
1. **分类收藏**：
   - 在收藏时添加分类标签
   - 使用筛选器快速查找
   - AI 会自动学习你的分类习惯

2. **关键词管理**：
   - AI 自动提取关键词
   - 可以手动编辑和添加
   - 支持关键词搜索

3. **批量操作**：
   - 选择多个收藏进行批量分析
   - 批量编辑分类和标签
   - 批量导出选择的内容

## 🔧 配置和定制

### AI 配置
```javascript
// 在整理模式 → 设置 → AI 设置中配置
{
  "apiUrl": "https://api.tu-zi.com/v1/chat/completions",
  "apiKey": "你的API密钥",
  "model": "claude-sonnet-4-20250514",
  "temperature": 0.7
}
```

### 界面定制
- **主题**：支持浅色/深色主题
- **字体**：可调整编辑器字体大小
- **布局**：可调整侧边栏宽度
- **快捷键**：支持自定义快捷键

### 数据管理
- **自动备份**：默认每24小时备份
- **数据导出**：支持 JSON/CSV/Markdown 格式
- **数据清理**：自动清理30天前的未分类内容

## 🎯 最佳实践

### 收集习惯
1. **及时收藏**：看到有价值内容立即收藏
2. **添加来源**：记录内容来源便于后续查找
3. **简单分类**：收藏时简单分类，后续 AI 会完善

### 整理工作流
1. **定期整理**：建议每周进行一次 AI 分析
2. **主题整理**：按项目或主题分批处理
3. **文档沉淀**：将有价值的分析结果整理成文档

### 团队协作
1. **导出分享**：使用 Markdown 格式便于分享
2. **标准化**：建立团队的分类和标签规范
3. **备份同步**：定期导出数据进行备份

## 🚨 常见问题

### Q: AI 分析失败怎么办？
A: 检查网络连接和 API 密钥配置，确保有足够的 API 额度。

### Q: 拖拽功能不工作？
A: 确保应用有足够的系统权限，在 macOS 上可能需要辅助功能权限。

### Q: 数据存储在哪里？
A: 数据库文件在 `~/Library/Application Support/QNotes/` (macOS)

### Q: 如何备份数据？
A: 在整理模式中选择 "工具 → 导出数据" 进行完整备份。

### Q: 支持哪些文件格式？
A: 收藏支持文本、链接、图片；导出支持 Markdown、HTML、PDF、JSON。

## 📞 获取帮助

- **使用指南**：查看完整的 README.md
- **技术文档**：查看 src/ 目录下的代码注释
- **问题反馈**：通过 GitHub Issues 报告问题
- **功能建议**：欢迎提交 Pull Request

---

🎉 **开始你的智能收藏之旅吧！**

记住：好的工具配合好的习惯，才能发挥最大价值。建议从简单的文本收藏开始，逐步探索 AI 分析和文档生成功能。