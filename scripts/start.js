#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class AppStarter {
    constructor() {
        this.projectRoot = path.join(__dirname, '..');
    }

    async start() {
        console.log('🚀 启动 QNotes 应用...\n');

        // 检查环境
        if (!this.checkEnvironment()) {
            return;
        }

        // 检查依赖
        if (!this.checkDependencies()) {
            console.log('📦 正在安装依赖...');
            await this.installDependencies();
        }

        // 启动应用
        this.launchApp();
    }

    checkEnvironment() {
        console.log('🔍 检查环境...');

        // 检查 Node.js 版本
        const nodeVersion = process.version;
        console.log(`  ✅ Node.js 版本: ${nodeVersion}`);

        // 检查 npm
        try {
            const { execSync } = require('child_process');
            const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
            console.log(`  ✅ npm 版本: ${npmVersion}`);
        } catch (error) {
            console.log('  ❌ npm 未找到');
            return false;
        }

        // 检查项目结构
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        if (!fs.existsSync(packageJsonPath)) {
            console.log('  ❌ package.json 未找到');
            return false;
        }
        console.log('  ✅ 项目结构正常');

        return true;
    }

    checkDependencies() {
        const nodeModulesPath = path.join(this.projectRoot, 'node_modules');
        const electronPath = path.join(nodeModulesPath, 'electron');
        
        return fs.existsSync(nodeModulesPath) && fs.existsSync(electronPath);
    }

    async installDependencies() {
        return new Promise((resolve, reject) => {
            const npm = spawn('npm', ['install'], {
                cwd: this.projectRoot,
                stdio: 'inherit',
                shell: true
            });

            npm.on('close', (code) => {
                if (code === 0) {
                    console.log('  ✅ 依赖安装完成');
                    resolve();
                } else {
                    console.log('  ❌ 依赖安装失败');
                    reject(new Error(`npm install 失败，退出码: ${code}`));
                }
            });

            npm.on('error', (error) => {
                console.log('  ❌ 启动 npm 失败:', error.message);
                reject(error);
            });
        });
    }

    launchApp() {
        console.log('\n🎯 启动应用...');

        const electron = spawn('npm', ['start'], {
            cwd: this.projectRoot,
            stdio: 'inherit',
            shell: true
        });

        electron.on('close', (code) => {
            console.log(`\n📊 应用已退出，退出码: ${code}`);
        });

        electron.on('error', (error) => {
            console.log('❌ 启动应用失败:', error.message);
            console.log('\n💡 可能的解决方案:');
            console.log('  1. 确保已安装所有依赖: npm install');
            console.log('  2. 检查 Node.js 版本是否兼容');
            console.log('  3. 尝试清除缓存: npm cache clean --force');
            console.log('  4. 删除 node_modules 后重新安装');
        });

        // 处理进程信号
        process.on('SIGINT', () => {
            console.log('\n⏹️  正在关闭应用...');
            electron.kill('SIGINT');
        });

        process.on('SIGTERM', () => {
            console.log('\n⏹️  正在关闭应用...');
            electron.kill('SIGTERM');
        });
    }

    showUsage() {
        console.log(`
📚 QNotes 启动脚本

用法:
  node scripts/start.js        启动应用
  node scripts/test-app.js     运行测试

选项:
  --help                       显示帮助信息
  --dev                        开发模式启动

环境变量:
  NODE_ENV=development         开发模式
  NODE_ENV=production          生产模式

示例:
  # 正常启动
  node scripts/start.js

  # 开发模式
  NODE_ENV=development node scripts/start.js

  # 运行测试
  node scripts/test-app.js
        `);
    }
}

// 检查命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    const starter = new AppStarter();
    starter.showUsage();
    process.exit(0);
}

// 主入口
if (require.main === module) {
    const starter = new AppStarter();
    starter.start().catch(error => {
        console.error('❌ 启动失败:', error.message);
        process.exit(1);
    });
}

module.exports = AppStarter;