// 测试脚本 - 验证应用的基本功能
const path = require('path');
const fs = require('fs');

class AppTester {
    constructor() {
        this.testResults = [];
        this.projectRoot = path.join(__dirname, '..');
    }

    async runTests() {
        console.log('🔍 开始测试 QNotes 应用...\n');

        // 测试项目结构
        await this.testProjectStructure();
        
        // 测试依赖
        await this.testDependencies();
        
        // 测试配置文件
        await this.testConfiguration();
        
        // 测试核心模块
        await this.testCoreModules();
        
        // 输出测试结果
        this.outputResults();
    }

    async testProjectStructure() {
        console.log('📁 测试项目结构...');
        
        const requiredFiles = [
            'package.json',
            'src/main.js',
            'src/controllers/AppController.js',
            'src/modes/CollectionMode.js',
            'src/modes/OrganizationMode.js',
            'src/services/AIService.js',
            'src/services/Settings.js',
            'src/database/Database.js',
            'src/utils/IPCManager.js',
            'src/ui/organization.html',
            'src/ui/drag-popup.html',
            'src/ui/quick-capture.html',
            'src/ui/styles/organization.css',
            'src/ui/scripts/organization.js'
        ];

        const requiredDirs = [
            'src',
            'src/controllers',
            'src/modes',
            'src/services',
            'src/database',
            'src/utils',
            'src/ui',
            'src/ui/styles',
            'src/ui/scripts'
        ];

        // 检查目录
        for (const dir of requiredDirs) {
            const dirPath = path.join(this.projectRoot, dir);
            if (fs.existsSync(dirPath)) {
                this.addTest('项目结构', `目录 ${dir}`, 'PASS', '目录存在');
            } else {
                this.addTest('项目结构', `目录 ${dir}`, 'FAIL', '目录不存在');
            }
        }

        // 检查必需文件
        for (const file of requiredFiles) {
            const filePath = path.join(this.projectRoot, file);
            if (fs.existsSync(filePath)) {
                this.addTest('项目结构', `文件 ${file}`, 'PASS', '文件存在');
            } else {
                this.addTest('项目结构', `文件 ${file}`, 'FAIL', '文件不存在');
            }
        }
    }

    async testDependencies() {
        console.log('📦 测试依赖配置...');
        
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        
        try {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            // 检查必需依赖
            const requiredDeps = [
                'electron',
                'sqlite3',
                'axios',
                'uuid',
                'lodash',
                'electron-store'
            ];

            const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };

            for (const dep of requiredDeps) {
                if (allDeps[dep]) {
                    this.addTest('依赖', dep, 'PASS', `版本: ${allDeps[dep]}`);
                } else {
                    this.addTest('依赖', dep, 'FAIL', '依赖缺失');
                }
            }

            // 检查脚本命令
            const requiredScripts = ['start', 'dev', 'build'];
            for (const script of requiredScripts) {
                if (packageJson.scripts && packageJson.scripts[script]) {
                    this.addTest('脚本', script, 'PASS', packageJson.scripts[script]);
                } else {
                    this.addTest('脚本', script, 'FAIL', '脚本缺失');
                }
            }

        } catch (error) {
            this.addTest('依赖', 'package.json', 'FAIL', `解析失败: ${error.message}`);
        }
    }

    async testConfiguration() {
        console.log('⚙️ 测试配置文件...');
        
        // 测试主要配置类
        try {
            const SettingsPath = path.join(this.projectRoot, 'src/services/Settings.js');
            const settingsContent = fs.readFileSync(SettingsPath, 'utf8');
            
            if (settingsContent.includes('class Settings')) {
                this.addTest('配置', 'Settings类', 'PASS', '设置类定义正确');
            } else {
                this.addTest('配置', 'Settings类', 'FAIL', '设置类定义有误');
            }

            // 检查默认配置
            if (settingsContent.includes('defaults:')) {
                this.addTest('配置', '默认配置', 'PASS', '包含默认配置');
            } else {
                this.addTest('配置', '默认配置', 'FAIL', '缺少默认配置');
            }

        } catch (error) {
            this.addTest('配置', 'Settings文件', 'FAIL', `读取失败: ${error.message}`);
        }
    }

    async testCoreModules() {
        console.log('🔧 测试核心模块...');
        
        const modules = [
            { name: 'AppController', path: 'src/controllers/AppController.js', class: 'AppController' },
            { name: 'CollectionMode', path: 'src/modes/CollectionMode.js', class: 'CollectionMode' },
            { name: 'OrganizationMode', path: 'src/modes/OrganizationMode.js', class: 'OrganizationMode' },
            { name: 'AIService', path: 'src/services/AIService.js', class: 'AIService' },
            { name: 'Database', path: 'src/database/Database.js', class: 'Database' },
            { name: 'IPCManager', path: 'src/utils/IPCManager.js', class: 'IPCManager' }
        ];

        for (const module of modules) {
            try {
                const modulePath = path.join(this.projectRoot, module.path);
                const moduleContent = fs.readFileSync(modulePath, 'utf8');
                
                // 检查类定义
                if (moduleContent.includes(`class ${module.class}`)) {
                    this.addTest('核心模块', `${module.name} 类定义`, 'PASS', '类定义正确');
                } else {
                    this.addTest('核心模块', `${module.name} 类定义`, 'FAIL', '类定义缺失');
                }
                
                // 检查导出
                if (moduleContent.includes(`module.exports = ${module.class}`)) {
                    this.addTest('核心模块', `${module.name} 导出`, 'PASS', '模块导出正确');
                } else {
                    this.addTest('核心模块', `${module.name} 导出`, 'FAIL', '模块导出缺失');
                }

            } catch (error) {
                this.addTest('核心模块', module.name, 'FAIL', `模块测试失败: ${error.message}`);
            }
        }
    }

    addTest(category, name, status, message) {
        this.testResults.push({
            category,
            name,
            status,
            message,
            timestamp: new Date().toISOString()
        });
    }

    outputResults() {
        console.log('\n📊 测试结果汇总:');
        console.log('='.repeat(80));
        
        const categories = [...new Set(this.testResults.map(r => r.category))];
        let totalPass = 0;
        let totalFail = 0;

        for (const category of categories) {
            console.log(`\n📂 ${category}:`);
            
            const categoryTests = this.testResults.filter(r => r.category === category);
            const passCount = categoryTests.filter(r => r.status === 'PASS').length;
            const failCount = categoryTests.filter(r => r.status === 'FAIL').length;
            
            totalPass += passCount;
            totalFail += failCount;

            for (const test of categoryTests) {
                const icon = test.status === 'PASS' ? '✅' : '❌';
                console.log(`  ${icon} ${test.name}: ${test.message}`);
            }
            
            console.log(`  📈 ${category} 汇总: ${passCount} 通过, ${failCount} 失败`);
        }

        console.log('\n' + '='.repeat(80));
        console.log(`🎯 总体结果: ${totalPass} 通过, ${totalFail} 失败`);
        
        const successRate = Math.round((totalPass / (totalPass + totalFail)) * 100);
        console.log(`📊 成功率: ${successRate}%`);

        if (totalFail === 0) {
            console.log('🎉 所有测试通过！应用已准备就绪。');
        } else {
            console.log('⚠️  存在测试失败项，请检查并修复。');
        }

        // 输出建议
        this.outputRecommendations();
    }

    outputRecommendations() {
        console.log('\n💡 建议和下一步:');
        console.log('='.repeat(80));
        
        const recommendations = [
            '1. 运行 `npm install` 安装所有依赖',
            '2. 配置 AI API 密钥在设置中',
            '3. 运行 `npm run dev` 启动开发模式',
            '4. 测试拖拽功能和快捷键',
            '5. 验证数据库创建和数据存储',
            '6. 测试模式切换功能',
            '7. 验证 AI 分析功能（需要网络）',
            '8. 测试导出功能',
            '9. 运行 `npm run build` 进行生产构建',
            '10. 考虑添加单元测试和集成测试'
        ];

        recommendations.forEach(rec => console.log(`  ${rec}`));

        console.log('\n📚 参考文档:');
        console.log('  - README.md: 完整使用说明');
        console.log('  - package.json: 依赖和脚本配置');
        console.log('  - src/: 源代码结构');
        
        console.log('\n🔗 相关链接:');
        console.log('  - Electron 文档: https://electronjs.org/docs');
        console.log('  - Claude API 文档: https://docs.anthropic.com/');
        console.log('  - SQLite 文档: https://sqlite.org/docs.html');
    }

    async checkNodeModules() {
        const nodeModulesPath = path.join(this.projectRoot, 'node_modules');
        if (fs.existsSync(nodeModulesPath)) {
            console.log('✅ node_modules 目录存在');
            
            // 检查关键依赖
            const keyDeps = ['electron', 'sqlite3', 'axios'];
            for (const dep of keyDeps) {
                const depPath = path.join(nodeModulesPath, dep);
                if (fs.existsSync(depPath)) {
                    console.log(`  ✅ ${dep} 已安装`);
                } else {
                    console.log(`  ❌ ${dep} 未安装`);
                }
            }
        } else {
            console.log('❌ node_modules 目录不存在，请运行 npm install');
        }
    }
}

// 运行测试
if (require.main === module) {
    const tester = new AppTester();
    tester.runTests().then(() => {
        console.log('\n🔍 额外检查:');
        return tester.checkNodeModules();
    }).catch(error => {
        console.error('❌ 测试过程中发生错误:', error);
        process.exit(1);
    });
}

module.exports = AppTester;