<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化测试</title>
</head>
<body>
    <h1>最小化测试</h1>
    <div id="output">测试中...</div>
    
    <script>
        console.log('脚本开始执行');
        
        // 测试是否能加载其他脚本
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        async function testScripts() {
            const output = document.getElementById('output');
            
            try {
                output.innerHTML = '正在测试脚本加载...<br>';
                
                console.log('测试 core.js');
                await loadScript('src/ui/scripts/core.js');
                output.innerHTML += '✅ core.js 加载成功<br>';
                
                console.log('测试 materials-manager.js');
                await loadScript('src/ui/scripts/materials-manager.js');
                output.innerHTML += '✅ materials-manager.js 加载成功<br>';
                
                console.log('测试 app.js');
                await loadScript('src/ui/scripts/app.js');
                output.innerHTML += '✅ app.js 加载成功<br>';
                
                output.innerHTML += '<br>所有脚本加载成功！';
                
            } catch (error) {
                console.error('脚本加载失败:', error);
                output.innerHTML += `❌ 脚本加载失败: ${error.message}<br>`;
            }
        }
        
        testScripts();
    </script>
</body>
</html>
